# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_04_10_170250) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "api_events", force: :cascade do |t|
    t.integer "activity"
    t.string "api_type"
    t.string "class_name"
    t.bigint "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.text "detail"
    t.text "error_detail"
    t.string "error_message"
    t.boolean "first_time", default: false
    t.bigint "integration_id"
    t.integer "resolution_status", default: 0
    t.jsonb "response"
    t.integer "status"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_api_events_on_company_id"
    t.index ["integration_id"], name: "index_api_events_on_integration_id"
  end

  create_table "asset_discovery_logs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.bigint "locationable_id"
    t.string "locationable_type"
    t.jsonb "response"
    t.integer "source"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_asset_discovery_logs_on_company_id"
    t.index ["locationable_type", "locationable_id"], name: "index_asset_discovery_logs_on_location"
  end

  create_table "email_logs", force: :cascade do |t|
    t.text "body"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.integer "email_type", default: 0
    t.text "error_message"
    t.text "receiver_emails", default: [], array: true
    t.string "sender_email"
    t.integer "status", default: 0
    t.string "subject"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_email_logs_on_company_id"
  end

  create_table "net_suite_webhooks", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "response"
    t.string "tenant"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "plaid_event_logs", force: :cascade do |t|
    t.bigint "company_user_id"
    t.datetime "created_at", precision: nil, null: false
    t.string "event_name"
    t.string "metadata"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_user_id"], name: "index_plaid_event_logs_on_company_user_id"
  end

  create_table "sage_intacct_webhooks", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.jsonb "response"
    t.string "tenant"
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "slack_alert_logs", force: :cascade do |t|
    t.string "alert_type", default: "email_limit_reached", null: false
    t.integer "company_id", null: false
    t.datetime "created_at", null: false
    t.text "message", null: false
    t.string "source", default: "system", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id", "created_at"], name: "index_slack_alert_logs_on_company_id_and_created_at"
  end

  create_table "staff_import_service_logs", force: :cascade do |t|
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.jsonb "response"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_staff_import_service_logs_on_company_id"
  end

  create_table "stripe_webhook_logs", force: :cascade do |t|
    t.string "charge_id"
    t.bigint "company_id"
    t.datetime "created_at", null: false
    t.string "error_message"
    t.string "log_event"
    t.jsonb "response"
    t.string "stripe_id"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_stripe_webhook_logs_on_company_id"
  end

  create_table "super_admin_logs", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.string "email"
    t.string "ip_address"
    t.datetime "logged_in_at", precision: nil, default: -> { "now()" }, null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["user_id"], name: "index_super_admin_logs_on_user_id"
  end

  create_table "ticket_email_logs", force: :cascade do |t|
    t.text "attachment_details", default: [], array: true
    t.text "attachments", default: [], array: true
    t.text "body_html"
    t.text "body_text"
    t.text "cc_recipients", default: [], array: true
    t.integer "company_id"
    t.datetime "created_at", precision: nil, null: false
    t.text "email_message_id"
    t.text "error"
    t.string "from"
    t.integer "help_ticket_id"
    t.integer "log_type"
    t.text "s3_message_id"
    t.text "s3_response"
    t.string "source"
    t.string "subject"
    t.string "to"
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_ticket_email_logs_on_company_id"
    t.index ["log_type"], name: "index_ticket_email_logs_on_log_type"
  end

  create_table "v3_incoming_request_logs", force: :cascade do |t|
    t.string "build_url"
    t.bigint "company_id"
    t.integer "company_status"
    t.datetime "created_at", null: false
    t.boolean "is_first_request"
    t.string "latest_version"
    t.jsonb "request_details"
    t.string "source"
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_v3_incoming_request_logs_on_company_id"
  end

end
