namespace :help_ticket_email do
  desc "It will re-post all emails from the last 24 hours"
  task repost: :environment do
    s3 = Aws::S3::Client.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )
    post_url = "http://secure.lvh.me:3000/inbound_emails"
    s3_bucket = "nulodgic-development"

    if Rails.env.production?
      "https://secure.gogenuity.com/inbound_emails"
      s3_bucket = 'nulodgic-production'
    elsif Rails.env.staging?
      "https://secure.gogenuity-staging.com/inbound_emails"
      s3_bucket = 'nulodgic-staging'
    end

    start_time = 1.day.ago
    headers = { "content_type" => "application/x-www-form-urlencoded" }
    marker = nil
    keys = []
    bad_keys = []
    loading = true
    while (loading)
      objects = s3.list_objects_v2(bucket:'nulodgic-production', prefix:'helpdesk_emails/', delimiter:'/', start_after: marker)
      if objects.contents.size == 0
        loading = false
      end
      objects.contents.each do |object|
        if object.key =~ /helpdesk_emails\/(\w+)/
          key = $1
          marker = object.key
          if object.last_modified > start_time
            puts "key: #{object.key}"
            keys << object
            post_data = { message_id: key }
            begin
              response = RestClient.post(post_url, post_data, headers)
            rescue StandardError => e
              bad_keys << key
            end
          end
        end
      end
    end
  end

  desc "It will delete all ticket emails created before one month"
  task delete_emails: :environment do
    DeleteHelpTicketEmailsWorker.perform_async
  end
end
