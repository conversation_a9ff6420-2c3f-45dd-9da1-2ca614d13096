namespace :general_transactions do
  desc "It will create new transactions from recurring transactions"
  task check_recurring: :environment do
    CheckRecurringTransactionsWorker.perform_async
  end

  task add_company_integration_id_to_old_transaction: :environment do
    Company.joins(:plaid_accounts).find_each do |company|
     company.plaid_accounts.each do |plaid_account|
       plaid_account.general_transactions
         .where(company_integration_id: nil)
         .update_all(company_integration_id: plaid_account.company_integration.id)
     end
    end
  end

  task create_recurrances_for_missing_transactions: :environment do
    GeneralTransactionMissingService.new().check_missing_transactions
  end

  task nullify_recurred_from_id_whose_parent_does_not_exist: :environment do
    recurred_transactions = GeneralTransaction.where(recurring: false, recurred: true)
    recurred_transactions.find_each do |recurred_transaction|
      parent_transaction = GeneralTransaction.find_by(id: recurred_transaction.transaction_recurred_from_id)
      if (parent_transaction.nil?)
       recurred_transaction.update_columns(transaction_recurred_from_id: nil)
      end
    end
  end

  desc "It will delete invalid general transactions from company"
  task :remove_invalid_transactions_from_company, [:subdomain] => :environment do |t, args|
    company = Company.find_by(subdomain: args[:subdomain])
    if company.present?
      company.general_transactions.where.not(transaction_id: nil).each do |transaction|
        transaction.destroy unless transaction.valid?
      end
    end
  end

  desc "It will add company_id to invoices where company_id is zero"
  task add_company_id_to_invoices: :environment do
    Invoice.where(:company_id => [0, nil]).find_each do |invoice|
      if (company_id = invoice.general_transactions.where.not(company_id: nil).first&.company_id)
        invoice.update_columns(company_id: company_id)
      end
    end
  end

  desc "It will delete all the invoices that are not linked to any general transaction"
  task delete_invoices_that_are_not_linked_to_any_transaction: :environment do
    general_transaction_invoice_ids = GeneralTransactionInvoice.pluck(:invoice_id).compact.uniq
    Invoice.where.not(id: general_transaction_invoice_ids).delete_all
  end
end
