namespace :module_alert do
  desc "Send emails for module alerts"
  task send_emails: :environment do
    ModuleAlertSendEmailsWorker.perform_async
  end

  desc "add module_alert to module_alert_contributors"
  task add_module_alert_to_module_alert_contributors: :environment do
    # Remove all the ModuleAlertContributor records that don't have a vendor associated with it
    contributor_alerts_no_vendor_ids = ModuleAlertContributor.pluck(:alertable_id) - Vendor.pluck(:id)
    ModuleAlertContributor.where(alertable_id: contributor_alerts_no_vendor_ids).delete_all

    # Remove all the ModuleAlert records that don't have a vendor associated with it
    module_alerts_no_vendor_ids = VendorSpendAlert.pluck(:monitorable_id) - Vendor.pluck(:id)
    VendorSpendAlert.where(monitorable_id: module_alerts_no_vendor_ids).delete_all

    # Create a ModuleAlertContributor record for the existing ModuleAlerts
    ModuleAlert.all.find_each do |ma|
      mod_alert_cus = ma.company.module_alert_contributors
      cus = mod_alert_cus.where(alertable_id: ma.monitorable_id)
      cus.update_all(module_alert_id: ma.id)
    end

    # Remove the ModuleAlertContributor records that don't have a ModuleAlert associated with it
    records_to_destroy = []
    ModuleAlertContributor.where(module_alert_id: nil).find_each do |macu|
      alert = ModuleAlert.find_by(monitorable_id: macu.alertable_id)
      records_to_destroy << macu.id if alert.nil?
    end
    ModuleAlertContributor.where(id: records_to_destroy).delete_all
  end

  desc "convert module alert company user ids to contributor ids"
  task convert_to_contributor: :environment do
    ModuleAlertContributor.find_each do |alert|
      company = Company.find_by(id: alert.company_id)
      if company.present?
        comp_user = company.company_users.find_by(id: alert.contributor_id)
        alert.update!(contributor_id: comp_user.contributor.id) if comp_user.present?
      end
    end
  end
end
