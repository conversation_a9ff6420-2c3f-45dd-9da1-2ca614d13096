namespace :service_option do
  desc "It will create service option to hide/unhide asset risk center"
  task asset_risk_center_sections: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "other",
      service_name: "asset_risk_center_sections"
    )
  end

  desc "It will create feature flag for contracts calendar"
  task contracts_calendar_section: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "contracts/calendar"
    )
  end

  desc "It will create feature flag for kanban view"
  task helpdesk_kanban_view: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "help_tickets/kanban_view"
    )
  end

  desc "It will create feature flag for help ticket modern view"
  task help_ticket_modern_view: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "help_ticket_modern_view"
    )
  end

  desc "Delete the feature flag for help ticket modern view"
  task delete_help_ticket_modern_view: :environment do
    ServiceOption.find_by(service_type: "feature", service_name: "help_ticket_modern_view").destroy
  end

  desc "Create import service options for all modules"
  task create_import_options: :environment do
    modules = ["assets", "phone_numbers", "help_tickets", "ip_addresses", "contracts", 
               "company_users", "apps", "locations", "general_transactions", "vendors", "telecom_services"]
    
    modules.each do |module_name|
      ServiceOption.find_or_create_by(service_name: module_name, service_type: ServiceOption.service_types[:imports]) do |option|
        option.status = false
        option.company_ids = []
      end
    end
    
    puts "Import service options created successfully."
  end

  desc "It will create feature flag to enable/disable help ticket drafts"
  task help_ticket_drafts: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "help_tickets/drafts"
    )
  end

  desc "It will create feature flag for people tab view"
  task managed_asset_people_tab: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "managed_assets/people_tab"
    )
  end
  
  desc "It will create feature flag to enable/disable people tab in help desk"
  task help_desk_people_tab: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "helpdesk/people"
    )
  end

  desc "It will create feature flag for ticket summary"
  task help_tickets_ai_summary: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "help_tickets/ai_summary",
      status: true
    )
  end

  desc "It will create feature flag to enable/disable google workspace integration"
  task assets_google_workspace_integration: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "discovery_tools/google_workspace_integration",
      status: true
    )
  end

  desc "It will create feature flag for ticket comments feature"
  task help_tickets_ai_comments: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "help_tickets/ai_comments",
      status: true
    )
  end
end
