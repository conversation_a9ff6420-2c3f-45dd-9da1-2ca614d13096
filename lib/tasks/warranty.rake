namespace :warranty do
  desc "Fetch missing warranties"
  task fetch_warranty: :environment do
    CronLock.lock("warranty:fetch") do
      count = 0
      ManagedAsset.unscoped.joins(company: :subscriptions)
      .where(merged: false, warranty_info_fetched: false, subscriptions: {status: ["active", "canceled"], module_type: ["asset_management", "full_platform"]})
      .where("warranty_fetch_scheduled_at IS NULL OR warranty_fetch_scheduled_at <= ?", Date.today)
      .with_manufacturer_and_machine_serial_number.distinct.find_each do |asset|
        break if count >= 10_000
        
        manufacturer = asset.try(:manufacturer).try(:downcase)
        if manufacturer && ManagedAsset::MANUFACTURERS.match(manufacturer).present?
          FetchWarrantyExpirationDateWorker.perform_in(count * 30.seconds, asset.id, true)
          count += 1
        end
      end
    end
  end

  desc "Fix lenovo warranties"
  task fix_lenovo_warranty: :environment do
    ManagedAsset.where(manufacturer: "lenovo").where.not(machine_serial_number: nil).find_each do |asset|
    	warranty_details = Warranty::LenovoAzureService.new.warranty_expiry_date({machine_serial_number: asset.machine_serial_number, product_number: asset.product_number})
      asset.update_column(:warranty_expiration, warranty_details[:date]) if warranty_details[:date]
    end
  end
end
