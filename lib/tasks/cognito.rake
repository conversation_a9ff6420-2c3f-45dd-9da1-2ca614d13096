namespace :cognito do
  desc "update first_name and last_name of cognito_users from local db"
  task update_users_attributes: :environment do
    service = CognitoService.new
    email_addresses = []

    cognito_users = service.users_list
    cognito_users['users'].each do |user|
      user.attributes.each do |attr|
        next if attr.name != "email"
        email_addresses << attr.value
      end
    end

    local_users = User.where(email: email_addresses).includes(company_users: :company)
    local_users.each do |user|
      company = user.companies.not_sample.first
      service.update_user_attributes(user.email, user.email, user.first_name, user.last_name, company.guid, company.name)
    end
  end

  desc "update missing company_guid and company_name from local db"
  task update_company_user_attributes: :environment do
    service = CognitoService.new
    email_addresses = []
    
    response = service.users_list
    cognito_users = response.users

    cognito_users.each do |c_user|
      user_email = c_user.attributes.find{|item| item[:name] == 'email'}
      
      if (c_user.attributes.find{|item| item[:name] == 'family_name'}&.value&.downcase != 'admin' and
        (c_user.attributes.find{|item| item[:name] == "custom:company_name"}&.value.blank? or
        c_user.attributes.find{|item| item[:name] == "custom:external_company_id"}&.value.blank? ))
      
        email_addresses << user_email.value
      end
    end

    db_users = User.where(email: email_addresses).joins(:companies).distinct
    db_users.each do |user|
      company = user.companies.not_sample&.first
      service.update_user_attributes(user.email, user.email, user.first_name, user.last_name, company.guid, company.name) if company.present?

      Rails.logger.info("Updated Company guid for User #{user.email}")
    end

  end

  desc "destroy users from cognito which are not associated with any company"
  task destroy_user: :environment do

    User.left_outer_joins(:companies).where(companies: {id: nil}).distinct.each do |user|
      user.user_accesses.destroy_all
      user.destroy!
    end

  end

  desc "destroy users from that are deleted from db but present in cognito user pool"
  task destroy_redundant_users: :environment do
    @client = Aws::CognitoIdentityProvider::Client.new(
      region: Rails.application.credentials.aws[:region],
      access_key_id: Rails.application.credentials.aws[:cognito][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:cognito][:secret_access_key]
    )
    pt = nil
    deleted_user = []
    loop do
      res = @client.list_users({ user_pool_id: Rails.application.credentials.aws[:cognito][:user_pool_id], pagination_token: pt })
      pt = res.pagination_token
      res.users.each do |user|
        cog_username = user.username
        @client.admin_delete_user({username: cog_user_username, user_pool_id: Rails.application.credentials.aws[:cognito][:user_pool_id]}) if User.find_by(guid: cog_username).blank?
      end
      break if pt.blank?
    end
  end

  desc "import user data from cognito and upload to s3"
  task import_users: :environment do
    CognitoImportUsersWorker.perform_async
  end

  desc "restore user data from s3"
  task restore_users: :environment do
    s3 = Aws::S3::Client.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )

    client = Aws::CognitoIdentityProvider::Client.new(
      region: Rails.application.credentials.aws[:region],
      access_key_id: Rails.application.credentials.aws[:cognito][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:cognito][:secret_access_key]
    )

    file = "#{Rails.root}/public/s3_users_data.csv"
    users_attributes = []
    users_attributes = import_data_to_csv(s3, users_attributes, file)
    restore_users_to_cognito(client, users_attributes)
    File.delete(file) if File.exist?(file)
  end

  # Make sure to execute below tasks in the order they were written.
  desc "update users if only for external users"
  task update_users_with_external_guids: :environment do
    # update users only for SAML and Google identities
    updated_users = update_users_guids('external')
  end

  desc "update users with primary guids"
  task update_with_primary_guids: :environment do
    # update all users with primary guid
    UpdateCognitoUsersWithPrimaryGuidsWorker.perform_async
  end

  desc "update app direct users external ids (GUIDs)"
  task update_appdirect_users_external_ids: :environment do
    updated_users = update_appdirect_users
  end
end

private

def appdirect_service
  @service ||= AppDirect::Service.new()
end

def import_data_to_csv(s3, users_attributes, file)
  user_attributes = []
  # s3_file_name = "users_data-2021-11-16" #add the name of the file which you want to restore

  File.open(file, 'w') do |file|
    s3.get_object(bucket: "cognito-pool-backup", key: "#{Rails.env}/#{s3_file_name}.csv" , response_target: file)
  end

  table = CSV.parse(File.read(file), headers: true)
  table.each do |all_attributes|
    users_attributes << user_attributes
    user_attributes = []
    JSON.parse(all_attributes['User Attributes']).each do |attribute|
      user_attributes << ({ name: attribute.first, value: attribute.last })
    end
  end
  users_attributes.delete_at(0)
  return users_attributes
end

def restore_users_to_cognito(client, users_attributes)
  users_attributes.each do |user|
    user_data = extract_data(user)
      #inorder to restore a specific user, enter it's email in the following if

      # if user_data[:email] == ''
      user_params = set_user_params(user_data)
      begin
      @response = client.admin_create_user(user_params)
      rescue Aws::CognitoIdentityProvider::Errors::UsernameExistsException => e
        return e.message
      end
    # end #for the above commented if
  end
end

def extract_data(user)
  user_data = {}

  user.each do |attribute|
    if attribute[:name] == 'email'
      user_data[:email] = attribute[:value]
    elsif attribute[:name] == 'given_name'
      user_data[:first_name] = attribute[:value]
    elsif attribute[:name] == 'family_name'
      user_data[:last_name] = attribute[:value]
    elsif attribute[:name] == 'custom:external_company_id'
      user_data[:custom_external_company_id] = attribute[:value]
    elsif attribute[:name] == 'custom:company_name'
      user_data[:custom_company_name] = attribute[:value]
    elsif attribute[:name] == 'email_verified'
      user_data[:email_verified] = attribute[:value]
    end
  end

  user_data
end

def set_user_params(user_data)
  user_params = {
    user_pool_id: "#{Rails.application.credentials.aws[:cognito][:user_pool_id]}",
    username: user_data[:email],
    user_attributes: [
      {
        name: 'email',
        value: user_data[:email]
      },
      {
        name: 'given_name',
        value: user_data[:first_name] || ''
      },
      {
        name: 'family_name',
        value: user_data[:last_name] || ''
      },
      {
        name: 'custom:external_company_id',
        value: user_data[:custom_external_company_id] || ''
      },
      {
        name: 'custom:company_name',
        value: user_data[:custom_company_name] || ''
      },
      {
        name: 'email_verified',
        value: user_data[:email_verified]
      }
    ],
    message_action: 'SUPPRESS',
    desired_delivery_mediums: ['EMAIL']
  }

  user_params
end

def client
  @client ||= Aws::CognitoIdentityProvider::Client.new( 
    region: Rails.application.credentials.aws[:region], 
    secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key], 
    access_key_id: Rails.application.credentials.aws[:s3][:access_key_id]
  )
end

def user_pool_id
  Rails.application.credentials.aws[:cognito][:user_pool_id]
end

def list_users(user_pool_id:, pagination_token: nil)
  client.list_users({ user_pool_id: user_pool_id, pagination_token: pagination_token })
end

def update_users_guids(user_type)
  updated_users = {}
  res = list_users(user_pool_id: user_pool_id)

  loop do
    res.users.each do |user|
      cognito_sub = user.username

      if user_type == 'primary'
        # skip external users
        # ignore the SAML and Google identities users
        next if user.attributes.find {|attr| attr.name == 'identities'}
      else
        # skip primary users
        next unless user.attributes.find {|attr| attr.name == 'identities'}
      end
      # ignore if the user is already updated with the primary guid
      next if User.find_by_guid(cognito_sub)

      # return if email not found in our postgres database
      email_address = user.attributes.find { |attr| attr.name == 'email' }.value
      postgres_user = User.find_by_email(email_address)
      next unless postgres_user

      updated_users[email_address] = {psql_guid: postgres_user.guid, cognito_sub: cognito_sub}
      
      # update the cognito user's external ID
      appdirect_user = appdirect_service.fetch_user(postgres_user.guid)
      if appdirect_user.present?
        appdirect_service.update_user_external_id(appdirect_user['uuid'], cognito_sub)
      end

      postgres_user.update_column(:guid, cognito_sub)
      Rails.logger.info("User with diff sub found: #{email_address}")
    end

    break if res.pagination_token.blank?

    res = list_users(user_pool_id: user_pool_id, pagination_token: res.pagination_token)
  end

  updated_users
end

def update_appdirect_users
  # update all mismatched appdirect users' external IDs
  updated_data = {}
  response = appdirect_service.fetch_all_users

  loop do
    break if response["page"]["number"] > response["page"]["totalPages"]

    response["content"].each do |user|
      next if user["externalId"].nil?

      db_user_guid = User.find_by(email: user["email"])&.guid
      next if db_user_guid.nil? || db_user_guid == user["externalId"]

      # Update the App Direct user's external IDs
      updated_data[user['uuid']] = {old_external_id: user["externalId"], db_user_guid: db_user_guid}
      appdirect_service.update_user_external_id(user['uuid'], db_user_guid)
    end

    page_number = response["page"]["number"] + 1
    response = appdirect_service.fetch_all_users(page_number)
  end

  updated_data
end
