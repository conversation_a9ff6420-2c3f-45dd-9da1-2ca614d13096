require 'rails_helper'

RSpec.describe Integrations::GoogleWorkspace::SyncDataWorker, type: :worker do
  describe '#perform' do
    before(:each) do
      @company = FactoryBot.create(:company)
      @config = FactoryBot.create(:google_workspace_config, company: @company)

      @device = OpenStruct.new(
        model: 'Pixel 7',
        os: 'Android 14',
        serial_number: 'GW123456',
        wifi_mac_address: 'AA:BB:CC:DD:EE:FF',
        manufacturer: 'Google'
      )

      @response = OpenStruct.new(
        mobiledevices: [@device],
        next_page_token: nil
      )

      allow_any_instance_of(Integrations::GoogleWorkspace::FetchData).to receive(:fetch_all_mobile_devices).and_return(@response)
      allow_any_instance_of(Integrations::GoogleWorkspace::SyncDataWorker).to receive(:add_source)
      allow_any_instance_of(Integrations::GoogleWorkspace::SyncDataWorker).to receive(:is_higher_precedence?).and_return(true)
      allow(Integrations::GoogleWorkspace::SaveChromeDevicesWorker).to receive(:perform_async)
    end

    it 'saves a discovered asset from Google Workspace device' do
      expect {
        described_class.new.perform(@config.id, true, false, nil)
      }.to change(DiscoveredAsset, :count).by(1)

      asset = DiscoveredAsset.last
      expect(asset.company_id).to eq(@company.id)
      expect(asset.machine_serial_no).to eq('GW123456')
      expect(asset.source).to eq('google_workspace')
    end

    it 'calls SaveChromeDevicesWorker after saving mobile devices' do
      described_class.new.perform(@config.id, true, false, nil)
      expect(Integrations::GoogleWorkspace::SaveChromeDevicesWorker).to have_received(:perform_async).with(@config.id, true)
    end
  end
end
