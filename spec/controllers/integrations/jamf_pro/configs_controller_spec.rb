require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::JamfPro::ConfigsController, type: :controller do
  create_company_and_user

  before do
    controller.send(:set_privilege)
  end

  let(:jamf_pro_config) { FactoryBot.create(:jamf_pro_config, company: company) }

  describe '#create' do
    token_detail = {
      'token'=> 'mock_access_token',
      'expires'=> DateTime.now + 30.minutes,
    }

    jamf_pro_data = {
      'username'=> 'ryan',
      'password'=> '********',
      'instance_name'=> 'airland'
    }

    incorrect_jamf_pro_data = {
      'username'=> '',
      'password'=> '********',
      'instance_name'=> 'airland'
    }

    it 'authenticates and saves credentials and redirects to sync accounts page' do
      allow_any_instance_of(Integrations::JamfPro::FetchData).to receive(:token).and_return(token_detail)

      post :create, params: { jamf_pro_data: jamf_pro_data }
      expect(Integrations::JamfPro::Config.where(company_id: company.id).count).to eq(1)
    end

    it 'does not authenticate and fails create integration' do
      allow_any_instance_of(Integrations::JamfPro::FetchData).to receive(:token).and_return(token_detail)

      post :create, params: { jamf_pro_data: incorrect_jamf_pro_data }
      expect(JSON.parse(response.body)['message']).to eq("Username can't be blank")
      expect(Integrations::JamfPro::Config.where(company_id: company.id).count).to eq(0)
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      resync_params = {
        id: jamf_pro_config.id,
        integration_name: 'jamf_pro'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'Delete #destroy' do
    it 'Deletes JamfPro config' do
      delete :destroy, params: { id: jamf_pro_config.id }
      expect(response.status).to eq(200)
      expect(company.discovered_assets.joins(:asset_sources).where(asset_sources: { source: 'jamf_pro' }).count).to eq(0)
    end
  end

  describe 'Disable JamfPro' do
    it 'Disables JamfPro' do
      post :deactivate, params: { id: jamf_pro_config.id }
      expect(response.status).to eq(200)
      expect(jamf_pro_config.company_integration.active).to eq(false)
      expect(jamf_pro_config.company_integration.status).to eq(false)
    end
  end
end
