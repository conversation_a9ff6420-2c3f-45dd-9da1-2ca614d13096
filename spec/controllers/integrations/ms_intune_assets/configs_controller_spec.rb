require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::MsIntuneAssets::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    FactoryBot.create(:ms_intune_assets_config, company: company)
    @config ||= Company.find(company.id).ms_intune_assets_config
    controller.send(:set_privilege)
  end

  describe 'Get #consent_callback' do
    token_detail = {
      'token_type':'Bearer',
      'scope': 'offline_access email openid profile DeviceManagementManagedDevices.Read.All',
      'expires_in'=>3600,
      'ext_expires_in'=>3600,
      'access_token'=>'mock_access_token',
      'refresh_token'=>'OAQABAAAAAAAP0wLlqdLVToOpA4kwzSnxiAMsfekyjT2BNGLdR',
      'id_token'=>'mock_id_token'
    }

    consent_params = {
      'session_state'=>'da3d747e-cbe2-4fb7-9476-c93ed8d089e5',
      'controller'=>'integrations/ms_intune_assets/configs',
      'action'=>'consent_callback'
    }

    it 'authenticates and saves credentials and redirects to sync accounts page' do
      allow_any_instance_of(Integrations::MsIntuneAssets::FetchData).to receive(:token).and_return(token_detail)
      consent_params['code'] = 'OAQABAAIAAAAP0wLlqdLVToOpA4kwzSnxGu6c1Erydyxmy'
      consent_params['state'] = company.id
      get :consent_callback , params: consent_params
      response.should redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.assets_connectors_path}")
      expect(Integrations::MsIntuneAssets::Config.where(company_id: company.id).count).to eq(1)
    end

    it 'does not authenticate and fails create integration' do
      Integrations::MsIntuneAssets::Config.find_by(company_id: company.id).destroy
      allow_any_instance_of(Integrations::MsIntuneAssets::FetchData).to receive(:token).and_return(token_detail)
      consent_params['code'] = nil
      consent_params['state'] = company.id
      get :consent_callback , params: consent_params
      response.should redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.assets_connectors_path}")
      expect(Integrations::MsIntuneAssets::Config.where(company_id: company.id).count).to eq(0)
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      resync_params = {
        id: @config.id,
        integration_name: 'ms_intune_assets'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'Delete #destroy' do
    it 'Deletes Intune config' do
      delete :destroy, params: { id: @config.id }
      expect(response.status).to eq(200)
      expect(company.discovered_assets.joins(:asset_sources).where(asset_sources: { source: 'ms_intune' }).count).to eq(0)
    end
  end

  describe 'Disable Intune' do
    it 'Disables Intune' do
      post :deactivate, params: { id: @config.id}
      expect(response.status).to eq(200)
      expect(@config.company_integration.active).to eq(false)
      expect(@config.company_integration.status).to eq(false)
    end
  end
end
