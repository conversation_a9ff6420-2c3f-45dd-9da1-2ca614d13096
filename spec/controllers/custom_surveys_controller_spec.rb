require 'rails_helper'
include CompanyUserHelper

RSpec.describe CustomSurveysController, type: :controller do
  create_company_and_user
  
  let!(:custom_survey) { CustomSurvey.create!(title: "Custom Survey Spec Test Cont", company: company, workspace: workspace) }
  let(:valid_params) do
    {
      custom_survey: {
        background_color: "#0C345F",
        description: "test description",
        title: "Custom Survey Valid Params",
        custom_design: true,
        visible: false,
        company_id: company.id,
        workspace_id: workspace.id,
        custom_forms: [{ id: 0, name: 'All Custom Forms', label: 'Form' }],
        is_default: false,
        questions: [
          { 
            question_text: "test question", 
            question_type: "short"
          }
        ],
        rules: [
          { 
            condition: "{\"start\":\"test\",\"middle\":\"test\",\"end\":\"test\"}",
            actions: [
              { action_type: "{\"start\":\"Show\",\"question\":\"test.\",\"index\":5}"}
            ]
          }
        ],
        trigger: { button_text: "test button" }
      }
    }
  end

  describe "GET index" do
    it "returns custom surveys" do
      get :index

      custom_surveys = JSON.parse(response.body)['custom_surveys']
      expect(response).to have_http_status(:ok)
      expect(custom_surveys).to be_present
      expect(custom_surveys.last['id']).to eq(custom_survey.id)
    end
  end

  describe "GET show" do
    context "when the custom survey exists" do
      it "returns the custom survey as JSON" do
        get :show, params: { id: custom_survey.id }, format: :json

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(json_response['id']).to eq(custom_survey.id)
      end
    end

    context "when the custom survey does not exist" do
      it "returns nil" do
        get :show, params: { id: 99999 }, format: :json 

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok) 
        expect(json_response).to be_nil
      end
    end
  end

  describe "POST create" do
    context "with valid parameters" do
      it "creates a new custom survey" do
        expect {
          post :create, params: valid_params, format: :json
        }.to change(CustomSurvey, :count).by(1)

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(json_response['title']).to eq("Custom Survey Valid Params")
      end
    end

    context "when invalid parameters" do
      it "returns an error when title is not present" do
        expect {
          invalid_params = {custom_survey: valid_params[:custom_survey].except(:title)}
          post :create, params: invalid_params, format: :json
        }.to change(CustomSurvey, :count).by(0)

        expect(response.body).to include("Validation failed: Title is required")
      end
    end  
  end

  describe "PATCH update" do
    context "with valid parameters" do
      it "updates the existing custom survey" do
        CustomSurvey::Trigger.create!(
            custom_survey_id: custom_survey.id,
            button_text: "Button Test" 
        )
        patch :update, params: { id: custom_survey.id, custom_survey: valid_params[:custom_survey] }, format: :json
        custom_survey.reload

        expect(response).to have_http_status(:ok)
        expect(custom_survey.title).to eq("Custom Survey Valid Params")
        expect(custom_survey.description).to eq("test description")
      end
    end
  
    context "when the custom survey does not exist" do
      it "returns a status of (204) no content" do
        patch :update, params: { id: 99999, custom_survey: valid_params[:custom_survey] }, format: :json

        expect(response).to have_http_status(:no_content)
      end
    end
  end

  describe "DELETE #destroy" do
    context "when the custom survey exists" do
      it "deletes the custom survey and returns success message" do
        custom_survey 
        expect {
          delete :destroy, params: { id: custom_survey.id }, format: :json
        }.to change(CustomSurvey, :count).by(-1)

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["message"]).to eq("Custom Survey deleted successfully")
      end
    end

    context "when the custom survey does not exist" do
      it "returns an error message" do
        expect {
          delete :destroy, params: { id: 99999 }, format: :json
        }.to raise_error(NoMethodError, /undefined method `destroy' for nil:NilClass/)
      end
    end
  end

  describe "CUSTOM ACTION #toggle_active" do
    context "when given valid id" do
      it "toggles the visibility of the custom survey" do
        get :toggle_active, params: { id: custom_survey.id, visible: true }, format: :json

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['domain']['visible']).to eq(true)
      end
    end

    context "when given invalid id" do
      it "toggles the visibility of the custom survey" do
        expect {
          get :toggle_active, params: { id: 99999, visible: true }, format: :json
        }.to raise_error(NoMethodError, /undefined method `visible=' for nil:NilClass/)
      end
    end
  end

  describe "CUSTOM ACTION #should_display_custom_surveys" do
    it "determines whether to display custom surveys or not" do
      get :should_display_custom_surveys
      
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to have_key("display_survey_section")
    end
  end
end
