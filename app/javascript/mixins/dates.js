
export default {
  computed: {
    currentTz() {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    },
  },
  methods: {
    toISOstring(date) {
      return moment(new Date(date).toISOString());
    },
    daydiff(first, second) {
      return Math.round((second - first) / (1000 * 60 * 60 * 24));
    },
    timeAgo(date) {
      return moment(date.toString()).fromNow();
    },
    showDate(date) {
      if (date) {
        return moment(date.toString()).format('MMM DD, YYYY');
      }
      return null;
    },
    showFullDate(date) {
      if (!date) {
        return null;
      }
      return moment(date.toString()).format("MMMM DD, YYYY");
    },
    showDateCondensed(date) {
      return moment(date.toString()).format('MM/DD');
    },
    showDateTime(date) {
      return moment(date.toString()).format('h:mm a, MMM DD, YYYY');
    },
    daysUntilToday(futureDate) {
      return moment(futureDate).startOf('day').diff(moment().startOf('day'), 'days');
    },
    toDaysAndHours(hoursString) {
      const uptimeHours = parseInt(hoursString.replace(/ Hours/gi, ''), 10);
      const days = Math.floor(uptimeHours / 24);
      const hours = uptimeHours % 24;
      const dayPart = days > 0 ? `${days} Day(s)` : '';
      const hourPart = hours > 0 ? `${hours} Hour(s)` : '';

      return [dayPart, hourPart].filter(Boolean).join(', ') || '0 Hour(s)';
    },
    timeframeDates(timeframe) {
      const today = moment().startOf("day");
      const startOfMonth = moment().startOf("month");
      const startOfQuarter = moment().startOf("quarter");
      const startOfYear = moment().startOf("year");

      if (timeframe.name === "custom_date_range") {
        const start = typeof timeframe.startDate === "string" ? new Date(timeframe.startDate) : timeframe.startDate;
        const end = typeof timeframe.endDate === "string" ? new Date(timeframe.endDate) : timeframe.endDate;
      
        return {
          startDate: start.toISOString(),
          endDate: end.toISOString(),
        };
      }

      if (timeframe === "Today") {
        return {
          startDate: today.toISOString(),
          endDate: today.endOf("day").toISOString(),
        };
      } 
      else if (timeframe === "Yesterday") {
        return {
          startDate: today.subtract(1, "days").toISOString(),
          endDate: today.endOf("day").toISOString(),
        };
      } 
      else if (timeframe === "This Week") {
        return {
          startDate: moment().startOf("isoWeek").toISOString(),
          endDate: moment().endOf("isoWeek").toISOString(),
        };
      } 
      else if (timeframe === "Previous 7 Days") {
        return {
          startDate: moment().subtract(7, "days").startOf("day").toISOString(),
          endDate: today.endOf("day").toISOString(),
        };
      } 
      else if (timeframe === "Previous Week") {
        return {
          startDate: moment().subtract(1, "week").startOf("isoWeek").toISOString(),
          endDate: moment().subtract(1, "week").endOf("isoWeek").toISOString(),
        };
      } 
      else if (timeframe === "This Month") {
        return {
          startDate: startOfMonth.toISOString(),
          endDate: moment().endOf("month").toISOString(),
        };
      } 
      else if (timeframe === "Previous 30 Days") {
        return {
          startDate: moment().subtract(30, "days").startOf("day").toISOString(),
          endDate: today.endOf("day").toISOString(),
        };
      } 
      else if (timeframe === "Previous Month") {
        return {
          startDate: moment().subtract(1, "month").startOf("month").toISOString(),
          endDate: moment().subtract(1, "month").endOf("month").toISOString(),
        };
      } 
      else if (timeframe === "This Quarter") {
        return {
          startDate: startOfQuarter.toISOString(),
          endDate: moment().endOf("quarter").toISOString(),
        };
      } 
      else if (timeframe === "Previous 3 Months") {
        return {
          startDate: moment().subtract(3, "months").startOf("month").toISOString(),
          endDate: moment().subtract(1, "months").endOf("month").toISOString(),
        };
      } 
      else if (timeframe === "Previous Quarter") {
        return {
          startDate: moment().subtract(1, "quarter").startOf("quarter").toISOString(),
          endDate: moment().subtract(1, "quarter").endOf("quarter").toISOString(),
        };
      } 
      else if (timeframe === "Year-to-Date") {
        return {
          startDate: startOfYear.toISOString(),
          endDate: today.endOf("day").toISOString(),
        };
      } 
      
      return {
        startDate: null,
        endDate: null,
      };
      
    },
    previousTimeperiodDates(timeframe) {
      const today = moment().startOf("day");
      const startOfMonth = moment().startOf("month");
      const startOfQuarter = moment().startOf("quarter");
      const startOfYear = moment().startOf("year");
  
      if (timeframe === "Today") {
        return {
          startDate: today.subtract(1, "days").toISOString(),
          endDate: today.endOf("day").toISOString(),
        };
      }
      else if (timeframe === "Yesterday") {
        const dayBeforeYesterday = moment().subtract(2, "days").startOf("day");
        return {
          startDate: dayBeforeYesterday.toISOString(),
          endDate: dayBeforeYesterday.endOf("day").toISOString(),
        };
      }
      else if (timeframe === "This Week") {
        return {
          startDate: moment().startOf("isoWeek").subtract(1, "week").toISOString(),
          endDate: moment().startOf("isoWeek").subtract(1, "day").endOf("day").toISOString(),
        };
      }
      else if (timeframe === "Previous 7 Days") {
        return {
          startDate: moment().subtract(14, "days").startOf("day").toISOString(),
          endDate: moment().subtract(7, "days").endOf("day").toISOString(),
        };
      }
      else if (timeframe === "Previous Week") {
        return {
          startDate: moment().subtract(2, "weeks").startOf("isoWeek").toISOString(),
          endDate: moment().subtract(2, "weeks").endOf("isoWeek").toISOString(),
        };
      }
      else if (timeframe === "This Month") {
        return {
          startDate: startOfMonth.subtract(1, "month").toISOString(),
          endDate: moment().startOf("month").subtract(1, "day").endOf("day").toISOString(),
        };
      }
      else if (timeframe === "Previous 30 Days") {
        return {
          startDate: moment().subtract(60, "days").startOf("day").toISOString(),
          endDate: moment().subtract(30, "days").endOf("day").toISOString(),
        };
      }
      else if (timeframe === "Previous Month") {
        return {
          startDate: moment().subtract(2, "months").startOf("month").toISOString(),
          endDate: moment().subtract(2, "months").endOf("month").toISOString(),
        };
      }
      else if (timeframe === "This Quarter") {
        return {
          startDate: startOfQuarter.subtract(1, "quarter").toISOString(),
          endDate: moment().startOf("quarter").subtract(1, "day").endOf("day").toISOString(),
        };
      }
      else if (timeframe === "Previous 3 Months") {
        return {
          startDate: moment().subtract(6, "months").startOf("month").toISOString(),
          endDate: moment().subtract(4, "months").endOf("month").toISOString(),
        };
      } 
      else if (timeframe === "Previous Quarter") {
        return {
          startDate: moment().subtract(2, "quarters").startOf("quarter").toISOString(),
          endDate: moment().subtract(2, "quarters").endOf("quarter").toISOString(),
        };
      } 
      else if (timeframe === "Year-to-Date") {
        return {
          startDate: startOfYear.subtract(1, "year").toISOString(),
          endDate: startOfYear.subtract(1, "day").endOf("day").toISOString(),
        };
      }
      return null;
    },
    convertTimeFormat(timeStr, format) {
      if (!timeStr) return null;

      if (format === 'to12') {
        const m = moment(timeStr, 'HH:mm');
        return m.isValid() ? m.format('hh:mm A') : null;
      } 
      
      const m = moment(timeStr, 'hh:mm A');
      return m.isValid() ? m.format('HH:mm') : null;
    },
  },
  monthsArr: {
    monthNames: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
  },
};
