<template>
  <div
    class="clearfix"
    :class="{ 'row': isHelpdeskModule }"
  >
    <sub-menu v-if="isHelpdeskModule" />
    <div
      :class="{ 'w-75 ml-5': isHelpdeskModule }"
    >
      <div
        v-if="$isSampleCompany"
        class="container text-center my-5"
      >
        <h5 class="mb-3 font-weight-normal">
          You are in sample company. Please switch to your company to select business hours.
        </h5>
      </div>
      <div v-else>
        <div
          v-if="loading"
          class="mt-5"
        >
          <sync-loader
            color="#0d6efd"
            size="0.5rem"
            :loading="true"
          />
        </div>
        <div
          v-else-if="selectedTab !== 'company'"
          class="heading-section"
          :class="{ 'pb-0': !isHelpdeskModule }"
        >
          <h5 class="text-left">
            Work Schedule
          </h5>
          <business-hours-sub-menu
            v-if="!isHelpdeskModule"
            :active-tab="selectedTab" 
            @select-tab="selectedTab = $event"
          />
          <div class="mt-3">
            <span>
              Note: Workspace business hours apply only to that workspace and do not affect company-wide hours.
            </span>
          </div>
          <div class="pl-5 pr-5 mt-5">
            <div class="box__inner text-center">
              <h5>
                These Business Hours are workspace specific.
                <br>
                Please click
                <a 
                  class="text-primary"
                  @click="openWorkspaceBusinessHours"
                >here</a>
                to customize your Business Hours.
              </h5>
            </div>
          </div>
        </div>
        <div
          v-else
          class="w-75"
        >
          <div 
            class="heading-section"
            :class="{ 'pb-0': !isHelpdeskModule }"
          >
            <workspace-settings-banner v-if="isHelpdeskModule" />
            <h5 class="text-left">
              Work Schedule
            </h5>
          </div>
          <business-hours-sub-menu
            v-if="!isHelpdeskModule"
            :active-tab="selectedTab" 
            @select-tab="selectedTab = $event"
          />
          <div class="form-group">
            <label>
              Description
              <span class="small text-muted ml-1">(optional)</span>
            </label>
            <textarea
              v-model="workSchedule.description"
              class="form-control"
            />
          </div>
          <div class="form-group">
            <label>Time Zone</label>
            <select
              id="timezone"
              v-model="workSchedule.timezone"
              class="form-control"
              name="timezone"
            >
              <option
                v-for="tz in timezoneNames()"
                :key="tz"
                :value="tz"
              >
                {{ timezoneMapping[tz] }}
              </option>
            </select>
          </div>
          <label>Working Hours</label>
          <div class="box--block p-0">
            <div
              v-for="(day, index) in workSchedule.schedule"
              :key="`day-${index}`"
              class="border border-left-0 border-right-0 border-top-0 pl-4 mx-0 py-3 row align-items-baseline"
            >
              <div class="col-2 d-inline-flex">
                <input
                  v-model="day.active"
                  class="mr-2 mt-1 checkbox clickable"
                  type="checkbox"
                  :disabled="isLastDay(day.day)"
                >
                <label class="mb-1 ml-2">
                  {{ day.day }}
                </label>
              </div>
              <div class="align-items-baseline d-flex col-10">
                <div class="inner-addon manual-timepicker-holder ml-3 mb-2 col-auto">
                  <i class="text-muted genuicon genuicon-android-time time-icon pt-2" />
                  <vue-timepicker
                    v-model="day.startTime"
                    lazy
                    hide-clear-button
                    manual-input
                    input-class="manual-timepicker-input"
                    input-width="95%"
                    placeholder="00:00"
                  />
                </div>
                To
                <div class="inner-addon manual-timepicker-holder ml-3 mb-2 col-auto">
                  <i class="text-muted genuicon genuicon-android-time time-icon pt-2" />
                  <vue-timepicker
                    v-model="day.endTime"
                    lazy
                    hide-clear-button
                    manual-input
                    input-class="manual-timepicker-input"
                    input-width="95%"
                    placeholder="00:00"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div>
              <label>Holidays List</label>
            </div>
            <div
              v-for="(holiday, index) in workSchedule.holidays"
              :key="index"
              class="mt-2"
            >
              <div class="d-inline-flex w-75">
                <input
                  v-model="holiday.name"
                  class="input form-control mr-3"
                  name="name"
                  placeholder="Name"
                  type="text"
                >
                <nice-datepicker
                  id="holidayDate"
                  v-model="holiday.date"
                  class="w-100 mr-2"
                  :input-class="{ 'warning': holiday.date && isDateOld(holiday.date) }"
                />
                <a
                  v-tooltip="'Delete holiday'"
                  href="#"
                  class="text-muted float-right mt-2 ml-2"
                  @click.prevent="removeHoliday(index)"
                >
                  <i class="nulodgicon-trash-b mb-0 clickable mr-3" />
                </a>
              </div>
              <i
                v-if="holiday.date && isDateOld(holiday.date)"
                v-tooltip="'This date is in the past.'"
                class="nulodgicon-information-circled ml-2 mt-2 not-as-small text-warning"
              />
            </div>
            <div class="not-as-small form-text mt-2">
              <a
                href="#"
                @click.prevent="addHoliday"
              >
                &plus; Add Holiday
              </a>
            </div>
          </div>
          <div
            v-if="isHelpdeskModule"
            class="mt-4"
          />
          <button
            class="btn btn-primary mt-1 submit-custom"
            :class="{ 
              'mb-2': isHelpdeskModule,
              'mt-3': !isHelpdeskModule
            }"
            :disabled="submitting"
            @click="
              isHelpdeskModule
                ? confirmationModal()
                : updateBusinessHours()"
          >
            {{ submitting ? 'Saving' : 'Save' }}
          </button>
        </div>
      </div>
    </div>
    <hd-settings-confirmation-modal
      ref="helpdeskSettings"
      @update="updateBusinessHours"
    />
  </div>
</template>

<script>
import SyncLoader from "vue-spinner/src/SyncLoader.vue";
import VueTimepicker from "vue2-timepicker";
import "vue2-timepicker/dist/VueTimepicker.css";
import http from "common/http";
import permissionsHelper from "mixins/permissions_helper";
import MomentTimezone from 'mixins/moment-timezone';
import NiceDatepicker from 'components/shared/nice_datepicker';
import SubMenu from "../help_tickets/settings/sub_menu.vue";
import hdSettingsConfirmationModal from "../shared/hd_settings_confirmation_modal.vue";
import businessHoursSubMenu from "./business_hours_sub_menu.vue";
import workspaceSettingsBanner from "../help_tickets/settings/workspace_settings_banner.vue";

export default {
  components: {
    SyncLoader,
    VueTimepicker,
    NiceDatepicker,
    SubMenu,
    hdSettingsConfirmationModal,
    businessHoursSubMenu,
    workspaceSettingsBanner,
  },
  mixins: [permissionsHelper, MomentTimezone],
  data() {
    return {
      loading: true,
      submitting: false,
      workSchedule: {},
      selectedTab: 'company',
    };
  },
  computed: {
    activeDays() {
      return this.workSchedule?.schedule.filter((day) => day.active);
    },
    isHelpdeskModule() {
      return this.moduleName === 'HelpTicket';
    },
  },
  methods: {
    onWorkspaceChange() {
      this.fetchBusinessHours();
    },
    fetchBusinessHours() {
      const selectedWorkspace = this.isHelpdeskModule;
      http
        .get("/company/business_hours.json", { params: { is_helpdesk_module: selectedWorkspace } })
        .then(res => {
          this.loading = false;
          this.workSchedule = res.data.businessHour;
        })
        .catch(() => {
          this.loading = false;
          this.emitError("Sorry, there was an error fetching these hours. Please refresh the page.");
        });
    },
    isDateOld(holidayDate) {
      const currentDate = this.timezoneDate(new Date(), this.workSchedule.timezone);
      const targetDate = this.timezoneDate(holidayDate, this.workSchedule.timezone);

      return moment(targetDate, 'MMM DD, YYYY').isBefore(moment(currentDate, 'MMM DD, YYYY'));
    },
    isLastDay(day) {
      return this.activeDays.length === 1 && this.activeDays[0].day === day;
    },
    confirmationModal() {
      this.$refs.helpdeskSettings?.open();
    },
    updateBusinessHours() {
      this.submitting = true;
      const errorText = "Sorry, there was an error saving these settings.";

      const isValidTimeRange = this.workSchedule.schedule.every(day => {
        const start = moment(day.startTime, "HH:mm");
        const end = moment(day.endTime, "HH:mm");
        const timeDiff = end.diff(start);  
        return timeDiff > 0;
      });

      if (!isValidTimeRange) {
        this.submitting = false;
        this.emitError(`${errorText} Ending time has to be after the start time.`);
        return;
      }

      if (this.activeDays.length === 0) {
        this.submitting = false;
        this.emitError(`${errorText} You must select at least one day.`);
        return;
      }

      this.saveBusinessHours();
    },
    saveBusinessHours() {
      http
        .post(`/company/update_business_hours.json`, {
          schedule: this.workSchedule.schedule,
          timezone: this.workSchedule.timezone,
          is_helpdesk_module: this.isHelpdeskModule,
          description: this.workSchedule.description,
          holidays: this.workSchedule.holidays,
         })
        .then(res => {
          this.submitting = false;
          this.emitSuccess(res.data.message);
        })
        .catch(error => {
          this.submitting = false;
          this.emitError(`Sorry, there was an error updating work schedule details. ${error.message}`);
        });
    },
    addHoliday() {
      this.workSchedule.holidays = this.workSchedule.holidays || [];
      this.workSchedule.holidays.push({
        name: '',
        date: '',
      });
    },
    removeHoliday(index) {
      this.workSchedule.holidays.splice(index, 1);
    },
    openWorkspaceBusinessHours() {
      window.open('/help_tickets/settings/business_hour_settings', '_blank');
    },
  },
};
</script>

<style lang="scss" scoped>
.inner-addon {
  position: relative;
}

.left-addon .genuicon  { left:  0px;}

.left-addon input  { padding-left:  40px; }

.inner-addon .genuicon {
  position: absolute;
  color: #BBB;
  padding: 10px;
  font-size: 1.2rem;
  pointer-events: none;
}

.time-icon {
  z-index: 1;
}
</style>
