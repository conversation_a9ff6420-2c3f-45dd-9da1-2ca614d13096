<template>
  <div>
    <span
      v-show="!isHintOpen"
      class="tips-icon-wrap col-auto mt-2"
      data-tc-custom-form-hint-button
      @click.prevent="showHint(true)"
    >
      <img
        v-tooltip="'Show Hints'"
        class="tips-icon mt-1"
        src="https://nulodgic-static-assets.s3.amazonaws.com/images/tips_lightbulb.svg"
      >
      <span class="tips-icon__highlight" />
    </span>

    <div class="row justify-content-end m-0">
      <dismissible-container
        :container-classes="'w-100 m-0 p-0'"
        :show-container="isHintOpen"
        :persist-state="true"
        @show-hide-container="showHint"
      >
        <div class="bg-lighter p-4 rounded">
          <h6 class="text-cyan mb-3">
            <img
              class="tips-icon mr-2"
              src="https://nulodgic-static-assets.s3.amazonaws.com/images/tips_lightbulb.svg"
            > Some
            helpful tips and tricks
          </h6>
          <div class="row">
            <div class="col-lg-12">
              <p class="not-as-small text-muted mb-0">
                You can now customize your helpticket by adding custom forms and
                fields. Below is current structure of your custom helpticket
                forms. Then drag to move the custom fields to set their postion
                within the form.
              </p>
            </div>
          </div>
        </div>
      </dismissible-container>
    </div>

    <div
      class="d-flex align-items-center bg-themed-box-bg border-bottom pb-2 mb-4 align-items-end sticky-header"
      :class="{ 'pt-5': !isMsp }"
    >
      <div class="d-flex col-xl-7 col-md-6">
        <div class="position-relative mr-3">
          <i class="genuicon-build-custom-forms rounded-lg text-white bg-helpdesk"/>
        </div>
        <div>
          <div
            v-if="currentForm"
            class="d-flex align-items-center"
          >
            <h3 class="mb-0 form-title mr-3">
              {{ currentForm.formName }}
            </h3>
            <div
              v-if="currentForm.default"
              class="px-2 rounded-pill truncate true-small font-weight-semi-bold text-light bg-info-dark"
            >
              <i class="genuicon-star mr-0.5 ml-n0.5 smallest"/>
              Default {{ moduleTitle }}
              Form
            </div>
          </div>
          <div 
            v-if="isHelpDeskModule && !isMsp"
            class="d-flex flex-wrap align-items-center"
          >
            <a
              class="btn btn-link btn-xs true-small text-secondary ml-n2 mr-1 py-0 flatten-line-height"
              :href="helpCenterURL"
              target="_blank"
            >
              <i class="nulodgicon-external-link mr-1 not-as-small my-n2 d-inline-block"/>
              {{ helpCenterURL }}
            </a>
            <a 
              class="btn btn-link btn-xs true-small text-secondary ml-n2 py-0" 
              href="/help_tickets/settings/email_notifications"
              target="_blank"
            >
              <i class="genuicon-build-email-templates mr-1 not-as-small my-n2 d-inline-block"/>
              {{ inboundEmail }}
            </a>
          </div>
          <div
            v-if="isFormArchived"
            class="mt-1"
          >
            <small class="font-weight-semi-bold text-danger not-as-small">
              archived
            </small>
          </div>
        </div>
      </div>
      <div
        v-if="!formSelectOpen"
        class="col-xl-5 col-md-6 row align-items-center justify-content-end pr-0"
      >
        <span class="mb-2">
          <a
            v-if="!isMspForm"
            v-tooltip="'Switch to Different Form'"
            class="change-link mx-3 small"
            href="#"
            data-tc-link="change form"
            @click.prevent.stop="openFormSelect"
          >
            Switch Custom Form
            <i class="nulodgicon-arrow-down-b arrow-down d-inline-block align-middle" />
          </a>
        </span>
        <a
          v-if="!isMspForm"
          class="mt-2 mr-1 text-secondary back-to-link float-right pt-2"
          role="button"
          href="#"
          @click.prevent.stop="goToCustomFormIndex(true)"
        >
          <i class="nulodgicon-arrow-left-c white mr-2" />
          <span data-tc="back to custom forms">Back to <strong>Custom Forms</strong></span>
        </a>
        <span v-if="!isHelpCenterMode">
          <button
            v-if="showRemoveButton && companyModule != 'company_user'"
            v-tooltip="'Delete Form'"
            class="btn btn-sm text-danger edit-delete-btn delete ml-3"
            data-tc-delete-custom-form="Help desk"
            @click.prevent="openRemoveFormModal"
          >
            <i class="nulodgicon-trash-b" />
          </button>
          <button
            v-if="showArchiveButton && !isMspForm"
            v-tooltip="'Archive Form'"
            class="btn btn-sm text-secondary edit-delete-btn archive ml-3"
            data-tc-archieve-custom-form="Help desk"
            @click.prevent="openArchiveFormModal"
          >
            <i class="nulodgicon-archive" />
          </button>
          <button
            v-if="showRemoveButton"
            v-tooltip="'Unarchive Form'"
            class="btn btn-sm text-secondary edit-delete-btn unarchive ml-3"
            data-tc-unarchieve-custom-form="Help desk"
            @click.prevent="openArchiveFormModal"
          >
            <i class="nulodgicon-unarchive" />
          </button>
        </span>
      </div>
      <multi-select
        v-else-if="formOptions"
        id="custom_forms"
        class="mb-2"
        label="name"
        :value="currentForm"
        show-labels
        :searchable="false"
        :allow-empty="false"
        :options="formOptions"
        :loading="!!!formOptions"
        placeholder="Search for or type in a custom form"
        tag-placeholder="Select a custom form"
        track-by="id"
        data-tc-drop-down="forms"
        @open="openFormSelect"
        @close="closeFormSelect"
        @input="formSwitch"
      >
        <template
          slot="option"
          slot-scope="props"
        >
          {{ props.option.name }}
          <span
            v-if="!props.option.isActive"
            class="ml-2 small text-danger"
          >
            archived
          </span>
        </template>
      </multi-select>
    </div>

    <div
      v-if="companyModule"
      :class="{ 'pt-2' : !isHelpDeskModule }"
      data-tc-help-desk-frame
    >
      <form
        v-if="currentForm"
        id="custom_form"
        class="row custom-form"
        accept-charset="UTF-8"
        enctype="multipart/form-data"
      >
        <div
          class="col-xl-8 col-md-12"
          :class="{'sticky-builder' : !isMsp}"
        >
          <div class="p-0 box box--natural-height">
            <div class="box__inner">
              <div class="d-flex py-3 border-bottom px-4 rounded-top align-items-end">
                <h5 class="font-weight-normal mb-0">
                  Customize Your {{ titleize(formType) }} Form
                </h5>
                <div class="ml-auto mr-n2">
                  <button 
                    v-if="!isFormSetup"
                    class="btn btn-outline-light btn-sm px-3 mr-3"
                    :disabled="!isAnyChange"
                    @click.prevent.stop="resetForm"
                  >
                    Clear Unsaved Changes
                  </button>
                  <button 
                    v-if="!isMsp"
                    class="btn btn-dark btn-sm px-3 btn-flat"
                    @click.prevent.stop="openPreviewFormModal"
                  >
                    Preview Form
                  </button>
                </div>
              </div>
              <div class="row align-items-start builder-section">
                <div class="col-auto pr-0 d-flex mh-100">
                  <div
                    class="pl-4 py-4 bg-lighter not-as-small border-right field-palette"
                    :class="isMsp ? 'pr-3' : 'pr-5'"
                  >
                    <field-palette :company-module="companyModule" />                                        
                  </div>
                </div>
                <div class="col pl-0 d-flex mh-100">
                  <div
                    class="w-100 active-form-elements" 
                    :class="{'pt-5 px-3': isHelpDeskModule}"
                  >
                    <div class="box__inner mt-n2.5">
                      <ul class="nav nav-tabs">
                        <li class="nav-item">
                          <a
                            v-if="isHelpDeskModule"
                            class="nav-link"
                            :class="{ active: !isHelpCenterMode }"
                            href="#"
                            @click="setViewType('Internal')"
                          >
                            Internal
                          </a>
                        </li>
                        <li
                          v-if="isHelpDeskModule && !isMsp"
                          class="nav-item"
                        >
                          <a
                            class="nav-link"
                            :class="{ active: isHelpCenterMode }"
                            href="#"
                            data-tc-tab="help center"
                            @click="setViewType('Help Center')"
                          >
                            Help Center
                          </a>
                        </li>
                      </ul>
                      <div>
                        <div
                          v-if="currentForm"
                          class="bg-themed-box-bg"
                          :class="{'px-4 pb-5 pt-2': !isHelpDeskModule}"
                        >
                          <div class="box__inner px-1">
                            <div class="bg-cyan-subtle text-info rounded p-3 font-weight-semi-bold true-small my-4">
                              <div class="row">
                                <div class="col">
                                  <p
                                    v-if="isHelpCenterMode"
                                    class="p--responsive"
                                  >
                                    This is how your form below will look when you create a new ticket in the Help Center.
                                    <a
                                      href="#"
                                      @click.stop.prevent="readMore = !readMore"
                                    >
                                      <strong>Important, please read</strong>
                                      <i
                                        v-if="readMore"
                                        class="nulodgicon-chevron-down ml-1 small"
                                      />
                                      <i
                                        v-else
                                        class="nulodgicon-chevron-right ml-1 small"
                                      />
                                    </a>
                                  </p>
                                  <div v-if="isHelpCenterMode && readMore">
                                    <p class="p--responsive">
                                      The Help Center is publicly accessible at
                                      <a
                                        :href="helpCenterURL"
                                        target="_blank"
                                      >
                                        {{ helpCenterURL }}
                                      </a>
                                      for quick and easy ticket submission. Smart fields below
                                      that access company data such as People List, Asset List,
                                      etc. are hidden by default. You can unhide them by
                                      toggling the
                                      <i class="nulodgicon-eye-disabled" /> icon. Please be
                                      aware that when you make a field visible, this field is
                                      open and accessible to anybody with the URL above, so
                                      please use your discretion.
                                    </p>
                                  </div>
                                  <p
                                    v-if="!isHelpCenterMode"
                                    class="p--responsive"
                                  >
                                    This is how your form below will look when you create a
                                    new {{ getModuleData.companyModuleType }}
                                    <template v-if="isHelpDeskModule">
                                      within the {{ getModuleData.label }} while logged in.
                                    </template>
                                  </p>
                                  <p
                                    v-if="!isHelpCenterMode"
                                    class="mb-0 p--responsive"
                                  >
                                    <strong>Note:</strong> This is the form builder, the
                                    fields on this page are <strong>not</strong> functional.
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              v-if="filteredHeaderField"
                              class="form-group bg-themed-box-bg rounded mb-0"
                            >
                              <h6 class="mb-3 h6--responsive">
                                Header Section
                              </h6>
                              <div
                                v-for="(formField, idx) in filteredHeaderField"
                                :key="`field-${idx}`"
                                class="form-group bg-themed-box-bg rounded active-form-element"
                                :class="{ 'mb-0': subjectField(formField) }"
                              >
                                <field-builder-input
                                  ref="headerFieldValidation"
                                  :form="currentForm"
                                  :form-field="formField"
                                  :is-preview-only="false"
                                  :is-help-center-mode="isHelpCenterMode"
                                  :attribute-types="attributeTypes"
                                  :is-followers-present="isFollowersPresent"
                                  disable-drag
                                  :disable-delete="isDeleteDisabled(formField)"
                                  :disable-permissions="arePermissionsDisabled(formField)"
                                  @remove="removeFormField"
                                  @input="updateFormField"
                                />
                              </div>
                              <hr class="mt-4">
                            </div>
                            <h6 class="mt-4 h6--responsive">
                              Customizable Section
                              <span
                                class="small text-secondary ml-1 p--responsive"
                              >Drag additional fields below.</span>
                            </h6>
                            <draggable
                              v-model="filteredFormFields"
                              handle=".handle"
                              v-bind="dragOptions"
                              animation="100"
                              group="fieldOptions"
                              dragover-bubble="true"
                              empty-insert-threshold="40"
                              class="py-3"
                              data-tc-drop-element-sections
                              :scroll-sensitivity="50"
                              :scroll-speed="50"
                              @change="updateList"
                              @start="drag = true"
                            >
                              <transition-group
                                type="transition"
                                :name="!drag ? 'flip-list' : null"
                              >
                                <div
                                  v-for="(formField, idx) in filteredFormFields"
                                  :key="`field-${idx}`"
                                  class="form-group handle px-3 pt-2.5 pb-3 rounded active-form-element"
                                  :class="{ 'mb-0': subjectField(formField) }"
                                  :style="{
                                    boxShadow: isHelpCenterMode && formField.private ? 'inset 0 0 0 1px #ffb648' : '',
                                    cursor:'move',
                                  }"
                                >
                                  <field-builder-input
                                    ref="fieldValidation"
                                    :form="currentForm"
                                    :form-field="formField"
                                    :attribute-types="attributeTypes"
                                    :is-preview-only="false"
                                    :is-help-center-mode="isHelpCenterMode"
                                    :disable-drag="isDragDisabled(formField)"
                                    :disable-delete="isDeleteDisabled(formField)"
                                    :disable-permissions="arePermissionsDisabled(formField)"
                                    :is-msp-form="isMspForm"
                                    :all-fields="allFormFields"
                                    :is-followers-present="isFollowersPresent"
                                    @reset-permissions="resetPermissions"
                                    @remove="removeFormField"
                                    @input="updateFormField"
                                  />
                                </div>
                              </transition-group>
                            </draggable>
                            <span
                              v-if="hasUnlabeledField"
                              class="form-text text-danger not-as-small"
                              data-tc-form-has-labels-warning
                            >
                              Please make sure all of the form fields have labels.
                            </span>
                            <span
                              v-if="duplicateFormName"
                              class="form-text text-danger not-as-small"
                              data-tc-form-has-labels-warning
                            >
                              Form name has already been taken.
                            </span>                            
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class=" col-xl-4 col-md-12 mb-6">
          <div class="setting-section">
            <div
              class="box box--flat px-5 py-4 border bg-themed-box-bg"
              :class="{'bg-light mb-5 ': isFormSetup}"
            >
              <div class="box__inner">
                <div
                  class="d-flex align-items-end "
                  :class="{'mx-n5 bg-themed-dark-drawer-bg text-white py-3 mt-n4 rounded-top px-4 mb-4': isFormSetup}"
                >
                  <h5 class="font-weight-normal mb-0">
                    {{isFormSetup? 'Form Set Up' : 'Primary Settings'}}
                  </h5>
                </div>
                <hr 
                  v-if="!isFormSetup"
                  class="mb-4 mt-3 mx-n2.5"
                >
                <div>
                  <div class="form-group">
                    <label class="mb-0">
                      Form Name
                    </label>
                    <div class="text-secondary true-small mb-2">
                      Give your form a simple 
                      and descriptive name...
                    </div>
                    <input
                      v-model="currentForm.formName"
                      type="text"
                      class="form-control"
                      :maxlength="'30'"
                      :disabled="currentForm.isTaskLinked"
                      data-tc-form-name
                    >
                    <div
                      v-if="noName"
                      class="text-danger small mt-1"
                    >
                      Form name can't be empty.
                    </div>
                    <div
                      v-if="currentForm.isTaskLinked"
                      class="text-very-muted help-center-link mt-2 d-flex"
                    >
                      <i class="genuicon-info-circled align-middle" />
                      <span class="text-info small ml-1">
                        This form is linked to an automated task,
                        which is why you can't change its name.
                      </span>
                    </div>
                  </div>
                  <div v-if="isHelpDeskModule && !isMsp">
                    <div class="form-group">
                      <label class="mb-0">
                        Inbound Email Address
                      </label>
                      <div class="text-secondary true-small mb-2">
                        Emails sent to this address
                        will create new tickets.
                      </div>
                      <div
                        v-tooltip="inboundEmail"
                        class="input-group"
                      >
                        <input 
                          id="genuity_email_address"
                          type="text"
                          required="required"
                          class="form-control"
                          :value="emailLocalPart"
                          disabled
                        >
                        <div class="input-group-append">
                          <div class="input-group-text smallest font-weight-semi-bold px-2 truncate">
                            @{{ currentDomain() }}
                          </div>
                        </div>
                      </div>
                      <span class="smallest text-muted mt-2">
                        Manage all inbound emails in 
                        <a 
                          href="/help_tickets/settings/email_notifications"
                          class="smallest"
                          target="_blank"
                        >
                          <code class="smallest px-1 ml-0.5 py-0">
                            Settings &gt; Email
                          </code>
                        </a>
                      </span>
                    </div>
                  </div>
                  <hr>
                  <div v-if="isPeopleModule">
                    <div class="form-group row align-items-center mb-0">
                      <div class="col">
                        <label class="mb-1">
                          Form Icon
                        </label>
                        <div class="true-small text-secondary">
                          Select an icon to associate with this form.
                        </div>
                      </div>
                      <div class="col-auto">
                        <img
                          v-click-outside="closeIconSelectionDropdown"
                          class="border rounded p-2 ml-2 cursor-pointer"
                          :src="currentForm.icon"
                          height="50"
                          width="50"
                          @click="toggleIconSelectionDropdown"
                        >
                      </div>
                    </div>
                    <div
                      class="dropdown-menu icon-selection-dropdown pr-3"
                      :class="{ 'show': showIconSelectionDropdown }"
                    >
                      <div class="row icon-selection-grid">
                        <span
                          v-for="(icon, index) in iconDefaults"
                          :key="index"
                          class="col-auto m-2 pr-0"
                        >
                          <img
                            class="border rounded cursor-pointer p-2"
                            :class="{'selected': currentForm.icon == icon}"
                            :src="icon"
                            height="50"
                            width="50"
                            @click.stop.prevent="setSelectedIcon(icon)"
                          >
                        </span>
                      </div>
                    </div>
                    <hr>
                    <div class="form-group row align-items-center mt-3">
                      <div class="col">
                        <label class="mb-1">
                          Form Color
                        </label>
                        <div class="true-small text-secondary">
                          Select a color to label this form.
                        </div>
                      </div>
                      <div class="col-auto">
                        <v-input-colorpicker
                          v-model="currentForm.color"
                          class="color-picker mt-2 ml-2"
                        />
                      </div>
                    </div>
                    <hr>
                    <div class="form-group row align-items-center mt-3">
                      <div class="col">
                        <label class="mb-1">
                          Form Description
                        </label>
                        <div class="true-small text-secondary">
                          A short description for this form.
                        </div>
                      </div>
                      <div class="col">
                        <input
                          v-model="currentForm.description"
                          type="text"
                          class="form-control description-input d-inline ml-2"
                        >
                      </div>
                    </div>
                    <hr>
                  </div>
                  <div class="form-group row align-items-center mb-0">
                    <div class="col">
                      <label class="mb-1">
                        Use this form as the default for
                        {{ moduleTitle }}
                      </label>
                    </div>
                    <div class="col-auto">
                      <material-toggle
                        :init-active="currentForm.default"
                        @toggle-sample="currentForm.default = $event"
                      />
                    </div>
                  </div>
                  <div 
                    v-if="companyModule === 'helpdesk'" 
                    class="text-muted small mt-2"
                  >
                    Default automated tasks will be automatically linked with this form on selecting it as default.
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="isHelpDeskModule && !isMsp"
              class="bg-lighter border p-4 rounded additional-setting"
            >
              <h5 class="font-weight-normal mb-0">
                Additional Settings
              </h5>
              <hr class="my-2.5">
              <div v-if="isHelpDeskModule">
                <helpdesk-form
                  v-if="currentForm && currentForm.moduleForm"
                  ref="helpdeskForm"
                  :form="currentForm"
                  :contributor-error="contributorError"
                  :display-survey-section="displaySurveySection"
                  :display-survey-settings="displaySurveySettings"
                  @input="updateModuleForm"
                />
              </div>
              <div>
                <hr class="my-2.5">
                <div class="form-group btn btn-link mb-0 text-left d-block white-space-normal mx-n2 border-0 clickable">
                  <div class="row align-items-center">
                    <div class="col pr-4">
                      <label class="mb-1 clickable">
                        Allow this form in the Help Center
                      </label>
                      <div class="true-small text-secondary">
                        Enable this form to be accessible in the Help Center.
                      </div>
                      <span>
                        <div 
                          class="background-light-gray p-2 left-border font-color"
                          :class="borderClass"
                        >
                          Access to public articles by unauthenticated users is 
                          <span :class="accessStatusClass">
                            {{ isPublicAccessEnabled ? 'enabled' : 'disabled' }}
                          </span>
                          for this workspace in your
                          <router-link
                            target="_blank"
                            :to="{ path: '/settings/help_center/display', query: { highlight: 'allow-public-custom-forms-to-logged-out-users'} }"
                          >
                            Help Center Settings
                          </router-link>.
                        </div>
                      </span>  
                    </div>
                    <div class="col-auto">
                      <material-toggle
                        :init-active="currentForm.moduleForm.showInOpenPortal"
                        @toggle-sample="currentForm.moduleForm.showInOpenPortal = $event"
                      />
                    </div>
                  </div>
                </div>
                <hr class="my-2.5">
                <div class="form-group btn btn-link mb-0 text-left d-block white-space-normal mx-n2 border-0 clickable">
                  <div class="row align-items-center">
                    <div class="col pr-4">
                      <label class="mb-1 clickable">
                        Show People list for Unauthenticated Users
                      </label>
                      <div class="true-small text-secondary">
                        Allow unauthenticated users to view the people list.
                      </div>
                    </div>
                    <div class="col-auto">
                      <material-toggle
                        :init-active="currentForm.moduleForm.showPeopleList"
                        @toggle-sample="currentForm.moduleForm.showPeopleList = $event"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="sticky-btn-holder py-3 bg-light text-center border-top border-light">
          <button
            v-if="isFormSetup && !isMsp"
            class="btn btn-lighter text-dark mr-3 px-5 btn-shadow" 
            :disabled="noName"
            @click.prevent="submitForm(false, true)"
          >
            Save as Draft
          </button>
          <submit-button
            v-if="isMsp && modifyOnce"
            btn-content="Assign to Company"
            :btn-classes="'mt-3 px-5'"
            :is-saving="attachmentUploading"
            :disabled="noName"
            :is-validated="!noName"
            @submit="assignToCompany"
          />
          <submit-button
            v-else
            :btn-classes="'px-5'"
            :is-saving="disabledSubmit"
            :disabled="noName"
            :is-validated="!noName"
            :btn-content="saveForm"
            :saving-content="savingForm"
            @submit="submitForm(false, false)"
          />
        </div>
      </form>

      <archive-form-modal
        v-if="currentForm"
        ref="archiveFormModal"
        :form="currentForm"
        :company-module="companyModule"
        :is-task-linked="currentForm.isTaskLinked"
        @archive="archiveForm"
        @unarchive="unarchiveForm"
      />
      
      <confirm-add-form-modal
        v-if="currentForm"
        ref="confirmAddFormModal"
        :form="currentForm"
      />

      <form-tickets-modal
        ref="formTicketsModal"
        :form="currentForm"
        :form-tickets="formTickets"
        @update="submitForm(true)"
      />

      <select-default-form-modal
        ref="selectDefaultFormModal"
        :form="currentForm"
        :company-module="companyModule"
        @update="setNewDefaultFormId"
      />

      <remove-form-modal
        v-if="currentForm && currentForm.id"
        ref="removeFormModal"
        :forms="[ currentForm ]"
        :company-module="companyModule"
        @remove="removeForm"
      />

      <warning-modal
        v-if="currentForm && currentForm.id && showWarningBox"
        ref="warningModal"
        @update:showWarningBox="showWarningBox = $event"
        @back-to="goToCustomFormIndex"
      />

      <preview-form-modal
        v-if="currentForm"
        ref="previewFormModal"
        :form="currentForm"
        :company-module="companyModule"
        :form-type="formType"
        :form-label="formLabel"
        :is-help-desk-module="isHelpDeskModule"
      />
      
      <Teleport to="body">
        <loader-modal
          v-if="currentForm"
          ref="formLoaderModal"
        />
      </Teleport>
      <Teleport to="body">
        <copy-destination-modal
          v-if="isMsp && modifyOnce"
          ref="copyModal"
          :selected-items="selected"
          @copy-completed="$emit('close-modal')"
        />
      </Teleport>

      <Teleport to="body">
        <sweet-modal
          ref="deleteFieldWarningModal"
          v-sweet-esc
          title="Before you delete this field..."
          @close="closeDeleteFieldWarningModal"
        >
          <template slot="default">
            <div class="text-center">
              <p>This field has values in multiple tickets. If you delete it, you will lose all those values as well. Are you sure you want to delete this field?</p>
            </div>
          </template>
          <button
            slot="button"
            class="btn btn-link text-secondary"
            @click.stop="handleClose"
          >
            Cancel
          </button>
          <button
            slot="button"
            class="btn btn-link text-danger"
            @click.stop="deleteFormField"
          >
            Delete
          </button>
        </sweet-modal>
      </Teleport>
    </div>
  </div>
</template>

<script>
import http from "common/http";
import Draggable from "vuedraggable";
import { mapActions, mapGetters, mapMutations } from "vuex";
import MultiSelect from "vue-multiselect";
import vClickOutside from 'v-click-outside';

import customFormOptions from 'mixins/options/custom_forms';
import arrayComparisonHandler from "mixins/array_comparison_handler";
import customFormHelper from "mixins/custom_form_helper";
import permissionsHelper from "mixins/permissions_helper";
import string from 'mixins/string';
import customForms from 'mixins/custom_forms';

import _cloneDeep from "lodash/cloneDeep";
import _get from "lodash/get";
import _map from 'lodash/map';
import _filter from "lodash/filter";
import _sortBy from "lodash/sortBy";
import _reject from "lodash/reject";
import _some from "lodash/some";
import _camelCase from "lodash/camelCase";

import VInputColorpicker from "vue-native-color-picker";
import CopyDestinationModal from 'components/related_companies/copy_destination_modal.vue';
import MaterialToggle from 'components/shared/material_toggle.vue';
import { SweetModal } from 'sweet-modal-vue';
import subscription from "../../../stores/mixins/subscription";
import SubmitButton from "../submit_button.vue";
import FieldBuilderInput from "./field_builder_input.vue";
import HelpdeskForm from "./module_forms/help_desk.vue";
import DismissibleContainer from "../dismissible_container.vue";
import FieldPalette from "./field_palette.vue";
import SelectDefaultFormModal from "./modals/select_default_form_modal.vue";
import RemoveFormModal from "./modals/remove_form_modal.vue";
import FormTicketsModal from "./modals/form_tickets_modal.vue";
import ConfirmAddFormModal from "./modals/confirm_add_form_modal.vue";
import ArchiveFormModal from "./modals/archive_form_modal.vue";
import WarningModal from "./modals/warning_modal.vue";
import loaderModal from "./form_update_loader_modal.vue";
import PreviewFormModal from "./modals/preview_form_modal.vue";

export default {
  $_veeValidate: {
    validator: "new",
  },
  directives: {
    clickOutside: vClickOutside.directive,
  },
  components: {
    ArchiveFormModal,
    ConfirmAddFormModal,
    DismissibleContainer,
    Draggable,
    FieldBuilderInput,
    FieldPalette,
    FormTicketsModal,
    HelpdeskForm,
    MultiSelect,
    RemoveFormModal,
    SelectDefaultFormModal,
    SubmitButton,
    WarningModal,
    loaderModal,
    PreviewFormModal,
    VInputColorpicker,
    CopyDestinationModal,
    MaterialToggle,
    SweetModal,
  },
  mixins: [
    customFormOptions,
    customFormHelper,
    permissionsHelper,
    string,
    arrayComparisonHandler,
    customForms,
    subscription,
  ],
  props: {
    isMsp: {
      type: Boolean,
      default: false,
    },
    buildId: {
      type: Number,
      default: null,
    },
    pathName: {
      type: String,
      default: "",
    },
    currentFormModule: {
      type: String,
      default: "",
    },
    item: {
      type: Object,
      default: () => {},
    },
    modifyOnce: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      unarchiveOnlyStaffForm: true,
      isHintOpen: true,
      currentId: null,
      drag: false,
      companyModule: '',
      companyModuleFormatted: '',
      formSelectOpen: false,
      currentFormAttribute: null,
      attributeTypes: [],
      hasUnsavedChanges: false,
      moduleFormValid: true,
      isSaving: false,
      currentForm: null,
      formTickets: null,
      mspCustomForm: this.item,
      newFormNameDefault: "New Custom Form",
      newFormFields: [
        "fieldAttributeType",
        "orderPosition",
        "label",
        "name",
        "options",
        "required",
        "note",
        "fieldPosition",
        "permitViewDefault",
        "permitEditDefault",
        "permissions",
        "private",
        "defaultValue",
        "sortList",
        "nameFormat",
      ],
      isHelpCenterMode: false,
      readMore: false,
      helpCenterURL: "",
      permissionsKey: 0,
      nullValue: null,
      newDefaultFormId: null,
      openEditAttrModal: false,
      fieldPositionDifference: {
        'helpdesk': 1,
        'location': 1,
        'company_user': 3,
      },
      modulesPath: {
        helpdesk: "/settings/custom_forms",
        location: "/custom_forms/locations",
        company_user: "/custom_forms/company_users",
      },
      contributorError: false,
      allFormFields: [],
      copiedCustomForm: [],
      changedPermissionFields: [],
      showIconSelectionDropdown: false,
      iconDefaults: [],
      selected: {
        customForms: [],
      },
      fieldToDelete: null,
      isPublicAccessEnabled: null,
    };
  },
  computed: {
    ...mapGetters({
      smartLists: 'customForms/smartLists',
      displaySurveySection: 'displaySurveySection',
      displaySurveySettings: 'displaySurveySettings',
    }),
    borderClass() {
      return this.isPublicAccessEnabled ? 'border-left-green' : 'border-left-yellow';
    },
    accessStatusClass() {
      const base = 'p-2 rounded';
      return this.isPublicAccessEnabled ? `${base} bg-green-100` : `${base} bg-yellow-100`;
    },
    showRemoveButton() {
      return this.currentForm &&
              this.currentForm.id &&
              this.isFormArchived;
    },
    showArchiveButton() {
      return this.currentForm &&
              this.currentForm.id &&
              !this.isFormArchived;
    },
    isFormSetup() {
      return this.currentForm && !this.currentForm.id;
    },
    formType() {
        return _get(this, 'getModuleData.companyModuleType', '');
    },
    formLabel() {
      return _get(this, 'getModuleData.label', '');
    },
    isMspForm() {
      return this.isMsp;
    },
    fetchCustomFormPath() {
      if (this.isMsp) {
        return `/msp/templates/custom_forms`;
      }
      return `/custom_forms`;
    },
    formRendorPath() {
      if (this.isMsp) {
        const prevPath = new URL(document.referrer).pathname;
        return prevPath.substring(prevPath.indexOf('/company_templates'));
      }
      return this.modulesPath[this.companyModule];
    },
    formConstants() {
      const formWithCompanyField = this.isPeopleAndCompanyNameFieldForm(this.currentForm.formName);

      return {
        headerSection: formWithCompanyField ? 'HEADER_SECTION_WITH_COMPANY' : 'HEADER_SECTION',
        disabledDelete: formWithCompanyField ? 'DISABLED_DELETE_WITH_COMPANY' : 'DISABLED_DELETE',
      };
    },
    // TODO: 
    // - Refactor this method to prevent side effects (lies 762 & 765)
    // - Add a consistent return to the forEach loop (line 769)
    valid() {
      let isFormValid = this.moduleFormValid;
      if (this.isPeopleModule) {
        const descriptionLength = this.currentForm.description.length;

        if (descriptionLength > 50) {
          this.emitError('Form description cannot be greater than 50 letters.');
          isFormValid = false;
        } else if (!descriptionLength) {
          this.emitError('Form description cannot be empty.');
          isFormValid = false;
        }
      }
      if (!this.contributorsPresent) {
        this.emitError('Please select at least one contributor for Automatic Assignment');
        isFormValid = false;
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.contributorError = true;
      } else {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.contributorError = false;
      }
      // eslint-disable-next-line consistent-return
      this.currentForm.formFields.forEach((field, index) => {
        const isHeaderSection = this[this.companyModuleFormatted][this.formConstants.headerSection].includes(field.name);

        if (isHeaderSection) {
          if (!this.$refs.headerFieldValidation[index].checkValidation(field)) {
            this.emitError(`Please enter all required fields for ${field.label}`);
            isFormValid = false;
            return false;
          }
        } else if (!this.$refs.fieldValidation[index-this.$refs.headerFieldValidation.length].checkValidation(field)) {
          if (field.name === "city") {
            this.emitError(`The default value for ${field.label} may only contain alphabetic characters as well as spaces`);
          } else {
            this.emitError(`Please enter all required fields for ${field.label}`);
          }
          isFormValid = false;
          return false;
        }
        if (field.name === "created_by") {
          let defaultCreator = field.defaultValue;

          if (typeof defaultCreator === "string") {
            defaultCreator = JSON.parse(defaultCreator);
          }  
          isFormValid = Array.isArray(defaultCreator) && defaultCreator.every(userId => 
            Number.isInteger(userId) || userId.toLowerCase() === "current"
          );
          if (!isFormValid) {
            this.emitError(`The default value for ${field.label} may only contain valid user IDs or "current" for current user.`);
          }
        }
      });
      return isFormValid;
    },
    isFormArchived() {
      return !_get(this, "currentForm.isActive", true);
    },
    dragOptions() {
      return {
        animation: 200,
        disabled: false,
        ghostClass: "ghost",
        group: "attributeTypes",
      };
    },
    filteredHeaderField() {
      return _filter(this.currentForm.formFields, (field) =>
        this[this.companyModuleFormatted][this.formConstants.headerSection].includes(field.name)
      );
    },
    filteredFormFields: {
      get() {
        const fields = this.currentForm.formFields.filter((field) =>
          !this[this.companyModuleFormatted][this.formConstants.headerSection].includes(field.name)
        );
        const filteredFields = this.filterSubscriptions(fields);
        return _sortBy(filteredFields, "orderPosition");
      },
      set(val) {
        this.hasUnsavedChanges = true;
        val.unshift(...this.filteredHeaderField);
        for (let idx = 0; idx < val.length; idx += 1) {
          val[idx].orderPosition = idx;
        }
        this.currentForm.formFields = val;
      },
    },
    hasUnlabeledField() {
      return _some(this.currentForm.formFields, {
        label: "Add a label",
      });
    },
    duplicateFormName() {
      let forms = this.formOptions;
      if (this.currentForm.id) {
        forms = _reject(forms, ["id", this.currentForm.id]);
      }
      return _some(forms, (item) => item.name.toLowerCase() === this.currentForm.formName.toLowerCase());
    },
    noFields() {
      return this.currentForm.formFields.length === 0;
    },
    noName() {
      return this.currentForm.formName.trim().length === 0;
    },
    disabledSubmit() {
      return (
        this.isSaving ||
        this.duplicateFormName ||
        this.noFields ||
        (this.hasVisibleSmartListFields &&
          this.currentForm.moduleForm &&
          !this.currentForm.moduleForm.verifyVisibleSmartListFields &&
          this.allowsViewableInPortal)
      );
    },
    smartFieldList() {
      if (
        this.currentForm &&
        this.currentForm.formFields.length > 0
      ) {
        return _filter(
          this.currentForm.formFields,
          (field) =>
            this.smartLists.includes(field.fieldAttributeType) && !field.private && this.isHelpDeskModule
        );
      }
      return [];
    },
    hasVisibleSmartListFields() {
      return this.smartFieldList.length > 0;
    },
    allowsViewableInPortal() {
      return this.currentForm.showInOpenPortal;
    },
    contributorsPresent() {
      if (this.companyModule === 'helpdesk' && !this.isMsp) {
        const status = this.currentForm.automatedAssignmentStatus;
        const contributors = this.currentForm.moduleForm.selectedContributors;
        if (status && (!contributors || contributors.length < 1 || !contributors[0].id)) {
          return false;
        }
      }
      return true;
    },
    saveForm() {
      if (this.currentForm.id) {
        return 'Save & Publish Form';
      }
      return 'Create & Publish Form';
    },
    savingForm() {
      if (this.currentForm.id) {
        return 'Saving Form';
      }
      return 'Creating Form';
    },
    isPeopleModule() {
      return this.companyModule === 'company_user';
    },
    isFollowersPresent() {
      return this.currentForm.formFields.some(field => field.name === 'followers');
    },
    inboundEmail() {
      if (this.currentForm?.moduleForm) {
        const { email: defaultEmail, customEmail } = this.currentForm.moduleForm;
         if (
          this.$route.query.workspaceName &&
          this.$route.query.workspaceName !== getWorkspaceFromStorage().name
        ) {
          return this.workspaceInboundEmail(this.currentForm.formName);
        }

        return customEmail?.email || defaultEmail || this.newEmail;
      }
      return '';
    },
    newEmail() {
      const workspaceName = this.toHyphenCase(getWorkspaceFromStorage().name);
      const formName = this.toHyphenCase(this.currentForm?.formName);
      return `${workspaceName}-${formName}@${this.currentDomain()}`;
    },
    emailLocalPart() {
      return this.inboundEmail.split('@')[0];
    },
    isAnyChange() {
      return this.isFormValuesChanged(this.currentForm, this.copiedCustomForm) || this.hasUnsavedChanges===true;
    },
    footer() {
      return document.querySelector('footer');
    },
    verticalNav() {
      return document.querySelector('.vertical-theme-toggle');
    },
    moduleTitle() {
      const workspaceName = this.currentForm?.workspace?.name || '';
      return this.isHelpDeskModule
        ? this.toTitle(workspaceName)
        : this.toTitle(this.companyModule);
    },
  },
  watch: {
    item: {
      handler() {
        this.mspCustomForm = _cloneDeep(this.item);
      },
      deep: true,
    },
  },
  created() {
    this.addUniqueFormNameValidation();
    if (this.footer) {
      this.footer.style.display = 'none';
    };
    if (this.verticalNav) {
      this.verticalNav.style.display = 'none';
    };
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.beforeWindowUnload);
    if (this.footer) {
      this.footer.style.display = '';
    };
    if (this.verticalNav) {
      this.verticalNav.style.display = '';
    };
  },
  methods: {
    ...mapMutations(["setLoadingStatus"]),
    ...mapActions('GlobalStore', ['fetchCustomFormOptions']),
    getCustomFormOptions() {
      const params = {
        company_module: this.companyModule,
        active: true,
        company_id: this.companyId,
      };
      this.fetchCustomFormOptions(params);
    },
    openFormLoaderModal() {
      this.$refs.formLoaderModal.$refs.modal.open();
    },
    assignToCompany() {
      if (this.valid) {
        this.convertFieldOptions();
        this.convertSmartLisSortPreference();
        const form = this.initFormSubmit();
        this.selected.customForms = [form];
        this.$refs.copyModal.open();
      }
    },
    toggleIconSelectionDropdown() {
      this.showIconSelectionDropdown = !this.showIconSelectionDropdown;
    },
    gatherIcons() {
      for (let idx = 1; idx < 44; idx += 1) {
        this.iconDefaults.push(`https://s3.amazonaws.com/nulodgic-static-assets/images/people_defaults/default${idx}_thumbnail.png`);
      }
    },
    formDescription(template) {
      if (template.formName === 'Blank State') {
        return 'A handy form for your company.';
      }
      return template.description;
    },
    setSelectedIcon(icon) {
      this.currentForm.icon = icon;
    },
    closeIconSelectionDropdown() {
      this.showIconSelectionDropdown = false;
    },
    formLoaderModalRef() {
      return this.$refs.formLoaderModal.$refs.modal;
    },
    onWorkspaceChange() {
      window.addEventListener('beforeunload', this.beforeWindowUnload);
      this.companyModule = this.isMsp ? this.currentFormModule : this.getCompanyModule;
      this.initFieldPermissions().then(() => {
        this.setCurrentForm();
      });
      this.companyModuleFormatted = _camelCase(this.companyModule);

      // If we have a current form AND the current form has a different company/workspace,
      // then redirect to the index page.
      if (this.currentForm) {
        const formCompany = this.currentForm.company;
        const formWorkspace = this.currentForm.workspace;
        const company = getCompanyFromStorage();
        const workspace = getWorkspaceFromStorage();
        if ((formCompany && formCompany.id !== company.id) || (formWorkspace && formWorkspace.id !== workspace.id)) {
          this.goToCustomFormIndex(true);
        }
      }
      this.companyId = parseInt(this.$currentCompanyId, 10);
      this.filterFormFields();
      this.fetchHelpdeskPortalURL();
      if (this.companyModule === 'helpdesk') {
        this.fetchAllFormFields();
        this.fetchHelpCenterSettings();
      }
      if (this.isPeopleModule) {
        this.gatherIcons();
      }
    },
    fetchHelpCenterSettings() {
      http
        .get('/helpdesk_settings.json')
        .then((res) => {
          const settingsArray = res.data.settings?.helpCenterSettings;
          const setting = settingsArray.find(n => n.name === 'Allow unauthenticated users to access the public Custom Forms');
          this.isPublicAccessEnabled = setting?.enabled;
        })
        .catch(() => {
          this.emitError('There was an issue fetching your helpdesk settings. Please refresh the page and try again.');
        });
    },
    createFormPath() {
      const url = `/custom_forms.json`;
      if (this.isMsp) {
        return `/msp/templates${url}`;
      }
      return url;
    },
    updateFormPath() {
      const url = `/custom_forms/${this.currentForm.id}.json`;
      if (this.isMsp) {
        return `/msp/templates${url}`;
      }
      return url;
    },
    goToCustomFormIndex(flag) {
      this.showWarningBox = false;
      if (flag && this.currentForm?.id && this.isFormValuesChanged(this.currentForm, this.copiedCustomForm)) {
        this.showWarningBox = true;
      } else if (this.isHelpDeskModule) {
        this.$router.push('/settings/custom_forms');
      } else {
        this.$router.go(-1);
      }
    },
    openFormSelect() {
      this.fetchFormOptions();
      this.formSelectOpen = true;
    },
    closeFormSelect() {
      this.formSelectOpen = false;
      this.formOptions = [];
    },
    fetchHelpdeskPortalURL() {
      http
        .get("/ticket_portal_url.json")
        .then((res) => {
          this.helpCenterURL = res.data.helpCenterUrl;
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error loading your Help Center URL.`);
        });
    },
    setViewType(type) {
      this.isHelpCenterMode = type === "Help Center";
    },
    setCurrentForm() {
      const { id } = this.isMsp ? {id: this.mspCustomForm?.id} : this.$route.params;
      const { formId } = this.$route.query;
      const templateId = parseInt(this.$route.query.templateId, 10);
      if (formId) {
        this.fetchCustomFormForCopy();
      } else if (templateId) {
        this.fetchCustomFormTemplateForCopy();
      } else if (id) {
        this.fetchCustomForm({ id });
      } else {
        this.fetchDefaultCustomFormTemplate();
      }
    },
    fetchCustomFormTemplateForCopy() {
      const id = this.$route.query.templateId;
      http
        .get(`/custom_form_templates/${id}.json`)
        .then((res) => {
          this.setCustomFormFromTemplate(res.data);
          this.formSelectOpen = false;
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error loading your form template.`);
        })
        .finally(() => {
          this.setLoadingStatus(false);
        });
    },
    fetchCustomFormForCopy() {
      const id = this.$route.query.formId;
      http
        .get(`/custom_forms/${id}.json`)
        .then((res) => {
          this.setCustomFormFromForm(res.data);
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error loading your custom form.`);
        })
        .finally(() => {
          this.setLoadingStatus(false);
        });
    },
    fetchCustomForm(form) {
      if (this.isMsp && this.mspCustomForm.id) {
        this.currentForm = this.mspCustomForm;
        this.copiedCustomForm = _cloneDeep(this.currentForm);
        this.companyId = this.currentForm.companyId;
        this.formSelectOpen = false;
        this.setLoadingStatus(false);
      } else {
        const params = { company_module: this.companyModule };
        http
          .get(`${this.fetchCustomFormPath}/${form.id}.json`, { params })
          .then((res) => {
            this.currentForm = res.data;
            this.copiedCustomForm = _cloneDeep(this.currentForm);
            this.companyId = this.currentForm.companyId;
            this.formSelectOpen = false;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading your custom form.`);
          })
          .finally(() => {
            this.setLoadingStatus(false);
          });
      }
    },
    isDragDisabled(field) {
      return this[this.companyModuleFormatted].DISABLED_DRAG.includes(field.name);
    },
    isDeleteDisabled(field) {
      return this[this.companyModuleFormatted][this.formConstants.disabledDelete].includes(field.name);
    },
    arePermissionsDisabled(field) {
      return this[this.companyModuleFormatted].DISABLED_PERMISSIONS.includes(field.name);
    },
    subjectField(field) {
      return field.name === "subject";
    },
    addUniqueFormNameValidation() {
      const isValid = (value) => new Promise((resolve) => {
        let duplicateName = null;
        if (this.formOptions && this.formOptions.length > 0) {
          duplicateName = this.formOptions.find(
            (form) => form.name.toLowerCase() === value.toLowerCase()
          );
        }
        if (duplicateName) {
          resolve({
            valid: false,
            data: {
              message: "Please be sure to enter a unique form name",
            },
          });
        } else {
          resolve({
            valid: true,
            data: {
              message: "",
            },
          });
        }
      });
      this.$validator.extend("isUnique", {
        getMessage: (field, params, data) => data.message,
        validate: isValid,
      });
    },
    removeForm() {
      this.closeRemoveFormModal();
      this.openFormLoaderModal();
      const loaderModalRef = this.formLoaderModalRef();
      const params = { company_module: this.companyModule };
      http
        .delete(`/custom_forms/${this.currentForm.id}.json`, { params })
        .then(() => {
          this.goToCustomFormIndex(false);
          this.emitSuccess("Successfully removed this custom form");
        })
        .catch((err) => {
          const msg = _get(err, "response.data.message", "");
          this.emitError(`There was an error removing the form. ${msg}`);
        })
        .finally(() => {
          loaderModalRef.close();
          this.isSaving = false;
        });
    },
    submitForm(force=false, isDraft=false) {
      if (this.valid) {
        this.convertFieldOptions();
        this.convertSmartLisSortPreference();
        const form = this.initFormSubmit(isDraft);
        this.openFormLoaderModal();
        if (form.id) {
          if (!force) {
            this.getFormTickets(form);
          } else {
            this.updateForm(form);
          }
        } else {
          this.createForm(form);
        }
      }
    },
    updateForm(form) {
      const loaderModalRef = this.formLoaderModalRef();
      const params = {
        custom_form: form,
        company_module: this.companyModule,
        sub_module_name: 'custom_form',
        company_build_id: this.buildId,
      };
      if (this.newDefaultFormId) {
        params.newDefaultFormId = this.newDefaultFormId;
      }
      const { isFormArchived } = this;
      const url = this.updateFormPath();
      if (!url) {
        return this.emitError(`Must have a company selected.`);
      }
      return http
        .put(url, params)
        .then(() => {
          // TODO: Check if this is working as intended. 
          // Will this ever even return true? I can't see how, 
          // since it checks that form.unarchive_only_staff_form is both true and false.
          if (!isFormArchived && form.unarchive_only_staff_form && !form.unarchive_only_staff_form) {
            this.emitSuccess("Teammate unarchiving has been started. It will be completed in few minutes.");
          } else {
            if (this.companyModule === 'helpdesk' && form.helpdesk_custom_form_attributes) {
              this.$store.dispatch('loadRequestEmail');
            }
            this.getCustomFormOptions();
            this.emitSuccess("Successfully updated this custom form.");
          }
          if(this.isMsp) {
            this.$emit('close-modal');
          } else {
            this.$router.push({ path: this.formRendorPath});
          }
        })
        .catch((err) => {
          const msg = _get(err, "response.data.message", "");
          this.emitError(`There was an error submitting the form. ${msg}`);
        })
        .finally(() => {
          loaderModalRef.close();
          this.isSaving = false;
          this.setLoadingStatus(false);
        });
    },
    createForm(form) {
      const loaderModalRef = this.formLoaderModalRef();
      this.setLoadingStatus(true);

      const params = {
        custom_form: form,
        company_module: this.companyModule,
        company_build_id: this.buildId,
      };
      if (this.$route.query.workspaceId) {
        params.workspace_id = this.$route.query.workspaceId;
      }
      const url = this.createFormPath();
      if (!url) {
        this.emitError(`Must have a company selected.`);
        return;
      }
      http
        .post(url, params)
        .then(() => {
          if (this.companyModule === 'helpdesk' && form.helpdesk_custom_form_attributes) {
            this.$store.dispatch('loadRequestEmail');
          }
          this.getCustomFormOptions();
          this.emitSuccess("Successfully created a new custom form");
          if(this.isMsp) {
            this.$emit('close-modal');
          } else {
            this.$router.push({ path: this.formRendorPath});
          }
        })
        .catch((err) => {
          const msg = _get(err, "response.data.message", "");
          this.emitError(`There was an error submitting the form. ${msg }`);
        })
        .finally(() => {
          loaderModalRef.close();
          this.isSaving = false;
          this.setLoadingStatus(false);
        });
    },
    initFormSubmit(isDraft) {
      let formFields;
      let formPosition;
      if (this.modifyOnce) {
        formFields = "form_fields";
      } else if (this.isMsp) {
        formFields = "msp_templates_custom_form_fields_attributes";
      } else {
        formFields = "custom_form_fields_attributes";
      }
      if (this.modifyOnce) {
        formPosition = "field_position";
      } else if (this.isMsp) {
        formPosition = "msp_templates_field_position_attributes";
      } else {
        formPosition = "field_position_attributes";
      }
      const helpdeskForm = this.isMsp ? "msp_templates_helpdesk_custom_form_attributes" : "helpdesk_custom_form_attributes";
      const isNewFormRoute = this.$route.path.includes('new');

      this.isSaving = true;
      const form = {
        id: this.currentForm.id,
        form_name: this.currentForm.formName,
        company_module: this.companyModule,
        is_active: this.currentForm.isActive,
        is_draft: isDraft,
        default: this.currentForm.default,
      };
      if (this.isHelpDeskModule && !this.isMsp) {
        form[helpdeskForm] = {
          default: this.currentForm.default,
          collect_closing_survey: this.currentForm.moduleForm.collectClosingSurvey,
          email: this.currentForm.moduleForm.email,
          show_in_open_portal: this.currentForm.moduleForm.showInOpenPortal,
          show_people_list: this.currentForm.moduleForm.showPeopleList,
          show_checklist: this.currentForm.moduleForm.showChecklist,
          helpdesk_custom_email_attributes: this.currentForm.moduleForm.customEmail || {},
          require_time_spent_to_close: this.currentForm.moduleForm.requireTimeSpentToClose,
        };
      }
      if (this.isPeopleModule) {
        form.icon = this.currentForm.icon;
        form.color = this.currentForm.color;
        form.description = this.currentForm.description;
      }
      if (!isNewFormRoute && !this.isMsp) { 
        this.compareFormFieldPermission();
      };
      const customFormFieldsAttributes = this.currentForm.formFields.map((attr) => {
        attr.currentCompanyUser = this.$currentCompanyUserId;
        return attr;
      });
      form[formFields] = customFormFieldsAttributes;
      // TODO: This .map function has no returns and so, presumably, is accomplishing nothing. 
      // Need to decide if this is needed and if so, add a proper return to the map.
      // eslint-disable-next-line array-callback-return
      form[formFields].map((attr) => {
        attr[formPosition] = attr.fieldPosition;
        if (!isNewFormRoute && !this.isMsp && this.changedPermissionFields.includes(attr.id)) {
          attr.custom_form_field_permissions_attributes = this.mapPermissions(attr.permissions);
        } else if (isNewFormRoute && !this.isMsp) {
          attr.custom_form_field_permissions_attributes = this.mapPermissions(attr.permissions);
        }
        if (attr.defaultValue && Array.isArray(attr.defaultValue)) {
          attr.defaultValue = JSON.stringify(attr.defaultValue);
        }
      });

      if (this.currentForm.moduleForm) {
        let agents = this.currentForm.moduleForm.selectedContributors;
        agents = agents ? agents.filter(con => con.id != null) : [];

        form.automatic_ticket_assignment = {
          'option': this.currentForm.automatedAssignmentRouting,
          'enableAssignment': this.currentForm.automatedAssignmentStatus,
          'assignValue': this.currentForm.moduleForm.atAssignValue,
          'agents': agents,
        };
      }
      form.changed_field_permissions_ids = this.changedPermissionFields;

      return form;
    },
    compareFormFieldPermission() {
      this.currentForm.formFields.forEach((field, index) => {
        const clonedField = this.copiedCustomForm.formFields[index];
        if (!clonedField){
          this.changedPermissionFields.push(field.id);
        } else if (field.permissions.length === 0 && clonedField.permissions.length > 0) {
          this.changedPermissionFields.push(field.id);
        } else if (field.permissions.length > clonedField.permissions.length) {
          this.changedPermissionFields.push(field.id);
        } else if (clonedField.permissions.length > field.permissions.length) {
          this.changedPermissionFields.push(field.id);
        } else {
          field.permissions.forEach((permission, permissionIndex) => {
            const clonedPermission = clonedField.permissions[permissionIndex];
            if (this.arePermissionsDifferent(permission, clonedPermission)) {
              this.changedPermissionFields.push(clonedField.id);
            }
          });
        }
      });
    },
    arePermissionsDifferent(permission, clonedPermission) {
      if (
        permission?.permissions?.join(',') !== clonedPermission?.permissions?.join(',') ||
        permission?.contributorId !== clonedPermission?.contributorId ||
        permission?.name !== clonedPermission?.name ||
        permission?.contributorType !== clonedPermission?.contributorType
      ) {
        return true;
      }
      return false;
    },
    mapPermissions(permissions) {
      return permissions.map((permission) => (
        {
          contributor_id: permission.contributorId,
          can_view: permission.permissions.includes('read'),
          can_edit: permission.permissions.includes('write'),
        }
      ));
    },
    updateList(e) {
      if (e.added) {
        const newElem = e.added.element;
        ["iconClass", "iconClassContent", "iconTextContent"].forEach(
          (key) => delete newElem[key]
        );
        const pos = newElem.orderPosition;
        const permissions = [];
        permissions.push(this.defaultPermission());
        newElem.permissions = permissions;
        this.currentFormAttribute = newElem;
        this.currentForm.formFields = _cloneDeep(this.currentForm.formFields);
        this.$refs.fieldValidation[pos - this.fieldPositionDifference[this.companyModule]].openEdit();
      }
    },
    filterFormFields() {
      const fieldTypes = this[`${this.companyModuleFormatted}PalleteItems`].smartPaletteItems
                        .concat(this[`${this.companyModuleFormatted}PalleteItems`].paletteItems)
                        .concat(this.commonPaletteItems)
                        .map(type => type === 'attachments' ? 'attachment' : type);
      this.attributeTypes = fieldTypes.map((fieldType)=>({ 'fieldType': fieldType }));
    },
    updateFormField(formField) {
      this.hasUnsavedChanges = true;
      const idx = formField.orderPosition;
      const fieldIndex = this.currentForm.formFields.findIndex((obj) => obj.orderPosition === idx);
      this.currentForm.formFields.splice(fieldIndex, 1, { ...formField });

    },
    resetPermissions(formField) {
      formField.permissions = [this.defaultPermission()];
      this.updateFormField(formField);
    },
    submitArchiveForm() {
      const form = this.initFormSubmit();
      form.unarchive_only_staff_form = this.unarchiveOnlyStaffForm;
      this.closeArchiveFormModal();
      this.openFormLoaderModal();
      this.updateForm(form).then(() => {
        this.closeSelectDefaultFormModal();
      });
    },
    archiveForm() {
      if (this.currentForm.default) {
        this.openSelectDefaultFormModal();
      } else {
        this.currentForm.isActive = false;
        this.submitArchiveForm();
      }
    },
    unarchiveForm(form) {
      this.unarchiveOnlyStaffForm = form.unarchive_only_staff_form;
      this.currentForm.isActive = true;
      this.submitArchiveForm();
    },
    updateFieldPositions() {
      const fieldLength = this.currentForm.formFields.length;
      for (let i = 0; i < fieldLength; i += 1) {
        this.currentForm.formFields[i].orderPosition = i;
      }
    },
    removeFormField(formField) {
      this.fieldToDelete = formField;
      if (formField.areCfvsExists) {
        this.$refs.deleteFieldWarningModal.open();
      } else {
        this.deleteFormField();
      }
    },
    closeDeleteFieldWarningModal() {
      this.fieldToDelete = null;
    },
    handleClose() {
      this.closeDeleteFieldWarningModal();
      this.$refs.deleteFieldWarningModal.close();
    },
    deleteFormField() {
      const idx = this.fieldToDelete.orderPosition;
      this.currentForm.formFields.splice(idx, 1);
      this.handleClose();
      this.updateFieldPositions();
    },
    openArchiveFormModal() {
      if (this.$refs.archiveFormModal) {
        this.$refs.archiveFormModal.open();
      }
    },
    closeArchiveFormModal() {
      if (this.$refs.archiveFormModal) {
        this.$refs.archiveFormModal.close();
      }
    },
    openSelectDefaultFormModal() {
      if (this.$refs.selectDefaultFormModal) {
        this.$refs.selectDefaultFormModal.open();
      }
    },
    closeSelectDefaultFormModal() {
      if (this.$refs.selectDefaultFormModal) {
        this.$refs.selectDefaultFormModal.close();
      }
    },
    setCustomFormFromForm(copyForm) {
      const form = {};
      if (this.$route.query.name) {
        form.formName = `${this.$route.query.name}`;
      } else if (!form.formName) {
        form.formName = `${copyForm.formName} Copy`;
      }
      form.companyModule = copyForm.companyModule;
      form.moduleForm = copyForm.moduleForm;
      form.moduleForm.email = this.workspaceInboundEmail(form.formName);
      form.formFields = copyForm.formFields.map((f) => (
        {
          defaultValue: f.defaultValue,
          fieldAttributeType: f.fieldAttributeType,
          fieldPosition: {
            position: f.fieldPosition.position,
          },
          label: f.label,
          name: f.name,
          options: f.options,
          orderPosition: f.orderPosition,
          permitEditDefault: f.permitEditDefault,
          permitViewDefault: f.permitViewDefault,
          private: f.private,
          required: f.required,
          permissions: [],
        }
      ));
      this.formSelectOpen = false;
      this.currentForm = form;
      // Once we have the custom form assigned, then we need to populate the
      // permissions for each field.
      this.setDefaultFieldPermissions();
    },
    setCustomFormFromTemplate(template) {
      const form = {};
      if (this.$route.query.name) {
        form.formName = `${this.$route.query.name}`;
      } else if (!form.formName) {
        form.formName = `${template.formName} Copy`;
      }
      if (this.isPeopleModule) {
        form.icon = `https://s3.amazonaws.com/nulodgic-static-assets/images/people_defaults/default${Math.floor(Math.random() * 43) + 1}_thumbnail.png`;
        form.color = `#${Math.floor(Math.random()*********).toString(16)}`;
        form.description = this.formDescription(template);
      }
      form.moduleForm = {
        showInOpenPortal: true,
        showPeopleList: false,
        collectClosingSurvey: false,
        default: false,
        showChecklist: true,
      };
      form.isActive = template.isActive;
      form.companyModule = template.companyModule;
      form.automatedAssignmentStatus = false;
      form.automatedAssignmentRouting = 'Round Robin';
      form.assignedContributors = [];
      form.formFields = template.formFieldTemplates.map((f) => (
        {
          defaultValue: f.defaultValue,
          fieldAttributeType: f.fieldAttributeType,
          fieldPosition: {
            position: f.fieldPosition.position,
          },
          label: f.label,
          name: f.name,
          options: f.options,
          orderPosition: f.orderPosition,
          permitEditDefault: f.permitEditDefault,
          permitViewDefault: f.permitViewDefault,
          private: f.private,
          required: f.required,
          audience: f.audience,
          permissions: [this.defaultPermission()],
        }
      ));
      this.formSelectOpen = false;
      this.currentForm = form;
      // Once we have the custom form assigned, then we need to populate the
      // permissions for each field.
      this.setDefaultFieldPermissions();
    },
    initFieldPermissions() {
      return http
        .get("/group_options/XX.json?name=Everyone")
        .then((res) => {
          this.everyoneGroup = res.data;
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error fetching default group information`);
        });
    },
    setDefaultFieldPermissions() {
      if (this.everyoneGroup && this.everyoneGroup.id === this.$currentCompanyId) {
        this.populateFieldPermissions();
      } else {
        this.initFieldPermissions().then(() => {
          this.populateFieldPermissions();
        });
      }
    },
    defaultPermission() {
      return {
        id: this.everyoneGroup.id,
        contributorId: this.everyoneGroup.contributorId,
        contributorType: 'Group',
        includeAll: this.everyoneGroup.includeAll,
        name: this.everyoneGroup.name,
        permissions: ['write'],
      };
    },
    populateFieldPermissions() {
      if (!this.currentForm || !this.currentForm.formFields || !this.everyoneGroup) {
        return;
      }
      this.currentForm.formFields.forEach((f) => {
        f.permissions = [ ];
        f.permissions.push(this.defaultPermission());
      });
    },
    fetchDefaultCustomFormTemplate() {
      const module = this.isMsp ? this.currentFormModule : this.companyModule;
      return http
        .get("/default_custom_form_templates.json", { params: { company_module: module } })
        .then((res) => {
          this.setCustomFormFromTemplate(res.data);
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error fetching default form data`);
        });
    },
    openRemoveFormModal() {
      if (this.$refs.removeFormModal) {
        this.$refs.removeFormModal.open();
      }
    },
    closeRemoveFormModal() {
      if (this.$refs.removeFormModal) {
        this.$refs.removeFormModal.close();
      }
    },
    openPreviewFormModal() {
      if (this.$refs.previewFormModal) {
        this.$refs.previewFormModal.open();
      }
    },
    closePreviewFormModal() {
      if (this.$refs.previewFormModal) {
        this.$refs.previewFormModal.close();
      }
    },
    showHint(shouldShowHint) {
      this.isHintOpen = shouldShowHint;
    },
    formSwitch(form) {
      this.fetchCustomForm(form);
      this.$router.replace({
        name: this.pathName,
        params: { id: form.id },
      });
    },
    getFormTickets(form) {
      if (this.valid) {
        const loaderModalRef = this.formLoaderModalRef();
        const params = {
          form_fields_ids: _map(this.currentForm.formFields, "id", []),
        };
        http
          .get(`/custom_forms/${form.id}/custom_form_tickets.json`, { params: { custom_form: params } })
          .then((res) => {
            this.formTickets = _get(res, "data.tickets", []);
            if (this.formTickets && this.formTickets.length > 0) {
              this.$refs.formTicketsModal.open();
            } else {
              this.updateForm(form);
            }
          })
          .catch((err) => {
            this.emitError(
              `There was an error submitting the form. ${_get(
                err,
                "response.data.message",
                ""
              )}`
            );
            loaderModalRef.close();
          })
          .finally(() => { this.isSaving = false; });
      }
    },
    setNewDefaultFormId(id) {
      this.newDefaultFormId = id;
      this.currentForm.default = false;
      this.archiveForm();
    },
    convertFieldOptions() {
      this.currentForm.formFields
        .filter(field => field.options && typeof(field.options) !== "string")
        // TODO: This .map returns an assignment, which means it is likely meant to be a .forEach.
        // Need to decide if a .forEach is right, or if removing this line is best, as it currently does nothing.
        // eslint-disable-next-line no-return-assign
        .map(cff => cff.options = JSON.stringify(cff.options));
    },
    convertSmartLisSortPreference() {
      this.currentForm.formFields
        .filter(field => field.sortList && typeof(field.sortList) !== "string")
        // TODO: This .map returns an assignment, which means it is likely meant to be a .forEach.
        // Need to decide if a .forEach is right, or if removing this line is best, as it currently does nothing.
        // eslint-disable-next-line no-return-assign
        .map(cff => cff.sortList = JSON.stringify(cff.sortList));
    },
    updateModuleForm(form) {
      this.currentForm.moduleForm  = {...this.currentForm.moduleForm, ...form};
    },
    fetchAllFormFields() {
      let url = '/custom_form_fields/fetch_form_fields.json';
      if (this.isMsp) {
        url = '/msp/templates/custom_forms/fetch_form_fields.json';
      }
      http
        .get(url)
        .then((res) => {
          this.allFormFields = _get(res, "data.fields", []);
        })
        .catch(() => {
          this.emitError(
            'There was an error fetching form fields.'
          );
        });
    },
    beforeWindowUnload(e) {
      if (this.currentForm.id && this.isFormValuesChanged(this.currentForm, this.copiedCustomForm)) {
        e.preventDefault();
        e.returnValue = '';
      }
    },
    resetForm() {
      if (this.currentForm.id && this.isAnyChange) {
        this.setCurrentForm();
        this.hasUnsavedChanges = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
a.change-link {
  color: $themed-muted;

  &.big {
    color: $themed-dark;
  }

  i {
    color: $themed-fair;
  }

  &:hover {
    color: $themed-link;

    i {
      color: $themed-link;
    }
  }
}

.bg-green-100 {
  background-color: #dff3e7;
}

.bg-yellow-100 {
  background-color: #faf9e0;
}

.background-light-gray {
  background-color: var(--themed-light);
  display: inline-block;
  font-size: 0.8125rem !important;
}

.left-border {
  display: inline-block;
  align-items: center;
  border-left: 0.3rem solid $color-caution;
}  

.border-left-green {
  border-left-color: #7ac899;
}

.border-left-yellow {
  border-left-color: #fad141;
}

.font-color {
  color: var(--themed-secondary) !important;
}

.tips-icon {
  width: 2rem;
}

.tips-icon-wrap {
  position: absolute;
  right: 0;

  .tips-icon {
    width: 25px;
  }

  .tips-icon__highlight {
    background: $cyan;
    border-radius: 50%;
    display: none;
    height: 16px;
    position: absolute;
    right: 20px;
    top: 8px;
    opacity: 0.3;
    display: none;
    width: 14px;
  }

  &:hover {
    cursor: pointer;

    .tips-icon__highlight {
      display: block;
    }
  }
}

.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

#custom_form {
  .form-group {
    position: relative;
  }
}

.badge--archived {
  background-color: $color-accent;
  color: $white;
  bottom: 7px;
}

.back-to-link {
  height: 3rem;
  float: right;
  line-height: 0rem;

  @media($max: $medium) {
    height: 2rem;
  }
}

.flex-column-reverse--responsive {
  @media($max: 1151px) {
    flex-direction: column-reverse;
  }
}

.edit-delete-btn {
  @media($max: $medium) {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  i {
    @media($max: $medium) {
      margin: 0;
      margin-right: 0 !important;
    }
  }
}

.form-btn--responsive {
  @media($max: $medium) {
    font-size: 0.8125rem !important;
  }
}

.icon-selection-dropdown {
  top: 5% !important;
  left: 18.75rem !important;
}

.selected {
  outline: 0.25rem solid $green;
}

.icon-selection-grid {
  max-width: 18.75rem;
  max-height: 12.5rem;
  overflow-y: auto;
}
.description-input {
  max-width: 300px;
}
.disabled,
.multiselect--disabled {
  opacity: 1;
}

[class^="genuicon-"]:before,
[class*=" genuicon-"]:before {
  vertical-align: -0.125em;
}

.rounded-lg {
  border-radius: 1rem;
}

.active-form-element {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.active-form-element:hover {
  background: var(--themed-lighter);
}

.py-2\.5,
.pt-2\.5 {
  padding-top: 0.75rem; 
}

.py-2\.5,
.pb-2\.5 {
  padding-bottom: 0.75rem;
}

.internal-only-tooltip {
  pointer-events: none;
  transition: opacity 0.1s 0.1s ease-in;
}

.internal-only-tooltip.opacity-0 {
  transition-delay: 0s;
  transition-timing-function: ease-out;
}

.arrow-down {
  margin-top: 0.125rem;
}

.genuicon-build-custom-forms {
  font-size: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  display: block;
  line-height: 3.5rem;
  text-align: center;
}

.input-group-text {
  letter-spacing: 0.25px;
}

.field-palette {
  border-bottom-left-radius: 0.5rem;
  overflow: auto;
}

.builder-section {
  height: calc(100vh - 17.5rem - 4.75rem);
  overflow: visible;
}

.active-form-elements {
  overflow: auto;
}

.sticky-header {
  position: sticky;
  top: 3rem;
  z-index: 99;
}

.sticky-builder {
  position: sticky;
  z-index: 2;
  top: 11rem;
  height: calc(100vh - 12.5rem - 11px);
  @media($max: 1399px) {
    margin-bottom: 5rem;
  }
}

.form-title {
 @media($max: 1399px) {
    font-size: 1.5rem;
  }
}

.setting-section {
  @media($max:1399px) {
    display: flex;
  }
  @media($max:1100px) {
   display: block;
  }
}

.additional-setting {
  margin-top: 1.5rem;
  margin-bottom: 3.5rem;

  @media($max:1399px) {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 2rem;
  }

  @media($max:1100px) {
    margin-left: 0;
    margin-top: 1.5rem;
  }
}
.custom-form {
  flex-wrap: nowrap;
  @media($max:1399px) {
    flex-direction: column-reverse;
  }
}

.builder-section{
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border: none;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--themed-fair);
    border-radius: 4px;
    transition: background 0.3s;
    opacity: 0.5;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--themed-very-muted);
  }

  ::-webkit-scrollbar-thumb:active {
    background: var(--themed-very-muted);
  }
}

@keyframes basic-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
