<template>
  <div
    class="position-relative"
    style="cursor: pointer"
    @mouseover="hovering = true"
    @mouseleave="hovering = false"
    @click.stop.prevent="openEdit"
  >
    <slot name="label" />
    <div v-if="editing">
      <textarea
        v-if="editing"
        v-model="textValue"
        class="form-control w-100"
        :disabled="field.disabled"
        @input="textAreaUpdate"
      />
      <div class="text-right mt-2">
        <button
          class="btn btn-sm btn-link text-secondary mr-2"
          @click.stop.prevent="cancelUpdate"
        >
          Cancel
        </button>
        <button
          class="btn btn-sm btn-primary"
          @click.stop.prevent="updateFieldValue"
        >
          Update
        </button>
      </div>
    </div>
    <div
      v-else
      @click="openEdit"
    >
      <div
        v-if="textValue && textValue.length > 0"
        class="box__inner"
        :data-tc-text-area="textValue"
      >
        <span
          v-for="(line, index) in formattedTextValue"
          :key="index"
        >
          {{ line }}
          <br v-if="index < formattedTextValue.length - 1" >
        </span>
      </div>
      <button
        v-else
        class="form-control clickable mb-3 text-muted text-left btn-sm"
        :disabled="!isWritableObject"
        @click.prevent.stop="openEdit"
      >
        {{ missingText }}
      </button>
      <edit-field-button 
        v-if="isWritableObject"
        :hovering="hovering"
        @openEdit="openEdit"
      />
    </div>
  </div>
</template>

<script>
  import _get from 'lodash/get';
  import { mapGetters } from 'vuex';
  import _debounce from "lodash/debounce";
  import permissions from 'mixins/custom_forms/permissions';
  import fieldPermissions from 'mixins/custom_forms/field_permissions';
  import customForms from 'mixins/custom_forms';
  import helpTickets from 'mixins/help_ticket';
  import helpTicketDraft from 'mixins/help_ticket_draft';
  import EditFieldButton from 'components/shared/custom_forms/edit_field_button.vue';

  export default {
    components: {
      EditFieldButton,
    },
    mixins: [customForms, permissions, fieldPermissions, helpTicketDraft, helpTickets],
    props: ['field', 'value', 'object'],
    data() {
      return {
        editing: false,
        hovering: false,
        textValue: _get(this, "value[0].valueStr", this.field.defaultValue),
        existingValue: _get(this, "value[0].valueStr", this.field.defaultValue),
      };
    },
    computed: {
      ...mapGetters([
        'currentHelpTicketDraft',
        'enableTicketDrafts',
      ]),
      missingText() {
        return `Enter ${this.field.label}`;
      },
      formattedTextValue() {
        return this.textValue ? this.textValue.split('\n') : [];
      },
    },
    watch: {
      value() {
        this.textValue = _get(this, "value[0].valueStr", '');
      },
    },
    methods: {
      textAreaUpdate: _debounce(
        function () {
          if (this.textValue && this.textValue !== this.existingValue && this.currentHelpTicketDraft && this.enableTicketDrafts) {
            const fields = { ...this.currentHelpTicketDraft?.fieldsData} || {};
            if (this.field.id) {
              fields[this.field.id] = this.textValue;
            }
            this.updateDraftFieldValue(fields);
          }
        },
        1000
      ),
      onWorkspaceChange() {
        if (this.enableEditing && this.currentHelpTicketDraft?.fieldsData[this.field.id]) {
          this.openEdit();
        }
      },
      openEdit() {
        if (this.isWritableObject) {
          this.editing = true;
        }
      },
      cancelUpdate() {
        this.textValue = _get(this, "value[0].valueStr", this.field.defaultValue);
        this.editing = false;
        this.hovering = false;
        this.discardDraft();
      },
      updateFieldValue() {
        this.addFormValue(this.object.id, {
          custom_form_field_id: this.field.id,
          value: this.textValue,
          name: this.field.name,
        })
        .then(() => {
          this.editing = false;
          this.discardDraft();
        });
      },
      discardDraft() {
        if (this.draftExists && this.draftValue && this.field.fieldAttributeType === 'text_area') {
          this.handleDraftResolution('discard');
        }
      },
    },
  };
</script>

