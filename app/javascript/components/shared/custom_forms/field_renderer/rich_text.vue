<template>
  <div
    class="position-relative"
    :class="{ 'editable-area': hovering && !editing }"
    style="cursor: pointer"
    @mouseenter="shouldShowDescriptionHover ? hovering = false : hovering = true"
    @mouseleave="hovering = false"
  >
    <div
      :class="{ 'd-flex justify-content-between': isHelpTicketShow }"
    >
      <slot name="label"/>
      <edit-field-button 
        v-if="canEdit && !isDescription && isHelpTicketShow"
        :align-right="!isHelpTicketShow"
        :hovering="hovering"
        @openEdit="openEdit"
      />
    </div>
    <div
      v-if="!isHelpTicketShow && editing"
      class="row"
    >
      <rich-text-edit
        class="col-12"
        :field="field"
        :value="value"
        :object="object"
        :edit-mode="editing"
        @toggle-edit="closeEdit"
        @input="updateValueStr"
        @attachment-uploading-status="uploadingStatus"
        @check-unsaved-attachments="checkUnusedAttachments"
      />
    </div>
    <div
      v-else-if="!editing"
      :class="{'mb-3' : !isHelpTicketShow}"
    >
      <div :class="{'d-flex description-align': isHelpTicketShow}">
        <div v-if="valueStr">
          <trix-render
            v-if="!isHelpTicketShow"
            :id="field.id"
            :value="truncatedValue"
            :max-height-px="getMaxheight"
            :action="action"
            type="rich_text"
            :view-more="!(isDescription && isHelpTicketShow)"
            :is-description="isDescription && isHelpTicketShow"
            :is-rich-text="!isDescription && isHelpTicketShow"
            editable
            :data-tc-rich-text="field.name"
            @display="displayDescription"
          />
          <div
            v-else
            class="d-flex w-100 align-items-center"
          >
            <p class="truncate-text mb-0">
              {{ truncatedValue }}
            </p>
            <a
              v-if="shouldShowSeeMore"
              href="#"
              class="mt-1 text-align-center text-nowrap not-as-small cursor-pointer flex-shrink-0"
              @click="viewDescription"
            >
              see more
            </a>
            <a
              v-if="!truncatedValue && isImgDescription"
              href="#"
              class="not-as-small cursor-pointer"
              @click="viewDescription"
            >
              see attachments...
            </a>
          </div>

        </div>
        <div
          class="d-flex h-fit-content"
          :class="{
            'description-container': valueStr && isDescription && isHelpTicketShow,
            'description-container-empty': !valueStr && isDescription&& isHelpTicketShow,
            'w-100': !valueStr && isHelpTicketShow,
            'col-3': isHelpTicketShow && (!isDescription && valueStr)
          }"
        >
          <div 
            v-if="!valueStr"
            :class="{ 'w-100' : !isHelpTicketShow || !isDescription }"
          >
            <button
              v-if="!isHelpTicketShow"
              class="form-control clickable mb-3 text-muted text-left btn-sm"
              :disabled="!canEdit"
              @click.prevent.stop="openEdit"
            >
              {{ missingText }}
            </button>
            <div v-if="isHelpTicketShow">
              <a
                v-if="isDescription"
                href="#"
                class="text-nowrap not-as-small cursor-pointer"
                :disabled="canEdit"
                @click="openEdit"
              >
                Add ticket description
              </a>
              <button
                v-else
                class="form-control clickable mb-3 text-muted text-left btn-sm"
                :disabled="!canEdit"
                @click.prevent.stop="openEdit"
              >
                {{ missingText }}
              </button>
            </div>
          </div>
          <div
            @mouseenter="shouldShowDescriptionHover && (hovering = true)"
            @mouseleave="(shouldShowDescriptionHover || hovering !== true ) && (hovering = false)"
          >
            <edit-field-button 
              v-if="canEdit && (!isHelpTicketShow || (isDescription && isHelpTicketShow))"
              :align-right="!isHelpTicketShow"
              :hovering="hovering"
              @openEdit="openEdit"
            />
          </div>
        </div>
      </div>
    </div>
    <sweet-modal
      ref="editDescription"
      v-sweet-esc
      class="modal-style"
      :title="isDescription ? 'Edit Description' : `Edit ${getFieldName}`"
      width="60%"
      @close="editing=false"
    >
      <rich-text-edit
        class="col-12 rich-text-height trix-content--large nested-modal--width"
        :field="field"
        :value="value"
        :object="object"
        :edit-mode="editing"
        display-survey-section
        @toggle-edit="closeEdit"
        @input="updateValueStr"
        @attachment-uploading-status="uploadingStatus"
        @check-unsaved-attachments="checkUnusedAttachments"
      />
    </sweet-modal>
    <sweet-modal
      ref="showDescription"
      v-sweet-esc
      class="modal-style modern-modal"
      :title="isDescription ? 'Ticket Details' : `${getFieldName}`"
      width="60%"
      max-height="90vh"
    >
      <div
        v-if="showSubject && isDescription"
        class="modal-content"
      >
        <h5 class="modal-heading">
          Subject:
        </h5>
        <h4 class="modal-subject">
          {{ object.subject }}
        </h4>
        <h5 class="modal-heading">
          Description:
        </h5>
      </div>
  
      <trix-render
        :id="field.id"
        :value="valueStr"
        :max-height-px="350"
        :action="action"
        view-description
        type="rich_text"
        :data-tc-rich-text="field.name"
        class="modal-trix-render rich-text-height"
      />
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import { mapGetters, mapActions } from 'vuex';
  import fieldPermissions from "mixins/custom_forms/field_permissions";
  import helpTickets from 'mixins/help_ticket';
  import strings from "mixins/string";
  import _get from 'lodash/get';
  import EditFieldButton from 'components/shared/custom_forms/edit_field_button.vue';
  import { checkUnusedAttachments } from 'mixins/trix_vue_helper';
  import RichTextEdit from './rich_text_edit.vue';
  import TrixRender from "../../../trix_render.vue";

  export default {
    components: {
      TrixRender,
      RichTextEdit,
      EditFieldButton,
      SweetModal,
    },
    mixins: [fieldPermissions, checkUnusedAttachments, strings, helpTickets],
    props: ['field', 'value', 'object', 'action', 'isQuickView', 'isHelpTicketShow'],
    data() {
      return {
        editing: false,
        hovering: false,
        descVisibility: false,
        openDescModal: false,
        isImgDescription: false,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['isVerticalNav']),
      ...mapGetters(['hideHeader', 'currentHelpTicketDraft', 'enableTicketDrafts']),
      ...mapGetters(['validExtensions']),

      getMaxheight() {
        return this.isHelpTicketShow ? 30 : 160;
      },
      missingText() {
        return `Enter ${this.truncate(this.field.label, 100)}`;
      },
      valueStr() {
        return _get(this.value[0], 'valueStr', this.field.defaultValue);
      },
      truncatedValue() {
        if (!this.isHelpTicketShow) {
          return this.valueStr;
        }
        if (!this.valueStr) {
          return null;
        }
        const extractedText = this.extractTextFromHTML(this.valueStr);
        return extractedText || '';
      },
      showSubject() {
        return this.object.subject.length > 50;
      },
      concateValue() {
        if (this.isDescription) {
          return 80;
        }
        return 50;
      },
      isDescription() {
        return this.field.name === 'description';
      },
      shouldShowDescriptionHover() {
        return this.isHelpTicketShow && this.isDescription;
      },
      getFieldName() {
        return this.field.name.replace("_", " ");
      },
      shouldShowSeeMore() {
        return (this.truncatedValue && this.truncatedValue.length > this.concateValue) || (this.truncatedValue && this.isImgDescription);
      },
    },
    methods: {
      ...mapActions(['fetchValidExtensions']),

      extractTextFromHTML(htmlString) {
        if (!this.validExtensions) {
          this.fetchValidExtensions();
        }
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlString, "text/html");
        const attachments = doc.querySelectorAll("figure, img, [data-trix-attachment], [data-trix-cursor-target]");
        const s3Links = Array.from(doc.querySelectorAll("a[href*='s3.amazonaws.com']")).filter(a => {
          const href = a.getAttribute('href');
          return this.validExtensions && this.validExtensions.some(ext => href.includes(ext));
        });

        doc.querySelectorAll("figure, img, [data-trix-attachment], [data-trix-cursor-target]").forEach(el => el.remove());
        s3Links.forEach(link => link.remove());
        this.isImgDescription = attachments.length > 0 || s3Links.length > 0;
        return doc.body.innerText.trim();
      },
      onWorkspaceChange() {
        if (this.enableEditing && this.currentHelpTicketDraft?.fieldsData[this.field.id]) {
          this.openEdit();
        }
      },
      updateValueStr(params) {
        const { label } = this.field;
        const valueId = _get(params, "id", "X");
        let url = `/tickets/${this.object.id}/custom_form_values/${valueId}.json`;

        if (this.$route.path.includes('locations')) {
          url = `/locations/${this.object.id}/custom_form_values/${valueId}.json`;
        } else if (this.$route.path.includes('users')) {
          url = `/company_users/${this.object.id}/custom_form_values/${valueId}.json`;
        }

        http.put(url, { json: JSON.stringify(params), company_id: this.object.company.id, workspace_id: this.object.workspaceId })
          .then(() => {
            this.setAttachmentsSaved(true);
            this.emitSuccess(`Successfully changed ${label}`);
          })
          .catch((error) => {
            const msg = _get(error, 'response.data.message', "");
            this.emitError(`Sorry, there was an error updating ${label}. Please refresh the page and try again. ${msg}.`);
          });
        this.$refs.editDescription.close();
      },
      uploadingStatus(status) {
        this.$emit('attachment-uploading-status', status);
      },
      displayDescription() {
        this.descVisibility = true;
      },
      viewDescription() {
        if (this.isHelpTicketShow) {
          this.$refs.showDescription.open();
        }
      },
      openEdit() {
        if (this.canEdit) {
          this.editing = true;
          if (this.isHelpTicketShow) {
            this.$refs.editDescription.open();
          }
        }
      },
      closeEdit() {
        this.$refs.editDescription.close();
        this.editing = false;
        this.hovering = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
.modal-style {
  margin-top: 3rem;
  overflow: auto;
}
.desc-loader {
  ::v-deep .v-sync {
    background-color: #8a9197 !important;
    height: 0.3rem !important;
    width: 0.3rem !important;
  }
}
sweet-modal .sweet-content {
  margin-left: 0rem;
  margin-right: 0rem;
  margin-top: 0rem;
}
.sweet-content-content {
  width: 100%;
  max-height: 30rem !important;
}
.rich-text-height {
  max-height: 30rem !important;
}
.clickable-text {
  color: blue;
  cursor: pointer;
}
.modern-modal {
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0rem 0.25rem 0.625rem rgba(0, 0, 0, 0.15);
  border: 0.0625rem solid #ddd;
  cursor: default;
}
.modal-content {
  padding-bottom: 0.9375rem;
}
.modal-heading {
  font-size: 1rem;
  font-weight: 600;
  color: #555;
  margin-bottom: 0.5rem;
}
.modal-subject {
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.9375rem;
  word-break: break-word;
  overflow-wrap: anywhere;
  max-width: 100%;
  cursor: default
}
.modal-trix-render {
  border: 0.0625rem solid #ddd;
  border-radius: 0.375rem;
  padding: 0.625rem;
  overflow-y: auto;
}
:deep(.sweet-content-content) {
  overflow: auto;
}
.description-container {
  width: 25% !important;

  @media (min-width: 1280px) {
    width: 45% !important;
  }
  @media (min-width: 1460px) {
    width: 60% !important;
  }
  @media (min-width: 1600px) {
    width: 80% !important;
  }
}
.description-container-empty {
  max-width: 80% !important;
}
.description-align {
  margin-top: 0.5rem;
  max-height: 3rem;
  justify-self: flex-start;
  width: 100%;
}

.truncate-text {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 1.2rem; 
  word-break: break-word;
  overflow-wrap: anywhere;
  max-width: 100%;
}
</style>
