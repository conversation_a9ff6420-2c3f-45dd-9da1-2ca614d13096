<template>
  <div v-if="value != null">
    <div class="form-group mb-0">
      <input
        :id="`help_paragraph_${field.id}`"
        ref="helpParagraphInput"
        :value="valueStr"
        type="hidden"
      >
      <div class="position-relative">
        <trix-vue
          ref="trixEditor"
          v-model="valueStr"
          :input-id="`help_paragraph_${field.id}`"
          class="description-field px-0"
          :class="{'description-field--small': isRightSidePositioned}"
          :display-survey-section="displaySurveySection"
          grab-focus
          attachable-type="HelpTicket"
          @update-uploading-status="updateUploadingStatus"
          @handle-attachment-add="handleAttachmentAdd"
          @deleted-attachmet-id="deletedAttachmetId"
          @handle-attachment-remove="handleAttachmentRemove"
          @related-items="updateRelatedItems"
          @input="descriptionUpdate"
        />
      </div>
      <div class="text-right">
        <button
          class="btn btn-sm btn-link text-secondary mr-2 form-btn--responsive"
          @click.stop.prevent="toggleEdit"
        >
          Cancel
        </button>
        <button
          class="btn btn-sm btn-primary form-btn--responsive"
          :disabled="attachmentUploading"
          data-tc-update-btn
          @click.stop.prevent="updateFieldValue"
        >
          <span v-if="attachmentUploading">Processing attachment
            <sync-loader
              :loading="attachmentUploading"
              class="ml-2 float-right"
              color="#000"
              size="0.125rem"
            />
          </span>
          <span v-else>Update</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
import _get from 'lodash/get';
import _debounce from "lodash/debounce";
import customFormHelper from "mixins/custom_form_helper";
import { watchUnusedAttachments } from 'mixins/trix_vue_helper';
import helpTicketDraft from 'mixins/help_ticket_draft';
import TrixVue from '../../../trix_vue.vue';

export default {
  components: {
    TrixVue,
    SyncLoader,
  },
  mixins: [ customFormHelper, watchUnusedAttachments, helpTicketDraft ],
  props: ['field', 'value', 'object', 'displaySurveySection', 'editMode'],
  data() {
    return {
      valueStr: _get(this, "value[0].valueStr", ''),
      originalValueStr: _get(this, 'value[0].valueStr', ''),
      attachmentUploading: false,
      attachmentIds: [],
      relatedItems: [],
      doc: null,
    };
  },
  computed: {
    ...mapGetters(['currentHelpTicket', 'currentHelpTicketDraft', 'enableTicketDrafts']),
    isRightSidePositioned() {
      return this.field.fieldPosition.position === "right" || this.getCompanyModule === "location";
    },
  },
  watch: {
    'currentHelpTicketDraft': function (newDraft) {
      if (newDraft && newDraft?.fieldsData && newDraft.fieldsData?.[this.field.id] && this.enableTicketDrafts) {
        this.valueStr = newDraft.fieldsData[this.field.id];
      }
    },
  },
  methods: {
    descriptionUpdate: _debounce(
      function () {
        if (this.isDraftResolved || !this.enableTicketDrafts) {
          return;
        }
        if (this.editMode && this.valueStr && this.valueStr !== this.originalValueStr && this.currentHelpTicketDraft) {
          const fields = { ...this.currentHelpTicketDraft?.fieldsData} || {};
          if (this.field.id) {
            fields[this.field.id] = this.valueStr;
          }
          this.updateDraftFieldValue(fields);
        }
      },
      1000
    ),
    toggleEdit() {
      this.$emit('toggle-edit');
      if (this.draftExists && this.draftValue && this.field.fieldAttributeType === 'rich_text') {
        this.handleDraftResolution('discard');
      }
    },
    updateRelatedItems(relatedItems) {
      this.relatedItems = relatedItems;
    },
    updateFieldValue() {
      const params = {
        custom_form_field_id: this.field.id,
        value: this.valueStr,
      };
      if (this.attachmentIds.length > 0) {
        params['attachment_ids'] = this.attachmentIds;
      }
      if (this.deletedAttachmetIds.length > 0) {
        this.removeAttachmetUploads();
      }
      var parser = new DOMParser();
      this.doc = parser.parseFromString(params.value, 'text/html');
      this.removeElement(".attachment__caption-editor");
      this.removeElement(".attachment__caption-editor.trix-autoresize-clone");
      this.removeElement(".attachment__progress");
      this.removeElement(".trix-button.trix-button--remove");
      params.value = this.doc.getElementsByTagName('body')[0].innerHTML;
      this.$emit("input", params);
      this.toggleEdit();
    },
    updateUploadingStatus(status) {
      this.attachmentUploading = status;
      this.$emit('attachment-uploading-status', status);
    },
    handleAttachmentAdd(data) {
      this.attachmentIds.push(data.attachment.id);
    },
    handleAttachmentRemove(attachmentId) {
      let idx = this.attachmentIds.indexOf(attachmentId);
      if (idx > -1) {
        this.attachmentIds.splice(idx, 1);
      }
    },
    removeElement(name) {
      if (this.doc.querySelector(name)) {
        this.doc.querySelector(name).remove();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
  .description-field--small {
    :deep(.trix-button) {
      font-size: 0.6rem !important;
    }
  }
</style>
