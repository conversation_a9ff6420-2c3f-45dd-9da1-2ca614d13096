<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">

    <div class="row">
      <div class="col-md-12">
        <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <step-instruction-box
          step-number="1"
          step-title="In order to integrate GCP, please provide the necessary permissions to read your compute engine data."
        >
          <ul class="pl-4 mt-3 not-as-small">
            <li class="mt-3">
              Simply log in to
              <a 
                href="https://console.cloud.google.com"
                target="_blank"
              >
                Google Cloud Console
              </a>
              with an editor or owner account
            </li>
            <li class="mt-1">
              Select your project
            </li>
            <li class="mt-1">
              Navigate to <b>IAM &amp; Admin</b> > <b>IAM</b>
            </li>
            <li class="mt-1">
              Add new member with our service account
              <br>
              <b><EMAIL></b>
            </li>
            <li class="mt-1">
              Select <b>Compute Viewer</b> role and save it
            </li>
            <li class="mt-1">
              Navigate to <b>API &amp; Services > Library </b>, search for <strong>Compute Engine API</strong> and click <strong>Enable</strong>
            </li>
            <li class="mt-1">
              Enter your project ID in the form given below
            </li>
            <li class="mt-1">
              You can add our service account to multiple projects and enter their IDs in the form given below to fetch virtual machines data from multiple projects
            </li>
          </ul>
        </step-instruction-box>

        <step-instruction-box
          step-number="2"
          step-title="Fill out the following information"
        >
          <p
            v-if="errors.length"
            class="text-danger not-as-small mb-0 mt-3"
          >
            <strong>Please correct the following error(s):</strong>
            <ul class="list-unstyled">
              <li
                v-for="(error, index) in errors"
                :key="`error-${index}`"
              >
                {{ error }}
              </li>
            </ul>
          </p>
          <form class="w-100 mt-3 mb-3">
            <div class="form-group">
              <div class="pb-3">
                <label for="access_key">
                  Projects
                </label>
                <div
                  v-for="(project, index) in googleAssetData.projects"
                  :key="`header-${index}`"
                  class="mb-2"
                >
                  <div class="d-flex">
                    <input
                      :id="`project-${index}`"
                      v-model="project.projectId"
                      class="form-control"
                      required="true"
                      type="text"
                      placeholder="Enter your project id..."
                      :class="{ 'is-invalid': project.isValid == false }"
                    >
                    <span
                      v-if="googleAssetData.projects.length > 1"
                      class="remove-icon"
                      @click="removeProject(index)"
                    >
                      &times;
                    </span>
                  </div>
                  <span
                    v-if="!project.isValid"
                    class="form-text small text-danger"
                  >
                    {{ project.errorMessage }}
                  </span>
                </div>
                <a
                  href="#"
                  @click.prevent="addNewProject"
                >
                  &plus; Add another project
                </a>
              </div>
            </div>
            <div class="form-group mt-3 mb-0 d-flex justify-content-end">
              <button
                v-if="!isLoading"
                class="btn btn-sm btn-link text-secondary mr-2"
                @click.prevent="close"
              >
                <span>Cancel</span>
              </button>
              <submit-button
                :is-saving="isLoading"
                :is-validated="!hasValidData"
                :btn-classes="'btn-sm px-3'"
                btn-content="Sync GCP"
                saving-content="Syncing GCP"
                @submit="syncGoogleAssets"
              />
            </div>
          </form>
        </step-instruction-box>
      </div>
    </div>
  </div>
</template>

<script>
  import OnboardingTitle from  'components/shared/module_onboarding/onboarding_title.vue';
  import common from 'components/shared/module_onboarding/common.js';
  import http from 'common/http';
  import SubmitButton from 'components/shared/submit_button.vue';
  import StepInstructionBox from 'components/shared/module_onboarding/step_instruction_box.vue';
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    components: {
      OnboardingTitle,
      SubmitButton,
      StepInstructionBox,
    },
    mixins: [common, permissionsHelper],
    data() {
      return {
        header: "Integrate GCP",
        subHeader: "Sync your existing asset services with GCP",
        imageSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/google_logo.png",
        googleAssetData: {
          projects: [{ projectId: "" }],
        },
      }
    },
    computed: {
      hasValidData() {
        return !this.googleAssetData.projects.filter(p => p.projectId).length;
      }
    },
    methods: {
      onWorkspaceChange() {
        this.$parent.$parent.setCondenseModal(true);
        this.fetchGoogleAssetData();
      },
      syncGoogleAssets() {
        this.customizing = false;
        this.isLoading = true;
        this.errors = [];
        const params = { google_assets_config: { projects_attributes: this.googleAssetData.projects } }

        http
          .post('/integrations/google_assets/configs.json', params)
          .then((res) => {
            this.isLoading = false;
            this.$store.dispatch('fetchCompanyIntegrations');
            this.emitSuccess('Google Cloud Platform sync in progress!');
            this.close();
          })
          .catch((error) => {
            this.emitError("Please fix the highlighted errors");
            let response = error.response.data;
            this.errors.push(response.message);
            if (response.config) {
              this.googleAssetData = response.config;
            }
            this.isLoading = false;
          });
      },
      addNewProject() {
        this.googleAssetData.projects.push({projectId: ""});
      },
      removeProject(index) {
        this.googleAssetData.projects.splice(index, 1);
      },
      fetchGoogleAssetData() {
        const intg = this.companyIntegrations.find((integ) => integ.name == "google_assets");
        if (!intg) return;

        http
          .get(`/integrations/google_assets/configs/${intg.configId}.json`)
          .then(res => {
            const config = res.data.config;
            if (config && config.projects.length) {
              this.googleAssetData.projects = config.projects;
            }
          })
          .catch(error => {
            this.emitError("Google Cloud configs failed to load, please try again!");
          });
      },
      close() {
        this.$emit('close');
      }
    },
  }
</script>

<style lang="scss" scoped>
  .tips-icon {
    width: 2rem;
  }
  ul {
    list-style: disc;
  }
  input[type=checkbox] {
    width:15px; 
    height:15px;
  }

  .remove-icon {
    cursor: pointer;
    font-size: 1.5rem;
    padding: 0 0.5rem;

    &:hover {
      opacity: 0.65;
    }
  }
</style>
