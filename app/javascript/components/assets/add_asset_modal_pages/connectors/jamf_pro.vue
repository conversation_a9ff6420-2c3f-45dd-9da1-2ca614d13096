<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">

    <div class="row">
      <div class="col-md-12">
        <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <step-instruction-box
          step-number="1"
          step-title="Please read these instructions carefully before proceeding."
        >
          <ul
            class="pl-4 mt-3 not-as-small"
            v-html="integrationInstructions"
          />
        </step-instruction-box>

        <step-instruction-box
          step-number="2"
          step-title="Fill out the following information"
        >
          <form
            class="mt-4"
            @submit.prevent="submitJamfProForm"
          >
            <p
              v-if="errors.length > 0"
              class="col-12 small text-danger"
            >
              <strong>Please correct the following error(s):</strong>
              <ul class="mb-0">
                <li
                  v-for="(error, index) in errors"
                  :key="`error-${index}`"
                >
                  {{ error }}
                </li>
              </ul>
            </p>
            <div class="row">
              <div class="form-group col-4">
                <label for="username">
                  Username
                </label>
                <input
                  v-model="jamfProData.username"
                  class="input form-control"
                  id="username"
                  required
                  type="text"
                  @keydown.space.prevent
                >
              </div>
              <div class="form-group col-4">
                <label for="password">
                  Password
                </label>
                <input
                  v-model="jamfProData.password"
                  class="input form-control"
                  id="password"
                  required
                  type="password"
                  @keydown.space.prevent
                >
              </div>
              <div class="form-group col-4">
                <label for="instanceName">
                  Instance Name
                </label>
                <div class="p-2 d-flex align-items-center bg-themed-box-bg rounded instance-input">
                  <input
                    v-model="jamfProData.instanceName"
                    class="input form-control p-0 w-60 border-0"
                    id="instanceName"
                    required
                    type="text"
                    @keydown.space.prevent
                  >
                  <label class="w-40 mb-0">.jamfcloud.com</label>
                </div>
              </div>
            </div>
            <div class="form-group col-12 mb-0 text-right">
              <button
                v-if="!loading"
                class="btn btn-sm btn-link text-secondary mr-2"
                @click.prevent="close"
              >
                <span>Cancel</span>
              </button>
              <button
                :disabled="loading || !dataPresent"
                class="btn btn-sm btn-primary px-3"
              >
                Submit
              </button>
            </div>
          </form>
        </step-instruction-box>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import OnboardingTitle from  'components/shared/module_onboarding/onboarding_title.vue';
  import StepInstructionBox from 'components/shared/module_onboarding/step_instruction_box.vue';
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    components: {
      OnboardingTitle,
      StepInstructionBox
    },
    mixins: [permissionsHelper],
    data() {
      return {
        header: 'Integrate Jamf Pro',
        subHeader: 'Sync your existing asset services with Jamf Pro',
        imageSrc: 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/jamf_pro.png',
        integrationInstructions: `<li class='mt-3'> You must login with an account that has <b>Full Access</b> to Jamf Pro.</li>
                                  <li class='mt-1'>Syncing might take up to 15 minutes.</li>`,
        jamfProData: {
          username: '',
          password: '',
          instanceName: '',
        },
        errors: [],
        loading: false,
      };
    },
    computed: {
      dataPresent() {
        return Object.values(this.jamfProData).every((val) => val);
      },
    },
    methods: {
      onWorkspaceChange() {
        this.$parent.$parent.setCondenseModal(true);
      },
      close() {
        this.$emit('close');
      },
      submitJamfProForm() {
        this.loading = true;
        this.errors = [];
        if (this.dataPresent) {
          http
            .post('/integrations/jamf_pro/configs.json', { jamf_pro_data: this.jamfProData })
            .then(res => {
              this.loading = false;
              this.$store.dispatch('fetchCompanyIntegrations');
              this.close();
            })
            .catch(error => {
              this.loading = false;
              this.errors.push(error.response.data.message);
            })
        } else {
          this.showErrors();
          this.loading = false;
        }
      },
      showErrors() {
        if (!this.jamfProData.username) {
          this.errors.push('Username is missing');
        }
        if (!this.jamfProData.password) {
          this.errors.push('Password is missing');
        }
        if (!this.jamfProData.instanceName) {
          this.errors.push('Instance name is missing');
        }
      }
    },
  }
</script>

<style scoped>
.instance-input {
  border: 1px solid #ced4da;
  height: calc(2.25rem + 2px);
}
.instance-input input {
  height: auto;
}
.instance-input input:focus {
  box-shadow: none;
}

.instance-input label {
  color: #808080;
}

</style>
