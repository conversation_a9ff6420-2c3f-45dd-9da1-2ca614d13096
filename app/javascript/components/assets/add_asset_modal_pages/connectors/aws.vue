<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">

    <div class="row">
      <div class="col-md-12">
        <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <div class="rounded bg-lighter p-3 border border-light">
          <div>
            <div class="onboarding-rounded-circle align-middle bg-info text-center text-white d-inline-block font-weight-bold">
              1
            </div>
            <h5 class="d-inline pl-2 text-secondary align-middle">
              In order to integrate AWS, user must have EC2 access.
            </h5>
            <ul class="pl-4 mt-3 not-as-small">
              <li class="mt-3">
                To allow IAM user to access EC2 permissions,
                <a
                  href="https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/security-iam.html"
                  target="_blank"
                >
                  click here.
                </a>
              </li>

              <li class="mt-1">
                Access keys can be obtained from your <a
                  href="https://console.aws.amazon.com/console/home"
                  target="_blank"
                >AWS console</a> by navigating to
                <strong> IAM Service </strong> >
                <strong> Users </strong> >
                <strong> Select particular user </strong> >
                <strong> Security credentials</strong>
              </li>

              <li class="mt-1">
                Please select the <a
                  href="https://docs.aws.amazon.com/emr/latest/ManagementGuide/emr-plan-region.html"
                  target="_blank"
                > Default Region </a> of your AWS account.
              </li>
            </ul>
          </div>
        </div>

        <div class="rounded bg-lighter p-3 mt-4 border border-light">
          <div>
            <div>
              <div class="onboarding-rounded-circle align-middle bg-info text-center text-white d-inline-block font-weight-bold">
                2
              </div>
              <h5 class="d-inline pl-2 text-secondary align-middle">
                Fill out the following information
              </h5>
              <div class="mt-3">
                <p
                  v-if="errors.length"
                  class="text-danger not-as-small mb-0 mt-3"
                >
                  <strong>Please correct the following error(s):</strong>
                  <ul class="list-unstyled">
                    <li
                      v-for="(error, index) in errors"
                      :key="`error-${index}`"
                    >
                      {{ error }}
                    </li>
                  </ul>
                </p>
                <form class="w-100 mt-3">
                  <div class="form-group">
                    <div class="pb-3">
                      <label for="access_key">
                        Access Key
                      </label>
                      <input
                        id="access_key"
                        v-model="awsAssetData.access_key"
                        class="form-control"
                        required="true"
                        type="text"
                      >
                    </div>
                    <div class="pb-3">
                      <label for="secret_key">
                        Secret Key
                      </label>
                      <input
                        id="secret_key"
                        v-model="awsAssetData.secret_key"
                        class="form-control"
                        required="true"
                        type="text"
                      >
                    </div>

                    <div class="pb-3">
                      <label for="aws-region">
                        Region
                      </label>
                      <multi-select
                        id="aws-region"
                        v-model="awsAssetData.regions"
                        label="name"
                        track-by="name"
                        placeholder="Please select regoins"
                        :options="awsRegions"
                        :multiple="true"
                      />
                    </div>
                    <span
                      class="clearfix d-flex align-items-center clickable"
                      @click="toggleAllRegionSelection"
                    >
                      <input
                        class="mr-2 clickable"
                        type="checkbox"
                        :checked="selectedAllRegions"
                      >
                      <label class="mt-2 unclickable">
                        Select All Regions
                      </label>
                    </span>
                  </div>
                  <div class="form-group mt-3 mb-1 text-right">
                    <button
                      v-if="!isLoading"
                      class="btn btn-sm btn-link text-secondary mr-2"
                      @click.prevent="close"
                    >
                      <span>Cancel</span>
                    </button>
                    <submit-button
                      :is-saving="isLoading"
                      :is-validated="!hasValidData"
                      :btn-classes="'btn-sm px-3'"
                      btn-content="Sync AWS"
                      saving-content="Syncing AWS"
                      @submit="submitAwsAssetForm"
                    />
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import OnboardingTitle from  'components/shared/module_onboarding/onboarding_title.vue';
  import common from 'components/shared/module_onboarding/common.js';
  import SubmitButton from 'components/shared/submit_button.vue';
  import MultiSelect from "vue-multiselect";
  import http from 'common/http';
  import { mapActions, mapGetters } from 'vuex';
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    components: {
      OnboardingTitle,
      SubmitButton,
      mapGetters,
      MultiSelect,
    },
    mixins: [common, permissionsHelper],
    props: ['customizing'],
    data() {
      return {
        header: "Integrate AWS",
        subHeader: "Sync your existing asset services with AWS",
        imageSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/aws_logo.png",
        awsAssetData: {
          access_key: "",
          secret_key: "",
          regions: [],
        },
        selectedAllRegions: false,
        isLoading: false,
      }
    },
    computed: {
      ...mapGetters('GlobalStore', ['awsRegions']),
      hasValidData() {
        return (
          this.awsAssetData.access_key == "" ||
          this.awsAssetData.secret_key == "" ||
          this.awsAssetData.regions.length <= 0
        );
      },
      awsAssetConfigs() {
        const regionsValue = this.awsAssetData.regions.map((region) => region.value);
        return {
          access_key: this.awsAssetData.access_key,
          secret_key: this.awsAssetData.secret_key,
          regions: regionsValue,
        }
      },
    },
    watch: {
      awsAssetData() {
        this.awsAssetData['regions'] = this.getSelectedRegions(this.awsAssetData['regions']);
      },
      'awsAssetData.regions'() {
        this.checkForSelectedRegions();
      },
      awsRegions() {
        this.checkForSelectedRegions();
      }
    },
    methods: {
      ...mapActions('GlobalStore', ['fetchAwsRegions']),
      onWorkspaceChange() {
        this.$parent.$parent.setCondenseModal(true);

        if (this.customizing) this.fetchAwsAssetData();
        this.fetchAwsRegions();
      },
      fetchAwsAssetData() {
        const integration = this.companyIntegrations.filter((integ) => integ.name == "aws_assets");
        if (integration.length < 1) return null;
        http
          .get(`/integrations/aws_assets/configs/${integration[0].configId}.json`)
          .then(res => {
            const config = res.data.config;
            if (config) {
              this.awsAssetData = {
                access_key: config.accessKey,
                secret_key: config.secretKey,
                regions: config.regions
              };
            }
          })
          .catch(error => {
            this.emitError("AWS configs failed to load, please try again!");
          });
      },
      submitAwsAssetForm() {
        this.isLoading = true;
        this.errors = [];
        http
          .post('/integrations/aws_assets/configs.json', { aws_credentials: this.awsAssetConfigs })
          .then((res) => {
            this.isLoading = false;
            this.$store.dispatch('fetchCompanyIntegrations');
            this.emitSuccess(res.data.message);
            this.close();
          })
          .catch((error) => {
            this.emitError("Please fix the highlighted errors");
            this.errors.push(error.response.data.message);
            this.isLoading = false;
          });
      },
      toggleAllRegionSelection() {
        if (this.selectedAllRegions) {
          this.awsAssetData['regions'] = [];
          this.selectedAllRegions = false;
        } else {
          this.awsAssetData['regions'] = this.awsRegions;
          this.selectedAllRegions = true;
        }
      },
      getSelectedRegions(selectedRegions) {
        return this.awsRegions.filter((region) => selectedRegions.includes(region.value));
      },
      checkForSelectedRegions() {
        let selectedRegionsCount = this.awsAssetData['regions'].length;

        if (selectedRegionsCount && selectedRegionsCount == this.awsRegions.length) {
          this.selectedAllRegions = true;
        } else {
          this.selectedAllRegions = false;
        }
      },
      close() {
        this.$emit('close');
      }
    }
  }
</script>
