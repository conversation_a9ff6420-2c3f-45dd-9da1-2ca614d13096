<template>
  <div>
    <module-breadcrumb
      class="mt-n2.5"
      :names="{
        '/': 'Contracts',
      }"
    />
    <div class="clearfix">
      <h2 class="float-left mb-0 text-dark">
        Contract Management
        <span
          v-if="loadingStatus"
          class="ml-3 d-inline-block"
        >
          <pulse-loader
            :loading="true"
            class="ml-3"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </h2>
      <div
        v-if="!isImportPage"
        class="float-right"
      >
        <div class="d-inline-block position-relative">
          <button
            v-if="isReadAny || isWrite"
            v-tooltip.top="'Export Contract Data'"
            class="btn btn-link text-secondary"
            data-tc-export-contract-btn
            @click.prevent.stop="exportContractClick"
          >
            <i class="nulodgicon-cloud-download text-fair mr-1" />
            Export
          </button>

          <import-options-dropdown
            :show="showExportContractsDropdown"
            :recurring-url="`/contracts/export?term=open_ended`"
            :fixed-url="`/contracts/export?term=fixed_term`"
            type="Export"
            module="contract"
          />
        </div>

        <div
          v-if="isWrite"
          class="d-inline-block position-relative"
        >
          <button
            v-tooltip.top="'Import Contract(s)'"
            class="btn btn-link text-secondary mr-3"
            data-tc-import-contract-btn
            @click.prevent.stop="importContractClick"
          >
            <i class="nulodgicon-cloud-upload text-fair mr-1" />
            Import
          </button>

          <import-options-dropdown
            :show="showImportContractsDropdown"
            :recurring-url="`/contracts/import_contracts?term=open_ended`"
            :fixed-url="`/contracts/import_contracts?term=fixed_term`"
            type="Import"
            module="contract"
          />
        </div>
        
        <span
          v-if="isScoped || isWrite"
          id="primary_ctas"
          class="d-inline-block"
          data-tc-add-new-contract-btn
        >
          <div class="position-relative">
            <a
              class="btn-group p-0"
              @click.prevent.stop="addContractClick"
            >
              <button 
                id="dropdown_button" 
                type="button" 
                class="btn btn-primary px-3 d-flex align-items-end"
              >
                <i class="nulodgicon-plus-round mr-2 not-as-small"/> 
                Add Contract
                <i class="dropdown-toggle ml-3 arrow-down d-block"/>
              </button>
            </a> 

            <import-options-dropdown
              :show="showAddContractsDropdown"
              :recurring-url="`/contracts/new?term=open_ended`"
              :fixed-url="`/contracts/new?term=fixed_term`"
              type="Add"
              module="contract"
            />
          </div>

        </span>
      </div>
      <div v-else>
        <div class="text-right">
          <a
            class="text-secondary mr-4"
            @click="redirectBack"
          >
            <i class="nulodgicon-arrow-left-c white mr-2" />
            <span>Back to
              <strong>contracts</strong>
            </span>
          </a>
        </div>
      </div>
    </div>
    <div class="mt-4">
      <div class="sub-menu clearfix">
        <div class="float-left module-sub-tabs">
          <router-link
            id="dashboardBtn"
            class="sub-menu-item"
            to="/"
            data-tc-btn="contracts dashboard"
          >
            Dashboard
          </router-link>
          <router-link
            id="allContractsBtn"
            class="sub-menu-item"
            :class="{'router-link-exact-active': $route.name == 'all'}"
            to="/all"
            data-tc-contract-tab
          >
            Contracts
          </router-link>
          <span class="sub-menu-separator" />
          <router-link
            v-if="displayCalendarSection"
            id="calendar_btn"
            class="sub-menu-item"
            to="/calendar"
          >
            Calendar
          </router-link>
          <router-link
            id="insights_btn"
            class="sub-menu-item"
            to="/insights"
          >
            Insights
          </router-link>
          <router-link
            v-if="isImportPage"
            id="import_btn"
            class="sub-menu-item"
            to="/import_contracts"
          >
            Import
          </router-link>
        </div>
        <common-sub-menu
          help-center-link="contracts"
          :reports="true"
          @on-permissions-click="goToContractPermissions"
        />
      </div>
    </div>
    <sweet-modal
      ref="staffInModule"
      v-sweet-esc
      title="People with Contract module permissions"
      width="50%"
      modal-theme="dark-header theme-centered-title"
    >
      <template slot="default">
        <module-users
          :load-data="loadData"
          :mod-name="'contracts'"
          :permission-name="'Contract'"
        />
      </template>
    </sweet-modal>
  </div>
</template>

<script>
  import permissionsHelper from 'mixins/permissions_helper';
  import { mapGetters, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import ModuleUsers from "components/shared/group_permissions/module_users.vue";
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import ModuleBreadcrumb from "components/shared/module_breadcrumb.vue";
  import CommonSubMenu from "components/shared/common_sub_menu_actions.vue";
  import ImportOptionsDropdown from '../shared/imports/import_options_dropdown.vue';

  export default {
    components: {
      SweetModal,
      ModuleUsers,
      PulseLoader,
      ModuleBreadcrumb,
      CommonSubMenu,
      ImportOptionsDropdown,
    },
    mixins: [permissionsHelper],
    props: ['active'],
    data() {
      return {
        loadData: false,
        showAddContractsDropdown: false,
        showImportContractsDropdown: false,
        showExportContractsDropdown: false,
      };
    },
    computed: {
      ...mapGetters(['loadingStatus', 'displayCalendarSection']),
      isImportPage() {
        return this.$route.path === "/import_contracts";
      },
    },
    methods: {
      ...mapActions([
        'shouldDisplayCalendar',
      ]),
      onWorkspaceChange() {
        this.shouldDisplayCalendar();
      },
      goToContractPermissions() {
        this.loadData = true;
        this.$refs.staffInModule.open();
      },
      downloadContractData() {
        const url = '/contracts/export';
        window.open(url, '_blank');
      },
      redirectBack() {
        window.history.length > 2 ? this.$router.go(-1) : this.$router.push('/');
      },
      addContractClick() {
        this.showAddContractsDropdown = !this.showAddContractsDropdown;
        this.showImportContractsDropdown = false;
        this.showExportContractsDropdown = false;
      },
      importContractClick() {
        this.showImportContractsDropdown = !this.showImportContractsDropdown;
        this.showAddContractsDropdown = false;
        this.showExportContractsDropdown = false;
      },
      exportContractClick() {
        this.showExportContractsDropdown = !this.showExportContractsDropdown;
        this.showAddContractsDropdown = false;
        this.showImportContractsDropdown = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dropdown-menu.right-0 {
    left: unset;
  }
</style>
