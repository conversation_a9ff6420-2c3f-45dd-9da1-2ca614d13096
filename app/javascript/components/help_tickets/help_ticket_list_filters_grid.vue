<template>
  <div>
    <div
      v-click-outside="onBlur"
      class="header-top-margin"
    >
      <h6
        class="mb-2 d-flex align-items-center"
        data-tc-view-label="filter by field"
      >
        Filter by Field
        <a
          href="#"
          class="d-flex align-items-center smallest px-1 rounded ml-2 text-muted bg-light font-weight-normal"
          data-tc-filter-option-icon
          @click.stop.prevent="openInfoModal"
        >
          <i class="genuicon-info-circled d-flex py-0.5 mr-1 text-muted" />
          View options
        </a>
      </h6>

      <div class="filterByFieldSelect d-flex mb-3">
        <multi-select
          v-model="valueOptions"
          allow-empty
          label="name"
          track-by="name"
          class="filter-by-field-select w-75"
          :placeholder="''"
          :multiple="true"
          :taggable="false"
          :options="options"
          :block-keys="['Delete']"
          data-tc-filter-by-field
          @search-change="asyncFind"
          @select="updateFilterOption"
          @remove="updateFilterOption"
        >
          <template slot="tag">
            {{ '' }}
          </template>
          <template
            slot="option"
            slot-scope="props"
          >
            <div class="row mx-0">
              <div
                v-if="props.option"
                class="d-inline-block"
              >
                <i
                  v-if="filterByFieldIconClass(props.option)"
                  class="text-very-muted"
                  :class="filterByFieldIconClass(props.option)"
                />
                <img
                  v-else
                  class="module-type-styling pr-0"
                  :src="imageUrl(props.option)"
                >
              </div>
              <div
                v-tooltip.left="{
                  content: props.option.name,
                  boundariesElement: 'body',
                  show: (props.option.name && props.option.name.length > 32 && hoveredIndex == props.option.id),
                  trigger: 'manual'
                }"
                class="d-inline-block ml-3"
                @mouseover="hoveredIndex = props.option.id"
                @mouseleave="hoveredIndex = null"
              >
                <span class="d-inline-block not-as-small truncate-name">
                  {{ props.option.name }}
                </span>
              </div>
            </div>
          </template>

          <template
            v-if="displayShowMoreButton"
            slot="afterList"
          >
            <div
              class="not-as-small py-3 cursor-pointer text-center"
              style="padding-left: 0.75rem !important"
              @click="showMore"
            >
              <a href="#">+ Show More</a>
            </div>
          </template>

          <template 
            slot="caret" 
            slot-scope=""
          />

          <template slot="noOptions">
            <div class="multiselct__no-option not-as-small text-center mb-0 px-3 py-5">
              <div class="mb-2">
                <strong>Type</strong> to search for a specific filter list, or<br> click
                <a
                  class="text-themed-link"
                  @click.prevent.stop="openInfoModal"
                >
                  here
                </a>
                to see what to type.
              </div>
            </div>
          </template>
        </multi-select>
      </div>
    </div>
    <hr class="my-2">

    <template v-if="companyOptions && companyOptions.length > 1">
      <h6 class="mb-2 d-flex justify-content-between align-items-center">
        <span>Filter by Company</span>
        <div 
          class="btn btn-xs btn-link px-2"
          @click.stop.prevent="togglingFilters(companyOptions, companyFilter,'setCompanyFilter')"
        >
          <a
            href="#"
            class="text-secondary"
          >
            {{ getMultiSelectButtonLabel(filteredCompaniesByName, companyFilter) }}
          </a>
        </div>
      </h6>
      <input
        v-if="companyOptions.length >= searchCompanyThreshold"
        v-model="searchCompanyName"
        type="text"
        class="form-control form-rounded filter-company mb-2"
      >
      <div
        class="row mx-0"
        :class="{'filter-row--with-overflow': filteredCompaniesByName.length > 9}"
      >
        <a
          v-for="(filter, idx) in filteredCompaniesByName"
          :key="idx"
          href="#"
          class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
          :class="{'active': companyFilter && companyFilter.filter(f => f.name === filter.name).length}"
          @click.stop.prevent="filter.setFilter()"
        >
          <span class="filter-wrapper">
            <i class="genuicon-company mr-0 align-middle text-muted" />
            <span>
              {{ filter.name }}
            </span>
          </span>
        </a>
      </div>
      <hr class="my-2">
    </template>

    <template v-if="workspaceFilterPresent">
      <h6 class="mb-2 d-flex justify-content-between align-items-center">
        <span>Filter by Workspace</span>
        <div 
          class="btn btn-xs btn-link px-2"
          @click.stop.prevent="togglingFilters(workspaceOptions, workspaceFilter, 'setWorkspaceFilter')"
        >
          <a
            href="#"
            class="text-secondary"
          >
            {{ getMultiSelectButtonLabel(workspaceFilters, workspaceFilter) }}
          </a>
        </div>
      </h6>
      <div class="row mx-0">
        <a
          v-for="(filter, idx) in workspaceFilters"
          :key="idx"
          href="#"
          class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
          :class="{'active': isWorkspaceFilterPresent(filter.name)}"
          :data-tc-workspace="filter.name"
          @click.stop.prevent="filter.setFilter"
        >
          <span class="filter-wrapper workspace-filter-view">
            <i 
              v-if="filter.icon" 
              :class="[`${filter.icon}`, 'inherit-color']"
            />
            <span :data-tc-apply-filter="filter.name">
              {{ filter.name }}
            </span>
          </span>
        </a>
      </div>
      <hr class="my-2">
    </template>

    <h6
      class="mb-2 d-flex justify-content-between align-items-center"
      data-tc-view-label="filter by status"
    >
      <span>Filter by Status</span>
      <div
        class="btn btn-xs btn-link px-2"
        @click.stop.prevent="togglingFilters(statusOptions, statusFilter, 'setStatusFilter')"
      >
        <a
          href="#"
          class="text-secondary"
          :data-tc-icon="getMultiSelectButtonLabel(statusFilters, statusFilter)"
        >
          {{ getMultiSelectButtonLabel(statusFilters, statusFilter) }}
        </a>
      </div>
    </h6>
    <div class="row mx-0">
      <a
        v-for="(filter, idx) in statusFilters"
        :key="idx"
        href="#"
        class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
        :class="{'active': statusFilter && statusFilter.filter(f => f.name === filter.name).length}"
        @click.stop.prevent="filter.setFilter()"
      >
        <span class="filter-wrapper">
          <span
            class="mr-1"
            :class="[statusFilterClass(filter), {'status-icon': filter.name != 'Archived'}]"
            :style="{ 'background-color': `${filter.color} !important` }"
          />
          <span
            :data-tc-apply-filter="filter.name"
            :class="{'d-inline-flex align-items-center': shouldShowFilterExclusionTooltip(filter)}"  
          >
            {{ filter.name }}
            <i
              v-if="shouldShowFilterExclusionTooltip(filter)"
              v-tooltip.top="filterTooltipText(filter)"
              class="genuicon-info-circled d-inline-flex ml-2 text-muted"
            />
          </span>
        </span>
      </a>
    </div>
    <hr class="my-2">
    <h6
      class="mb-2"
      data-tc-view-label="filter by assignment"
    >
      Filter by Assignment
    </h6>
    <div class="row mx-0">
      <a
        v-for="(filter, idx) in assignmentOptions"
        :key="idx"
        href="#"
        class="text-secondary mb-2 pl-0 field-filter-tag"
        :class="{
          'active': (assignmentFilterName === filter.name) || (isAssigneeFilter.includes(filter.name)) || inAssignmentFilter(filter.name),
          'col-4': filter.name !== 'Assigned to Agent/Group',
          'col-9 mt-2' : filter.name === 'Assigned to Agent/Group'
        }"
        @click.stop.prevent="filter.setFilter()"
      >
        <span class="filter-wrapper">
          <span
            :class="assignmentFilterClass(filter.name)"
            class="status-icon mr-1"
          />
          <span v-if="isAssignedAgent(filter.name) || isAssignedGroup(filter.name)">
            {{ filter.name }}
            <span class="dropdown-toggle ml-1 arrow-down d-inline-block" />
            <basic-dropdown
              ref="basicDropdown"
              :show-dropdown="(isAssignAgentOpen && isAssignedAgent(filter.name)) || (isAssignGroupOpen && isAssignedGroup(filter.name))"
              is-filters-view
              dropdown-height="22rem"
              dynamic-content
            >
              <multi-select
                ref="multiSelectAssignee"
                v-click-outside="onBlur"
                allow-empty
                label="name"
                track-by="id"
                class="w-75 multiselect-assignee"
                :value="assignedToFilter"
                :multiple="true"
                :taggable="false"
                :options="options"
                :block-keys="['Delete']"
                @select="updateFilterOption"
                @remove="updateFilterOption"
              >
                <template
                  slot="option"
                  slot-scope="props"
                >
                  <div class="row mx-0">
                    <div
                      v-if="props.option"
                      class="d-inline-block"
                    >
                      <i
                        v-if="filterByFieldIconClass(props.option)"
                        class="text-very-muted"
                        :class="filterByFieldIconClass(props.option)"
                      />
                      <img
                        v-else
                        class="module-type-styling pr-0"
                        :src="imageUrl(props.option)"
                      >
                    </div>
                    <div
                      v-tooltip.left="{
                        content: props.option.name,
                        boundariesElement: 'body',
                        show: (shouldShowFilterOption(props.option, hoveredIndex)),
                        trigger: 'manual'
                      }"
                      class="d-inline-block ml-3"
                      @mouseover="hoveredIndex = props.option.id"
                      @mouseleave="hoveredIndex = null"
                    >
                      <span class="d-inline-block not-as-small truncate-name">
                        {{ props.option.name }}
                      </span>
                    </div>
                  </div>
                </template>

                <template
                  v-if="displayShowMoreButton"
                  slot="afterList"
                >
                  <div
                    class="not-as-small py-3 cursor-pointer text-center show-more-btn"
                    @click.prevent.stop="showMore"
                  >
                    <a href="#">+ Show More</a>
                  </div>
                </template>
              </multi-select>
            </basic-dropdown>
          </span>
          <span
            v-else
            :data-tc-apply-filter="filter.name"
          >
            {{ filter.name }}
          </span>
        </span>
      </a>
    </div>
    <hr class="my-2">
    
    <h6
      class="mb-2 d-flex justify-content-between align-items-center"
      data-tc-view-label="filter by priority"
    >
      <span>Filter by Priority</span>
      <div 
        class="btn btn-xs btn-link px-2"
        @click.stop.prevent="togglingFilters(priorityOptions, priorityFilter, 'setPriorityFilter')"
      >
        <a
          href="#"
          class="text-secondary"
        >
          {{ getMultiSelectButtonLabel(priorityFilters, priorityFilter) }}
        </a>
      </div>
    </h6>
    <div class="row mx-0">
      <a
        v-for="(filter, idx) in priorityFilters"
        :key="idx"
        href="#"
        class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
        :class="{'active': isPriorityFilterPresent(filter.name) }"
        @click.stop.prevent="filter.setFilter"
      >
        <span class="filter-wrapper">
          <i
            class="mr-1 align-middle genuicon-priority"
            :class="isDefaultPriority(filter.name.toLowerCase()) ? `genuicon-priority-${filter.name.toLowerCase()}` : 'genuicon-priority-generic'"
            :style="{'color': filter.color}"
          />
          <span :data-tc-apply-filter="filter.name">
            {{ filter.name }}
          </span>
        </span>
      </a>
    </div>
    <hr class="my-2">

    <h6
      class="mb-2"
      data-tc-view-label="common filters"
    >
      Timeframe (Recent)
    </h6>
    <div class="row mx-0">
      <a
        v-for="(filter, idx) in commonQuickFilters"
        :key="idx"
        href="#"
        class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
        :class="{'active': activeCommonQuickFiltersPresent(filter.name)}"
        @click.stop.prevent="filter.setFilter"
      >
        <span class="filter-wrapper">
          <i
            :class="commonFiltersIconClass(filter.name)"
            class="align-middle"
          />
          <span :data-tc-apply-filter="filter.name">
            {{ filter.name }}
          </span>
        </span>
      </a>
    </div>
    <hr class="my-2">

    <!-- Change to only show if SLA's are present  -->
    <div>
      <h6
        class="mb-2 d-flex justify-content-between align-items-center"
        data-tc-view-label="filter by form"
      >
        <span>Filter by SLA</span>
        <div 
          class="btn btn-xs btn-link px-2"
          @click.stop.prevent="togglingFilters(slaOptions, slaFilter, 'setSlaFilter')"
        >
          <a
            href="#"
            class="text-secondary"  
          >
            {{ getMultiSelectButtonLabel(slaOptions, slaFilter) }}
          </a>
        </div>
      </h6>
      <div class="row mx-0">
        <a
          v-for="(filter, idx) in slaFilters"
          :key="idx"
          href="#"
          class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
          :class="{'active': isSlaFilterPresent(filter.name)}"
          @click.stop.prevent="filter.setFilter"
        >
          <span class="filter-wrapper">
            <i class="genuicon-sla" />
            <span>
              {{ filter.name }}
            </span>
          </span>
        </a>
      </div>
      <hr class="my-2">
    </div>

    <h6
      class="mb-2 d-flex justify-content-between align-items-center"
      data-tc-view-label="filter by source"
    >
      <span>Filter by Source</span>
      <div 
        class="btn btn-xs btn-link px-2"
        @click.stop.prevent="togglingFilters(sourceOptions, sourceFilter, 'setSourceFilter')"
      >
        <a
          href="#"
          class="text-secondary"  
        >
          {{ getMultiSelectButtonLabel(sourceFilters, sourceFilter) }}
        </a>
      </div>
    </h6>
    <div class="row mx-0">
      <a
        v-for="(filter, idx) in sourceFilters"
        :key="idx"
        href="#"
        class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
        :class="{'active': sourceFilter && sourceFilter.filter(f => f.name === filter.name).length}"
        @click.stop.prevent="filter.setFilter()"
      >
        <span class="filter-wrapper">
          <i class="genuicon-sources mr-0.5" />
          <span :data-tc-apply-filter="filter.name">
            {{ filter.name }}
          </span>
        </span>
      </a>
    </div>
    <hr class="my-2">
    <template v-if="customFormFilters && customFormFilters.length > 1 && !isMultiCompanyFilter">
      <h6
        class="mb-2 d-flex justify-content-between align-items-center"
        data-tc-view-label="filter by form"
      >
        <span>Filter by Form</span>
        <div 
          class="btn btn-xs btn-link px-2"
          @click.stop.prevent="togglingFilters(customFormOptions, formFilter, 'setCustomFormFilter')"
        >
          <a
            href="#"
            class="text-secondary"
          >
            {{ getMultiSelectButtonLabel(customFormFilters, formFilter) }}
          </a>
        </div>
      </h6>
      <div class="row mx-0">
        <a
          v-for="(filter, idx) in customFormFilters"
          :key="idx"
          href="#"
          class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
          :class="{'active': formFilter && formFilter.filter(f => f.name === filter.name).length}"
          @click.stop.prevent="filter.setFilter()"
        >
          <span class="filter-wrapper">
            <i class="genuicon-clipboard" />
            <span :data-tc-apply-filter="filter.name">
              {{ filter.name }}
            </span>
          </span>
        </a>
      </div>
      <hr class="my-2">
    </template>

    <h6
      class="mb-2"
      data-tc-view-label="filter by timeframe"
    >
      Timeframe (Historical)
    </h6>
    <div class="row mx-0">
      <a
        v-for="(filter, idx) in dateFilters"
        :key="idx"
        href="#"
        class="text-secondary col-4 mb-2 pl-0 field-filter-tag"
        :class="{'active': (selectedDateFilterName === filter.name)}"
        @click.stop.prevent="setDate(filter)"
      >
        <span class="filter-wrapper">
          <i
            v-if="filter.name.startsWith('Last')"
            class="genuicon-calendar-o"
          />
          <i
            v-else
            class="genuicon-calendar-check-o"
          />
          <span :data-tc-apply-filter="filter.name">
            {{ filter.name }}
          </span>
        </span>
      </a>
    </div>

   

    <Teleport to="body">
      <sweet-modal
        ref="filterByFieldInfo"
        v-sweet-esc
        title="Filter by Field"
        data-tc-header="filter by field"
      >
        <template slot="default">
          <div class="bg-lighter p-2 pl-4 rounded">
            <h6 class="text-cyan not-as-small">
              <img
                class="tips-icon mr-2"
                src="https://nulodgic-static-assets.s3.amazonaws.com/images/tips_lightbulb.svg"
                data-tc-view-text
              >
              How filter by field works?
            </h6>
            <ul>
              <li data-tc-view-text="desired tickets">
                Now you can get the desired helptickets by applying custom filters.
              </li>
              <li data-tc-view-text="type">
                Thinking about what to type?
              </li>
              <li data-tc-view-text="keywords">
                You can type any of the following keywords to search respective filters.
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-assets>Asset</strong>
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-contracts>Contract</strong>
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-vendor>Vendor</strong>
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-telecom>Telecom</strong>
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-location>Location</strong>
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-staff>People</strong>
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-created-by>Created by</strong>
              </li>
              <li>
                <span class="text-info mr-1">•</span>
                <strong data-tc-assigned-to>Assigned to</strong>
              </li>
              <li data-tc-view-text="options">
                After typing any of the above keywords, you will get the list of options.
              </li>
              <li data-tc-view-text="result options">
                For applying filter,
                just select any one of the options and you will have the filtered help tickets.
              </li>
            </ul>
          </div>
          <div class="form-group mt-3 text-right">
            <button
              class="btn btn-primary"
              data-tc-close-btn="options"
              @click.prevent.stop="closeInfoModal"
            >
              Close
            </button>
          </div>
        </template>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="customTimeFrameModal"
        v-sweet-esc
        v-click-outside="updateCloseOutside"
        title="Set Date Range"
        class="date-range-modal"
        @close="clearDateRange"
      >
        <template slot="default">
          <div>
            <label> Filter Criteria </label>
            <div class="mb-2">
              <multi-select
                placeholder="Select criteria"
                :value="currentFilterCriteria"
                :preselect-first="true"
                :options="['Created at', 'Closed at']"
                @remove="selectFilterCriteria"
                @select="selectFilterCriteria"
              />
            </div>
            <nice-rangepicker
              :key="'date-range'"
              :value="dateRange"
              @start-date-selection="updateStartDate"
              @end-date-selection="updateEndDate"
            />
            <br>
            <label>
              Start Date
            </label>
            <div>
              <nice-datepicker
                id="start-date"
                v-model="ticketStartDate"
                disable-date-picker
                placeholder="Select start date"
                data-vv-as="Start Date"
                data-tc-start-date
                @input="startDate"
              />
            </div>
            <label class="mt-2">
              End Date
            </label>
            <div>
              <nice-datepicker
                id="end-date"
                v-model="ticketEndDate"
                disable-date-picker
                placeholder="Select end date"
                data-vv-as="End Date"
                data-tc-end-date
                @input="endDate"
              />
            </div>
            <div class="d-flex justify-content-center">
              <button
                class="btn btn-primary mt-2"
                type="button"
                :disabled="!datesPresent"
                @click="applyDateRange"
              >
                Apply
              </button>
            </div>
          </div>
        </template>
      </sweet-modal>
    </Teleport>
  </div>
</template>

<script>
  import http from 'common/http';
  import universalLinkImages from 'mixins/universal_link_images';
  import _cloneDeep from 'lodash/cloneDeep';
  import { mapGetters, mapMutations } from 'vuex';
  import Teleport from "vue2-teleport";
  import strings from 'mixins/string';
  import filters from 'mixins/filters';
  import { SweetModal } from "sweet-modal-vue";
  import _debounce from "lodash/debounce";
  import _uniqWith from 'lodash/uniqWith';
  import MultiSelect from 'vue-multiselect';
  import NiceRangepicker from "components/shared/nice_rangepicker.vue";
  import NiceDatepicker from "components/shared/nice_datepicker.vue";
  import permissionsHelper from 'mixins/permissions_helper';
  import customFormHelper from "mixins/custom_form_helper";
  import BasicDropdown from 'components/shared/basic_dropdown.vue';
  import vClickOutside from 'v-click-outside';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      Teleport,
      SweetModal,
      MultiSelect,
      NiceDatepicker,
      BasicDropdown,
      NiceRangepicker,
    },
    mixins: [strings, filters, permissionsHelper, universalLinkImages, customFormHelper],
    props: {
      values: {
        type: Array,
        required: true,
      },
      activeFiltersCount: {
        type: Number,
        required: true,
      },
    },
    data() {
      return {
        assignedToOptions: null,
        companyOptions: null,
        customFormOptions: null,
        createdByOptions: null,
        priorityOptions: null,
        statusOptions: null,
        workspaceOptions: null,
        options: [],
        isOpen: false,
        smartListOption: [],
        offset: 0,
        recordSize: 20,
        totalOptionsCount: 0,
        hoveredIndex: null,
        ticketStartDate: null,
        ticketEndDate: null,
        dateRange: {
          startDate: null,
          endDate: null,
        },
        customFilterType: null,
        customFilterValue: null,
        customFilterName: null,
        searchCompanyName: '',
        searchCompanyThreshold: 9,
        assignmentOptions: [
          { name: 'Assigned', setFilter: () => this.updateQuickFilter({ filter: 'setAssignmentFilter', item: { name: 'Assigned', id: 'Assigned', label: 'Assignment' } }) },
          { name: 'Unassigned', setFilter: () => this.updateQuickFilter({ filter: 'setAssignmentFilter', item: { name: 'Unassigned', id: 'Unassigned', label: 'Assignment' } }) },
          { name: 'Assigned to Me', setFilter: () => this.updateQuickFilter({ filter: 'setAssignedToFilter', item: { name: this.$currentContributorName, id: this.$currentContributorId, label: 'Assigned to', entity: 'CompanyMember' } }) },
          { name: 'Assigned to Agent', setFilter: () => this.toggleDropdown('agent') },
          { name: 'Assigned to Group', setFilter: () => this.toggleDropdown('group') },
        ],
        slaOptions: [
          { id: 'overdue', name: 'Overdue', label: 'SLA' },
          { id: 'duetoday', name: 'Due Today', label: 'SLA' },
          { id: 'tomorrow', name: 'Due Tomorrow', label: 'SLA' },
        ],
        setFilterType: null,
        sourceOptions: null,
        hasFetchedFilterOptions: false,
        filterColorClasses: {
          'Active': 'bg-success',
          'New': 'bg-blue',
          'Unread': 'bg-red-orange',
          'Archived': 'nulodgicon-archive not-as-small',
          'Drafts': 'nulodgicon-ios-list-outline',
        },
        valueOptions: _cloneDeep(this.values),
        isAssignAgentOpen: false,
        isAssignGroupOpen: false,
        currentFilterCriteria: null,
        isDateRangeModalOpen: false,
        commonTimeframeFilters: ['Created today', 'Created this week', 'Created this month', 'Created yesterday'],
        activeFilterType: null,
      };
    },

    computed: {
      ...mapGetters([
        'companyFilter',
        'workspaceFilter',
        'dateFilter',
        'customDateFilter',
        'statusFilter',
        'priorityFilter',
        'sourceFilter',
        'assignmentFilter',
        'formFilter',
        'dueSoonFilter',
        'recentlyClosedFilter',
        'completedTicketFilter',
        'closedByDateFilter',
        'createdByDateFilter',
        'createdByFilter',
        'assignedToFilter',
        'filterByField',
        'slaFilter',
        'enableTicketDrafts',
      ]),
      ...mapGetters('GlobalStore', ['currentCompanyUser']),
      isAgent() {
        return this.currentCompanyUser?.isAgent;
      },
      isMultiCompanyFilter() {
        return this.companyFilter.length > 1;
      },
      selectedDateFilterName() {
        if ( this.closedByDateFilter?.filterType?.name === 'Custom date range' || this.createdByDateFilter?.filterType?.name === 'Custom date range') {
          return "Custom date range";
        }

        return this.dateFilter?.name || this.dateFilter?.filterType?.name ||
          this.customDateFilter?.filterType?.name;
      },
      datesPresent() {
        const isFilterSelected = this.currentFilterCriteria;
        if (this.ticketStartDate && this.ticketEndDate && isFilterSelected) {
          return true;
        }
        return false;
      },
      selectedCompanyId() {
        return this.companyFilter ? this.companyFilter.map(company => company.id) : this.$currentCompanyId;
      },
      getCreatedByOptionByCurrentUser() {
        if (this.createdByOptions?.length) {
          return this.createdByOptions.find(option => option.id === this.$currentContributorId);
        }
        return null;
      },
      getAssignedToOptionByCurrentUser() {
        if (this.assignedToOptions?.length) {
          return this.assignedToOptions.find(option => option.id === this.$currentContributorId);
        }
        return null;
      },
      commonQuickFilters() {
        const mainFilters = [
          {
            name: 'Created today',
            setFilter: () => this.updateQuickFilter({ filter: 'setDateFilter', item: { name: 'Created today', value: 2, filterType: 'created_today'} }),
          },
          {
            name: 'Created yesterday',
            setFilter: () => this.updateQuickFilter({ filter: 'setDateFilter', item: { name: 'Created yesterday', value: 8, filterType: 'created_yesterday'} }),
          },
          {
            name: 'Created this week',
            setFilter: () => this.updateQuickFilter({ filter: 'setDateFilter', item: { name: 'Created this week', value: 9, filterType: 'created_this_week'} }),
          },
          {
            name: 'Almost due',
            setFilter: () => this.updateQuickFilter({ filter: 'setDueSoonFilter', item: { name: 'Almost due', value: 4, filterType: 'due_soon'} }),
          },
          {
            name: 'Recently closed (3 days)',
            setFilter: () => this.updateQuickFilter({ filter: 'setRecentlyClosedFilter', item: { name: 'Recently closed (3 days)', value: 3, filterType: 'closed_in_last_3_days' } }),
          },
          {
            name: 'Closed this week',
            setFilter: () => this.updateQuickFilter({ filter: 'setCompletedTicketFilter', item: { name: 'Closed this week', value: 7, filterType: 'completed_this_week' } }),
          },
          {
            name: 'Created this month',
            setFilter: () => this.updateQuickFilter({ filter: 'setDateFilter', item: { name: 'Created this month', value: 10, filterType: 'created_this_month'} }),
          },
          {
            name: 'Closed this month',
            setFilter: () => this.updateQuickFilter({ filter: 'setCompletedTicketFilter', item: { name: 'Closed this month', value: 0, filterType: 'completed_this_month'} }),
          },
          {
            name: 'Date Range',
            setFilter: () => this.setDate({name: "Date Range", filterType: "date_range", value: 0 }),
          },
        ];
        return mainFilters;
      },
      activeCommonQuickFilters() {
        let activeCommonQuickFilters= [
          this.dueSoonFilter,
          this.recentlyClosedFilter,
          this.completedTicketFilter,
          this.closedByDateFilter?.filterType,
          this.createdByDateFilter?.filterType,
        ];

        // Date filter oddities
        if (this.commonTimeframeFilters.includes(this.dateFilter?.name)) {
          activeCommonQuickFilters = activeCommonQuickFilters.concat({ name: this.dateFilter.name });
        }

        // Created by me filter oddities
        if (this.createdByFilter?.filter(f => f.name === this.getCreatedByOptionByCurrentUser?.name).length) {
          activeCommonQuickFilters = activeCommonQuickFilters.concat({name: "Created by me"});
        }

        // Assigned to me filter oddities
        if (this.assignedToFilter?.filter(f => f.name === this.getAssignedToOptionByCurrentUser?.name).length) {
          activeCommonQuickFilters = activeCommonQuickFilters.concat({name: "Assigned to me"});
        }

        return activeCommonQuickFilters.filter(f => !!f);
      },
      companyFilters() {
        if (this.companyOptions) {
          return this.companyOptions.map(company => ({ name: company.name, setFilter: () => this.updateQuickFilter({ filter: 'setCompanyFilter', item: company }) }));
        }
        return [];
      },
      workspaceFilters() {
        if (this.workspaceOptions) {
          return this.workspaceOptions.map(workspace => ({
            name: workspace.name,
            icon: workspace.iconType,
            setFilter: () => this.updateQuickFilter({ filter: 'setWorkspaceFilter', item: workspace }),
          }));
        }
        return [];
      },
      priorityFilters() {
        if (this.priorityOptions) {
          return this.priorityOptions.map(priority => ({ name: priority.name, color: priority.color, setFilter: () => this.updateQuickFilter({ filter: 'setPriorityFilter', item: priority }) }));
        }
        return [];
      },
      statusFilters() {
        if (this.statusOptions) {
          const mappedOptions = this.statusOptions.map(status => ({ name: status.name, color: status.color, setFilter: () => this.updateQuickFilter({ filter: 'setStatusFilter', item: status }) }));
          const sortOrder = ['New', 'Open', 'Unread', 'Active', 'Closed', 'Archived', 'In Progress', ...(this.enableTicketDrafts ? ['Drafts'] : [])];

          // Ensure a consistantly usable sort order for our default status filters
          return mappedOptions.sort((a, b) => {
            const indexA = sortOrder.indexOf(a.name);
            const indexB = sortOrder.indexOf(b.name);

            if (indexA === -1 && indexB === -1) return a.name.localeCompare(b.name);
            if (indexA === -1) return 1;
            if (indexB === -1) return -1;
            
            return indexA - indexB;
          });
        }
        return [];
      },
      sourceFilters() {
        if (this.sourceOptions) {
          return this.sourceOptions.map(source => ({ name: source.name, setFilter: () => this.updateQuickFilter({ filter: 'setSourceFilter', item: source }) }));
        }
        return [];
      },
      dateFilters() {
        if (this.filters) {
          return this.filters.map(dateFilter => ({
              name: dateFilter.name,
              filterType: dateFilter.filterType,
              value: dateFilter.value,
              setFilter: () => this.updateQuickFilter({
                filter: 'setDateFilter',
                item: dateFilter,
              }),
            }));
        }
        return [];
      },
      slaFilters() {
        return this.slaOptions.map(sla => ({ name: sla.name, setFilter: () => this.updateQuickFilter({ filter: 'setSlaFilter', item: sla }) }));
      },
      customFormFilters() {
        if (this.customFormOptions) {
          return this.customFormOptions.map(form => ({ name: form.name, setFilter: () => this.updateQuickFilter({ filter: 'setCustomFormFilter', item: form }) }));
        }
        return [];
      },
      filteredCompaniesByName(){
        return this.companyFilters.filter(company=> company.name.toLowerCase().includes(this.searchCompanyName.toLowerCase()));
      },
      isAssigneeFilter() {
        const conditions = [
          { condition: (assignee) => assignee.id === this.$currentContributorId, label: 'Assigned to Me' },
          { condition: (assignee) => assignee.entity === 'CompanyMember', label: 'Assigned to Agent' },
          { condition: (assignee) => assignee.entity === 'Group', label: 'Assigned to Group' },
        ];

        const currentAssigneeFilters = conditions
          .filter(({ condition }) => this.assignedToFilter.some(condition))
          .map(({ label }) => label);

        return currentAssigneeFilters;
      },
      assignmentFilterName() {
        return this.assignmentFilter?.name;
      },
      displayShowMoreButton() {
        return this.options.length && this.options.length < this.totalOptionsCount;
      },
      workspaceFilterPresent() {
        return this.workspaceOptions?.length > 1;
      },
    },
    watch: {
      values: {
        deep: true,
        handler(newValues) {
          this.valueOptions = _cloneDeep(newValues);
        },
      },
    },
    methods: {
      ...mapMutations([
        'setUpdatingMultipleFilters',
      ]),
      closeContainer() {
        this.$emit("close-container");
      },
      updateStartDate(value) {
        this.ticketStartDate = value;
        this.ticketEndDate = null;
      },
      updateEndDate(value) {
        if (value < this.ticketStartDate) {
          this.ticketEndDate = this.ticketStartDate;
          this.ticketStartDate = value;
        } else {
          this.ticketEndDate = value;
        }
      },
      toggleDropdown(entity) {
        if (entity === 'agent' || entity === 'group') {
          this.activeFilterType = entity;
          this.isAssignAgentOpen = entity === 'agent' ? !this.isAssignAgentOpen : false;
          this.isAssignGroupOpen = entity === 'group' ? !this.isAssignGroupOpen : false;

          if (this.isAssignAgentOpen || this.isAssignGroupOpen) {
            const clickY = window.event?.clientY || 0;
            const remainingHeight = window.innerHeight - clickY;
            this.$refs.basicDropdown.forEach(d => d.checkHeight(remainingHeight < 400));
            this.options = [];
            this.offset = 0;
            this.fetchAssignedToOptions({ limit: this.recordSize, offset: this.offset, entity });
          }
        }
      },
      clearDateRange(){
        this.dateRange = { startDate: null, endDate: null };
      },
      inAssignmentFilter(filterName) {
        if (this.assignmentFilterName !== undefined && this.assignmentFilterName === "Unassigned") {
          return filterName === "Assigned to Agent";
        } 
        return false;
      },    
      onBlur() {
        this.isAssignAgentOpen = false;
        this.isAssignGroupOpen = false;
        this.options = [];
        this.offset = 0;
      },
      ensureFilterOptions() {
        if (!this.hasFetchedFilterOptions) {
          this.fetchStatusOptions();
          this.fetchSourceOptions();
          this.fetchPriorityOptions();
          this.fetchCustomFormOptions();
          this.fetchCompanyOptions();
          this.fetchWorkspaceOptions();
          this.fetchCreatedByOptions();
          this.fetchAssignedToOptions();

          this.hasFetchedFilterOptions = true;
        }
      },
      forceClose() {
        this.isOpen = false;
        this.$refs.filterPopup.forceClose();
      },
      togglingFilters(categoryOptions, categoryFilters, categoryLabel) {
        const options = categoryOptions.slice();
        if (categoryFilters.length < options.length) {
          categoryFilters.forEach(filterObj => {
            const indexToRemove = options.findIndex(option => option.name === filterObj.name);
            if (indexToRemove !== -1) {
              options.splice(indexToRemove, 1);
            }
          });
        }
        this.setUpdatingMultipleFilters(true);
        options.forEach((option, index) => {
          if (index === (options.length-1)) {
            this.setUpdatingMultipleFilters(false);
          }
          this.updateQuickFilter({ filter: categoryLabel, item: option });
        });
      },
      updateQuickFilter(data) {
        this.$emit('update:activeQuickFilterId', null);
        this.$emit('update-filter', data);
      },
      setDate(filter) {
        const customDateFilterName = "Custom date range";
        const customDateFilterType = "setCustomDateFilter";
        const dateByRangeFilterName = "Date Range";
        const closedByFilterType = "setClosedByDateFilter";
        const createdByFilterType = "setCreatedByDateFilter";

        if ([customDateFilterName, dateByRangeFilterName].includes(filter.name)) {
          const isActiveCustomDateFilter = filter.name === customDateFilterName && this.customDateFilter?.filterType?.name === customDateFilterName;
          const isActiveDateFilter = filter.name === dateByRangeFilterName && this.activeCommonQuickFilters.filter(f => f.name === dateByRangeFilterName);
          const isCloseFilter = filter.name === customDateFilterName && (this.closedByDateFilter?.filterType?.name === customDateFilterName || this.createdByDateFilter?.filterType?.name === customDateFilterName);
          if (this.customDateFilter?.name !== customDateFilterName) {
            this.ticketStartDate = null;
            this.ticketEndDate = null;
          }

          this.setFilterType = createdByFilterType;

          this.customFilterType = filter.filterType;
          this.customFilterValue = filter.value;
          this.customFilterName = filter.name;

          if (isActiveCustomDateFilter) {
            this.updateQuickFilter({ filter: customDateFilterType, item: null });
          } else if (isActiveDateFilter.length) {
            const setFilterType = isActiveDateFilter[0].criteria === 'Created at' ? createdByFilterType : closedByFilterType;
            this.updateQuickFilter({ filter: setFilterType, item: null });
          } else if (isCloseFilter) {
            const setFilterType = this.closedByDateFilter?.filterType?.name === customDateFilterName ? closedByFilterType : createdByFilterType;
            this.updateQuickFilter({ filter: setFilterType, item: null });
          } else {
            this.isDateRangeModalOpen = false;
            if (filter.name === 'Date Range') {
              this.isDateRangeModalOpen = true;
            }
            this.$refs.customTimeFrameModal.open();
            this.clearDateRange();
            this.$emit('update-close-outside', true);
          }
        } else {
          filter.setFilter();
        }
      },
      updateDateRange() {
        this.dateRange = {
          startDate: this.ticketStartDate,
          endDate: this.ticketEndDate,
        };
      },
      startDate(value) {
        this.ticketStartDate = value;
        this.updateDateRange();
      },
      endDate(value) {
        this.ticketEndDate = value;
        this.updateDateRange();
      },
      handleInput(value) {
        this.activeFilterType = 'search';
        const trimmedValue = value.trim();
        if (trimmedValue === "") {
          this.options = [];
          this.offset = 0;
          return;
        }
        const firstPart = value.toLowerCase().split(" ")[0];
        this.smartListOption = [];
        const listOptions = ["assigned to", "created by", "location", "asset", "contract", 'people', "telecom", "vendor"];
        const selectedOption = listOptions.filter(opt => opt.startsWith(firstPart.toLowerCase()));
        const smartListValues = {
          'location':'location_list',
          'asset':'asset_list',
          'contract':'contract_list',
          'people': 'people_list',
          'vendor':'vendor_list',
          'telecom':'telecom_list' ,
          'assigned to':'assigned_to_options',
          'created by': 'created_by_options',
        };
        for (let idx = 0; idx < selectedOption.length; idx += 1) {
          this.smartListOption.push(smartListValues[selectedOption[idx]]);
        }
        this.offset = 0;
        this.options = [];
        this.loadMore(value);
      },
      openInfoModal() {
        this.$refs.filterByFieldInfo.open();
      },
      closeInfoModal() {
        this.$refs.filterByFieldInfo.close();
      },
      closedTickets() {
        this.updateQuickFilter({ filter: 'setStatusFilter', item: { id: "Closed", label: "Status" , name: "Closed"} });
      },
      showMore() {
        this.offset = (!this.options || this.options.length === 0 ) ? 0 : this.offset + this.recordSize;
        this.loadMore();
      },
      filterByFieldIconClass(option) {
        const iconClass = {
          'Location' : 'nulodgicon-location',
          'Assigned' : 'nulodgicon-person',
          'Created' : 'nulodgicon-person',
          'People' : 'nulodgicon-person',
        };
        const opt = option.name.split(" ")[0];
        return iconClass[opt];
      },
      imageUrl(option) {
        const imgSrc = {
          'Asset' : 'ManagedAsset',
          'Contract' : 'Contract',
          'Vendor': 'Vendor',
          'Telecom': 'TelecomService',
        };
        const opt = option.name.split(" ")[0];
        return this.universalLinkTypeImageSrc(imgSrc[opt]);
      },
      priorityFlagUrl(name) {
        const color = {
          'Low' : 'yellow',
          'Medium' : 'orange',
          'High' : 'red',
        };
        return `https://nulodgic-static-assets.s3.amazonaws.com/images/flags/flag-${color[name]}.svg`;
      },
      commonFiltersIconClass(name) {
        if (name === 'Due soon' || name === 'Created today' || name === 'Recently closed') {
          return 'genuicon-clock-o';
        } else if (name === 'Created by me' || name === 'Assigned to me') {
          return 'nulodgicon-person';
        }
          return 'genuicon-calendar-check-o';

      },
      statusFilterClass(filter) {
        return this.filterColorClasses[filter.name] || 'bg-themed-fair';
      },
      assignmentFilterClass(name) {
        const filterClass = {
          'Assigned' : 'bg-success',
          'Unassigned' : 'bg-warning',
          'Assigned to Group': 'nulodgicon-person-stalker',
        };
        return filterClass[name] || 'nulodgicon-person';
      },
      loadMore(value) {
        const params = {
          limit: this.recordSize,
          offset: this.offset,
        };
        if (this.activeFilterType === 'search') {
          if (value && value !== "" && value.split(' ').length > 2) {
            [params.query] = value.split(' ').reverse();
          }
        } else if (this.activeFilterType === 'agent' || this.activeFilterType === 'group') {
          this.smartListOption = ['assigned_to_options'];
          params.entity = this.activeFilterType;
          this.activeFilterType = null;
        }
        for (let idx = 0; idx < this.smartListOption.length; idx += 1) {
          switch (this.smartListOption[idx]) {
            case 'assigned_to_options':
              this.fetchAssignedToOptions(params);
              break;
            case 'created_by_options':
              this.fetchCreatedByOptions(params);
              break;
            default:
              this.fetchFilteredOptions(params, this.smartListOption[idx]);
          }
        }
      },

      asyncFind: _debounce(function handleAsyncFind(query) {
        this.handleInput(query);
      }, 700),

      fetchFilteredOptions(params, opt) {
        const name ={
          'location_list': 'Location',
          'asset_list': 'ManagedAsset',
          'contract_list': 'Contract',
          'vendor_list': 'Vendor',
          'people_list': 'Contributor',
          'telecom_list': 'TelecomService',
        };
        const options = params;
        options.name = name[opt];
        options.field_type = opt;
        const url = '/smart_lists_for_custom_forms.json';
        http
          .get(url, { params })
          .then((res) => {
            const val = opt.slice(0,-5);
            this.options = this.options.concat(res.data.options.map((result) => ({ id: result.id, name: `${val.charAt(0).toUpperCase() + val.slice(1)} is ${result.name}`})));
            this.totalOptionsCount = this.smartListOption.length > 1 ? this.totalOptionsCount + res.data.count : res.data.count;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading options.`);
          });
      },
      fetchCreatedByOptions(params) {
        const url = "/created_by_options.json";
        http
          .get(url, { params })
          .then((res) => {
            if (params) {
              if (!this.options) {
                this.options = [];
              }
              this.options = this.options.concat(res.data.options.map((obj) => ({ id: obj.id, name: `Created by ${obj.name}` })));
              this.totalOptionsCount = res.data.count;
            } else {
              this.createdByOptions = res.data.options;
            }
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading created by options.`);
          });
      },
      fetchAssignedToOptions(params) {
        const url = "/assigned_to_options.json";
        http
          .get(url, { params })
          .then((res) => {
            if (params) {
              if (this.offset === 0 && params.entity === "agent") {
                this.options.push({
                  id: "unassigned",
                  name: "Assigned to none",
                  entity: "agent",
                });
              }
              this.options = this.options.concat(res.data.options.map((obj) => ({ id: obj.id, name: `Assigned to ${obj.name}`, entity: obj.entity })));
              this.handleDropdownShow();
              this.totalOptionsCount = res.data.count + (params.entity === "agent" ? 1 : 0); // adding 1 for 'Assigned to none' option. (in only the case for the agent dropdown)
            } else {
              this.assignedToOptions = res.data.options;
            }
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading assigned to options.`);
          });
      },
      fetchStatusOptions() {
        const nonRemoveable = ['Open', 'In Progress', 'Closed'];
        const url = "/status_options.json";
        http
          .get(url)
          .then((res) => {
            this.statusOptions = res.data.map((o) => {
              const { name, color } = o;
              if ((this.isMultiCompanyFilter && nonRemoveable.includes(name)) || !this.isMultiCompanyFilter) {
                return {
                  id: name,
                  name,
                  color,
                  label: 'Status',
                };
              }
              return null;
            });
            this.statusOptions = this.statusOptions.filter(option => ![undefined, null].includes(option));
            this.statusOptions.push(
              { name: 'Active', id: 'Active', label: 'Status' },
              { name: 'Archived', id: 'Archived', label: 'Status' },
              { name: 'Unread', id: 'Unread', label: 'Status' }
            );
            if (this.enableTicketDrafts) {
              this.statusOptions.push(
                { name: 'Drafts', id: 'Drafts', label: 'Status' }
              );
            }
            if (this.isAgent) {
              this.statusOptions.splice(4, 0, { name: 'New', id: 'New', label: 'Status' });
            }
            this.statusOptions = _uniqWith(this.statusOptions, (obj1, obj2) => obj1.name.toLowerCase() === obj2.name.toLowerCase());
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading status options.`);
          });
      },
      fetchSourceOptions() {
        http
          .get('/source_options.json')
          .then((res) => {
            this.sourceOptions = res.data.map((o) => ({
              id: o.id,
              name: o.name,
              label: 'Source',
            }));
          })
          .catch(() => {
            this.emitError('Sorry, there was an error loading source options.');
          });
      },
      fetchCustomFormOptions() {
        const params = { company_module: 'helpdesk', active: true, company_id: this.selectedCompanyId };
        http
          .get('/custom_form_options.json', { params })
          .then((res) => {
            this.customFormOptions = res.data.map((o) => ({
                id: o.id,
                name: o.name,
                label: 'Form',
              }));
          })
          .catch(() => {
            this.emitError('Sorry, there was an error loading company forms.');
          });
      },
      fetchPriorityOptions() {
        const nonRemoveable = ['low', 'medium', 'high'];
        const url = "/priority_options.json";
        http
          .get(url, { params: { company_module: 'HelpTicket' } })
          .then((res) => {
            this.priorityOptions = res.data.map((o) => {
              const { name, color } = o;
              if ((this.isMultiCompanyFilter && nonRemoveable.includes(name)) || !this.isMultiCompanyFilter) {
                return {
                  id: name,
                  name: this.titleize(name),
                  color,
                  label: "Priority",
                };
              }
              return null;
            });
            this.priorityOptions = this.priorityOptions.filter(Boolean);
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading priority options.`);
          });
      },
      fetchCompanyOptions() {
        const params = {
          company_module: 'HelpTicket',
          related_companies: true,
          list_filters: true,
        };
        http
          .get("/company_options.json", { params })
          .then((res) => {
            this.companyOptions = res.data;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading company options.`);
          });
      },
      fetchWorkspaceOptions() {
        let url = null;
        const company = getCompanyFromStorage();
        url = `/workspace_options.json?company_id=${company.id}`;
        http
          .get(url, { params: { privilege_name: 'HelpTicket' } })
          .then((res) => {
            this.workspaceOptions = res.data.map(workspace => ({
              id: workspace.id,
              name: workspace.name,
              iconType: workspace.iconClass,
              company: workspace.company,
              default: workspace.default,
              label: 'Workspace',
            }));
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading workspace options.`);
          });
      },
      updateFilterOption(filter) {
        if (filter.name.startsWith("Created")) {
          this.updateQuickFilter({ filter: 'setCreatedByFilter', item: { id: filter.id, name: filter.name.slice(11), label: "Created by" } });
        } else if (filter.id === "unassigned" && filter.entity === "agent") {
          this.updateQuickFilter({ filter: 'setAssignmentFilter', item: { name: "Unassigned", id: "Unassigned", label: "Assignment" } });
          this.onBlur();
        } else if (filter.name.startsWith("Assigned")) {
          this.updateQuickFilter({ filter: 'setAssignedToFilter', item: { id: filter.id, name: filter.name.slice(12), label: "Assigned to", entity: filter.entity } });
          this.onBlur();
        } else {
          this.updateQuickFilter({ filter: 'setFilterByField', item: { id: filter.id, name: filter.name } });
        }
      },
      removeFieldFilter(filter) {
        this.updateQuickFilter(filter);
      },
      applyDateRange(){
        const startDate = moment(this.ticketStartDate);
        const endDate = moment(this.ticketEndDate);
        if (this.ticketEndDate && startDate.toDate() >= endDate.toDate()) {
          this.ticketStartDate = null;
          this.ticketEndDate = null;
          this.clearDateRange();
          this.emitError("Start date should be before end date.");
        } else if (this.ticketStartDate && endDate.toDate() <= startDate.toDate()) {
          this.ticketStartDate = null;
          this.ticketEndDate = null;
          this.clearDateRange();
          this.emitError("End date should be after start date.");
        } else {
          const dateFilterType = this.currentFilterCriteria === 'Created at' ? 'setCreatedByDateFilter' : 'setClosedByDateFilter';
          const filter = dateFilterType;
          this.updateQuickFilter({
            filter,
            item: {
              filterType: {
                startDate: startDate.format().toString(),
                endDate: endDate.format().toString(),
                filter: this.customFilterType,
                name: this.customFilterName,
                criteria: this.currentFilterCriteria,
              },
              value: this.customFilterValue,
            },
          });
          this.$refs.customTimeFrameModal.close();
          this.updateCloseOutside();
        }
      },
      shouldShowFilterExclusionTooltip(filter) {
        const excludedFilters = ['Closed', 'Archived', 'New', 'Unread'];
        if (this.enableTicketDrafts) {
          excludedFilters.push('Drafts');
        }
        return excludedFilters.includes(filter.name);
      },
      filterTooltipText(filter) {
        if (filter.name === "New") {
          return "Highlights tickets with status 'New' or untouched since creation.";
        } else if (filter.name === "Unread") {
          return 'Highlights tickets assigned to you, not yet seen by any assignee.';
        } else if (filter.name === "Drafts") {
          return 'Highlights tickets with unsaved changes.';
        }
          return `${filter.name} tickets will not appear in the list unless selected.`;
      },
      getMultiSelectButtonLabel(categoryFilters, activeFilter) {
        return categoryFilters.length > activeFilter.length ? 'Select All' : 'Clear All';
      },
      handleDropdownShow() {
        this.$nextTick(() => {
          if(this.$refs.multiSelectAssignee) {
            const multiSelect = this.$refs.multiSelectAssignee[0];
            if (multiSelect) {
              multiSelect.isOpen = true;
            }
          }
        });
      },
      isAssignedAgent(filterName) {
        return filterName === 'Assigned to Agent';
      },
      isAssignedGroup(filterName) {
        return filterName === 'Assigned to Group';
      },
      shouldShowFilterOption(option, hoveredIndex) {
        return option.name?.length > 32 && hoveredIndex === option.id;
      },
      isPriorityFilterPresent(filterName) {
        return this.priorityFilter?.filter(f => f.name === filterName)?.length;
      },
      activeCommonQuickFiltersPresent(filterName) {
        return this.activeCommonQuickFilters?.filter(f => f.name === filterName)?.length;
      },
      isSlaFilterPresent(filterName) {
        return this.slaFilter?.filter(f => f.name === filterName)?.length;
      },
      isWorkspaceFilterPresent(filterName) {
        return this.workspaceFilter?.filter(f => f.name === filterName)?.length;
      },
      selectFilterCriteria(criteria) {
        if (criteria === this.currentFilterCriteria) {
          this.currentFilterCriteria = null;
        } else {
          this.currentFilterCriteria = criteria;
        }
      },
      updateCloseOutside() {
        this.$emit('update-close-outside', false);
      },
    },
  };

</script>

<style lang="scss" scoped>
  .module-type-styling {
    height: 1rem;
    width: 1rem;
  }

  .filter-by-field-select {
    :deep(.multiselect__tags) {
      width: 415px;
    }

    :deep(.multiselect__select) {
      display: none;
    }
  }

  .multiselect {
    :deep(.multiselect__content-wrapper),
    .filter-company {
      width: 415px;
    }
  }

  .field-filter-tag {
    &.active .filter-wrapper {
      background: $info;
      border-radius: $border-radius;
      color: $light;
      margin-left: -0.1875rem;
      padding: 0.25rem 0.5rem .25rem .25rem;

      i:not(.genuicon-priority) {
        color: $themed-light !important;
      }

      img {
        filter: invert(1);
      }
    }

    &:not(.active):hover .filter-wrapper {
      background: $themed-light;
      border-radius: $border-radius;
      margin-left: -0.1875rem;
      padding: 0.1875rem 0.1875rem;
    }
  }

  .select-per-page-filter {
    max-height: unset;
    min-height: 38px;
    width: 4rem;
    border-radius: 8px;
  }

  .truncate-name {
    max-width: 220px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .date-range-modal {
    :deep(.sweet-modal) {
      overflow: unset;
      width: 26.25rem;
    }

    :deep(.multiselect__tags),
    :deep(.multiselect__content-wrapper) {
      width: 100%;
    }
  }

  .filter-row--with-overflow {
    max-height: 8rem;
    overflow: auto;
  }

  .genuicon-clipboard {
    color: $themed-muted;
    position: relative;
    top: 0.125rem;
  }

  .genuicon-sla {
    font-size: 1.0625rem;
    position: relative;
    top: 0.25rem;
  }

  .multiselect-assignee {
    :deep(.multiselect__tags),
    :deep(.multiselect__select) {
      display: none !important;
    }

    :deep(.multiselect__content-wrapper) {
      width: 21rem;
    }
  }

  .show-more-btn {
    padding-left: 0.75rem !important
  }

  .workspace-filter-view {
    filter: var(--themed-light-icon-filter);
  }

  .header-top-margin {
    margin-top: 1rem;
  }

  .inherit-color {
    color: inherit;
  }
</style>
