<template>
  <Teleport to="body">
    <sweet-modal
      ref="newTemplateFormModal"
      v-sweet-esc
      :title="templateModalTitle"
      modal-theme="right"
      class="newtemplate-modal"
      @close="handleCancel"
    >
      <template
        v-if="emailTemplate"
        slot="default"
      >
        <div>
          <div class="box h-auto bg-titan-white shadow-border-300">
            <div class="row">
              <div class="col-auto pr-1">
                <i class="nulodgicon-android-bulb pastel-android-bulb h5 mr-2" />
                Customize the email sent to the ticket creator after {{ templateSubTitle }}.
              </div>
            </div>
          </div>
          <div>
            <label class="h6 not-as-big mt-4">
              Template Title
            </label>
            <input
              id="name"
              ref="templateTitleInput"
              v-model="emailTemplate.templateTitle"
              v-validate="'required'"
              :disabled="disabled || !isWrite"
              type="text"
              class="form-item form-control"
              name="title"
            >
            <span
              v-if="errors.has('title')"
              class="form-text small text-danger"
            >
              {{ errors.first('title') }}
            </span>
          </div>
          <div>
            <label class="h6 not-as-big mt-4">
              Subject Title
            </label>
            <input
              id="title"
              ref="subjectTitleInput"
              v-model="emailTemplate.subjectTitle"
              v-validate="'required'"
              :disabled="disabled || !isWrite"
              type="text"
              class="form-item form-control"
              name="subject"
            >
            <span
              v-if="errors.has('subject')"
              class="form-text small text-danger"
            >
              {{ errors.first('subject') }}
            </span>
          </div>
          <div
            class="form-group mb-0 mt-5"
          >
            <input
              id="message"
              ref="commentInput"
              v-validate="'required'"
              :value="emailTemplate.emailBody"
              type="hidden"
              name="body"
            >
            <trix-vue
              ref="trixEditor"
              v-model="emailTemplate.emailBody"
              input-id="message"
              class="suggestion-item"
              :disabled="disabled || !isWrite"
              :label="'Email Body'"
              :show-title-bar="true"
              :display-survey-section="displaySurveySection"
            />
            <span
              v-if="errors.has('body')"
              class="form-text small text-danger"
            >
              {{ errors.first('body') }}
            </span>
          </div>
        </div>
      </template>
      <div slot="button">
        <button
          class="btn btn-link text-secondary mr-2"
          @click.stop="handleCancel"
        >
          Cancel
        </button>
        <button
          class="btn btn-secondary color-white mr-2" 
          @click="openPreview"
        >
          Preview
        </button>
        <button
          v-if="!disabled || !isWrite"
          class="btn btn-primary submit-button font-weight-semi-bold"
          @click.stop.prevent="saveNewTemplate"
        >
          {{ templateButton }}
        </button>
      </div>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import { mapGetters } from 'vuex';
  import _cloneDeep from 'lodash/cloneDeep';
  import permissionsHelper from 'mixins/permissions_helper';
  import TrixVue from '../../trix_vue.vue';

  export default {
    $_veeValidate: {
      validator: "new",
    },
    components: {
      SweetModal,
      TrixVue,
    },
    mixins: [permissionsHelper],
    props: {
      selectedTemplate: {
        type: Object,
        default: () => {},
      },
      section: {
        type: String,
        default: '',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        emailTemplate: this.selectedTemplate ? _cloneDeep(this.selectedTemplate) : {
          templateTitle: '',
          subjectTitle: '',
          emailBody: '',
        },
        emailSection:  _cloneDeep(this.section),
        isNewChecklistItem: false,
      };
    },
    computed: {
      ...mapGetters(['emailTemplateType', 'displaySurveySection']),
      templateModalTitle() {
        const sectionTitles = {
          'ticket created': {
            default: 'Add "Ticket Was Created" Email',
            edit: 'Edit "Ticket Was Created" Email',
            view: '"Ticket Was Created" Email',
          },
          'comment added': {
            default: 'Add "Comment Was Added" Email',
            edit: 'Edit "Comment Was Added" Email',
            view: '"Comment Was Added" Email',
          },
          'ticket assigned': {
            default: 'Add "Ticket Was Assigned" Email',
            edit: 'Edit "Ticket Was Assigned" Email',
            view: '"Ticket Was Assigned" Email',
          },
          'closed survey': {
            default: 'Add "Ticket Was Closed" Email',
            edit: 'Edit "Ticket Was Closed" Email',
            view: '"Ticket Was Closed" Email',
          },
        };

        const sectionTitle = sectionTitles[this.section];
        if (!sectionTitle) return '';

        if (this.disabled) {
          return sectionTitle.view;
        }

        return this.selectedTemplate?.id ? sectionTitle.edit : sectionTitle.default;
      },
      templateSubTitle() {
        const subTitles = {
          'ticket created': 'the ticket is created',
          'comment added': 'a comment is added',
          'ticket assigned': 'an agent is assigned',
          'closed survey': 'ticket is closed',
        };
        return subTitles[this.section] || '';
      },
      templateButton() {
        return this.selectedTemplate?.id ? 'Update Email Template' : 'Save Email Template';
      },
    },
    watch: {
      selectedTemplate() {
        this.emailTemplate = _cloneDeep(this.selectedTemplate);
      },
      section() {
        this.emailSection = _cloneDeep(this.section);
      },
    },
    methods: {
      openPreview() {
        localStorage.setItem('emailPreviewData', JSON.stringify({
          subjectTitle: this.emailTemplate.subjectTitle,
          emailBody: this.emailTemplate.emailBody,
        }));
        const baseUrl = window.location.origin;
        const fullUrl = `${baseUrl}/preview_email`;
        window.open(fullUrl, '_blank');
      },
      saveNewTemplate() {
        this.$validator.validateAll().then((result) => {
          if (result) {
            this.emailTemplate?.id ? this.updateTemplate() : this.saveTemplate();
          } else {
            this.emitError("Please correct the highlighted errors before submitting.");
          }
        });
      },
      saveTemplate() {
        const params = {
          email_template: {
            template_title: this.$refs.templateTitleInput.value,
            subject_title: this.$refs.subjectTitleInput.value,
            email_body: this.$refs.trixEditor.$refs.trixEditor.innerHTML,
            template_type: this.emailTemplateType,
            template_name: this.section,
          },
        };
        http
          .post('/email_templates.json', params)
          .then(() => {
            this.$emit("input");
            this.close();
            this.emitSuccess('Template created successfully.');
          })
          .catch(error => {
            this.emitError(`Sorry there was an error creating the template. ${error.response.data.errors}`);
          });
      },
      updateTemplate() {
        http
          .put(`/email_templates/${this.emailTemplate.id}.json`, { email_template: this.emailTemplate })
          .then(() => {
            this.$emit("input");
            this.close();
            this.emitSuccess("Template updated successfully!");
          })
          .catch((error) => {
            this.emitError(`Sorry there was an error updating this template. ${error.response.data.errors}`);
          });
      },
      open() {
        this.$refs.newTemplateFormModal.open();
      },
      handleCancel() {
        this.resetDate();
        this.close();
        this.$emit('cancel');
      },
      resetDate() {
        this.$refs.templateTitleInput.value = '';
        this.$refs.subjectTitleInput.value = '';
      },
      close() {
        if (this.$refs.newTemplateFormModal.visible) {
          this.$refs.newTemplateFormModal.close();
        };
      },
    },
  };
</script>

<style lang="scss" scoped>
  .newtemplate-modal {
    :deep(.sweet-title) {
      background-color: $themed-dark-drawer-bg;
      color: white;
      font-size: 1rem;
    }

    :deep(.sweet-modal) {
      max-width: 60rem;
    }

    :deep(.sweet-action-close) {
      color: white !important;
    }

    :deep(.sweet-content-content) {
      width: 100%;
    }

    :deep(.sweet-buttons) {
      bottom: 0;
      left: 0;
      position: sticky;
      width: 100%;
      z-index: 5;
      background-color: var(--themed-box-bg);
    }

    @media screen and (max-width: 84.375rem) and (min-width: 48.125rem) {
      :deep(.is-mobile-fullscreen) {
        width: 70% !important;
      }
    }
  }

  .suggestion-item {
    :deep(trix-editor) {
      min-height: 27rem !important;
    }
  }

  :deep(.sweet-modal.theme-right) {
    width: 52vw;
  }
</style>
