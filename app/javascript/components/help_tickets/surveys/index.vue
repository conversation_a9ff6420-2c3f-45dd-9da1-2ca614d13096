<template>
  <div v-if="displaySurveySection">
    <resources-menu active="articles" />

    <div v-if="isLoading">
      <h4 class="mt-5">
        <span class="float-left mr-2">
          Loading surveys
        </span>
        <span>
          <sync-loader
            :loading="true"
            class="float-left"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </h4>
    </div>

    <div 
      v-else
      class="mt-5"
    >
      <div class="box box--with-heading box--flat">
        <div class="box__header d-flex align-items-end">
          <h5 class="box-title mr-2.5">
            <i class="box-title__icon genuicon-build-faqs d-flex mr-2" />
            <span>Surveys</span>
          </h5>
          <p class="mb-0 not-as-small text-themed-fair">
            Create and trigger custom surveys to find out how your team can better help your users.
          </p>
        </div>
        <div class="box__inner bg-themed-light p-4">
          <div class="row ml-0 mr-n4 mb-n4">
            <div
              v-for="(survey, index) in surveysData"
              :key="index"
              class="col-xl-3 col-lg-4 col-md-6 pr-4 pl-0 mb-4"
            >
              <survey-card
                :survey="survey"
                @edit-survey="openSurveyModal(survey)"
                @toggle-survey-visibility="toggleVisibility(survey)"
                @delete-survey="openDeleteTemplateModal(survey)"
              />
            </div>

            <div 
              v-if="!isRead"
              class="col-xl-3 col-lg-4 col-md-6 pl-0 pr-4 mb-4"
            >
              <div
                class="box box--with-hover px-4 text-center justify-content-center align-content-center add-new-survey-box"
                @click="openSurveyModal(initSurvey)"
              >
                <div class="pt-2 pb-2 text-secondary">
                  <i class="nulodgicon-plus-round mr-1" />
                  Add New Survey
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="!isLoading"
      class="mt-4"
    >
      <div>
        <div class="box box--with-heading box--flat">
          <div class="box__heading">
            <div class="d-flex align-items-center">
              <i class="genuicon-build-responses d-flex mr-2 mb-n1" />
              <h5 class="font-weight-normal mb-0">
                Survey Responses
              </h5>
              <pulse-loader
                v-if="isLoadingResponses"
                class="ml-2"
                color="#0d6efd"
                size="0.5rem"
                :loading="true"
              />
            </div>
          </div>
          <div class="row col-12 mt-3 justify-content-between">
            <input
              id="searchInput"
              maxlength="100"
              type="text"
              class="form-control input-text-field ml-3"
              placeholder="Search by name or respondent"
              @input="updateSearch"
            >
            <div>
              <paginate
                v-if="pageCount > 0"
                ref="paginate"
                class="float-right mb-0"
                :click-handler="pageSelected"
                :container-class="'pagination pagination-sm mb-0'"
                :next-class="'next-item'"
                :next-link-class="'page-link'"
                :next-text="'Next'"
                :page-class="'page-item'"
                :page-count="pageCount"
                :page-link-class="'page-link'"
                :prev-class="'prev-item'"
                :prev-link-class="'page-link'"
                :prev-text="'Prev'"
                :selected="page"
              />
              <dropdown-filter
                class="float-right"
                label="Survey"
                name="title"
                :options="surveyOptions"
                :value="selectedFilterSurvey"
                @selected="filterSurvey"
              />
              <dropdown-filter
                class="float-right"
                label="Status"
                name="name"
                :options="surveyStatuses"
                :value="selectedStatus"
                @selected="filterStatus"
              />
            </div>
          </div>
          <div class="box__inner">
            <div
              v-if="!surveyResponses.length"
              class="text-center bg-light rounded no-responses-present p-5 d-flex align-items-center justify-content-center"
            >
              <div>
                <h4 class="text-secondary">
                  No responses present.
                </h4>
                <p class="text-muted mb-0">Try removing some filters or clearing the search bar.</p>
              </div>
            </div>
            <scrollable-table
              v-else
              class="mb-0"
              is-responses-page
              :table-class="'table table--spaced not-as-small mb-0'"
            >
              <thead>
                <table-header-row
                  sort-by-key="title"
                  :table-header="tableHeaders"
                  :list-table-layout-module-style-override="`survey_responses`"
                  :active-sort="activeSort"
                  :active-sort-direction="activeSortDirection"
                  @change="sortResponses"
                />
              </thead>
              <tbody>
                <tr
                  v-for="response in surveyResponses"
                  :key="response.id"
                  class="box--with-hover"
                  @click.stop.prevent="openResponseModal(response)"
                >
                  <td class="align-middle stats-column-2">
                    <div class="d-flex">
                      <span class="font-weight-semi-bold mb-0 text-truncate response-text">
                        {{ response.name }}
                      </span>
                    </div>
                  </td>
                  <td class="align-middle truncate">
                    <div
                      v-if="response.respondee"
                      class="row pr-2"
                    >
                      <div class="col-auto pr-0">
                        <avatar
                          :size="20"
                          :custom-style="{'opacity': 0.8, 'line-height': 20 }"
                          :username="`${response.respondee.name}`"
                          class="logo-outline"
                        />
                      </div>
                      <div class="text-dark col pl-2">
                        {{ response.respondee.name }}
                      </div>
                    </div>
                  </td>
                  <td class="true-small align-middle truncate">
                    <div
                      class="px-2 rounded-pill d-inline-flex font-weight-semi-bold align-items-center"
                      :class="statusColor(response.status)"
                    >
                      {{ response.status }}
                    </div>
                  </td>
                  <td class="align-middle truncate">
                    {{ getUpdatedAt(response.updatedAt) }}
                  </td>
                  <td class="not-as-small align-middle truncate">
                    {{ response.score }}
                  </td>
                  <td class="align-middle truncate">
                    <span class="ml-4">{{ response.comment.length }}</span>
                  </td>
                  <td class="align-middle truncate clickable">
                    <i
                      v-tooltip.left="'Help Center Ticket'"
                      class="nulodgicon-external-link external-link-size ml-4"
                      @click.stop.prevent="openTicket(response)"
                    />
                  </td>
                  <td class="align-middle text-right pr-4">
                    <i
                      v-if="response.status === 'unopened'"
                      v-tooltip="`Resend Survey`"
                      class="h6 nulodgicon-paper-airplane mr-1"
                      @click.stop.prevent="resendSurveyEmail(response)"
                    />
                    <i
                      v-tooltip="`View Survey Results`"
                      class="h6 nulodgicon-eye mr-4"
                      @click.stop.prevent="openResponseModal(response)"
                    />
                  </td>
                </tr>
              </tbody>
            </scrollable-table>
          </div>
        </div>
      </div>
    </div>

    <sweet-modal
      ref="deleteTemplateModal"
      class="modal-top"
      title="Before you delete this Survey..."
      @close="closeDeleteTemplateModal"
    >
      <template slot="default">
        <div class="text-center">
          <h6 class="mb-3">
            Are you sure you want to delete this Survey?
          </h6>
        </div>
      </template>
      <div class="d-flex justify-content-end mt-4">
        <button
          slot="button"
          class="btn btn-link text-secondary mr-2"
          @click.stop="handleClose"
        >
          Cancel
        </button>
        <button
          slot="button"
          class="btn btn-link text-danger"
          :disabled="isDeleteBtnDisabled"
          @click.stop="deleteSurveyTemplate"
        >
          Delete
        </button>
      </div>
    </sweet-modal>

    <survey-modal
      ref="editSurveyModal"
      :selected-survey="selectedSurvey"
      :section="currentSection"
      :disabled="disabled"
      @fetch-surveys="fetchCustomSurveys"
      @input="resetData"
      @cancel="resetData"
    />

    <sweet-modal
      ref="submittedResponseModal"
      v-sweet-esc
      :title="`Submitted Response`"
      modal-theme="dark-header theme-centered-title"
      class="preview-template-modal"
      width="85%"
    >
      <template slot="default">
        <survey-response
          ref="submittedResponseModal"
          is-response-preview
          :survey-response="selectedResponse"
          @cancel="closeResponseModal"
        />
      </template>
    </sweet-modal>

    <sweet-modal
      ref="noResponseModal"
      v-sweet-esc
      class="text-center"
      :title="`Survey Awaiting Completion`"
      modal-theme="dark-header theme-centered-title"
      width="50%"
    >
      <h5>We are waiting for the response on this survey.</h5>
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import Paginate from 'vuejs-paginate';
  import _debounce from "lodash/debounce";
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import { SweetModal } from 'sweet-modal-vue';
  import { mapGetters } from 'vuex';
  import vClickOutside from 'v-click-outside';
  import { Avatar } from 'vue-avatar';
  import permissionsHelper from 'mixins/permissions_helper';
  import MomentTimezone from 'mixins/moment-timezone';
  import ScrollableTable from 'components/shared/scrollable_table.vue';
  import TableHeaderRow from "components/shared/list_view_table_header.vue";
  import ResourcesMenu from "components/help_tickets/resources_menu.vue";
  import DropdownFilter from 'components/shared/dropdown_filter.vue';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import SurveyCard from './survey_card.vue';
  import SurveyModal from './survey_modal.vue';
  import SurveyResponse from './survey_response.vue';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      Avatar,
      SurveyModal,
      ScrollableTable,
      TableHeaderRow,
      ResourcesMenu,
      SurveyResponse,
      SweetModal,
      SyncLoader,
      Paginate,
      DropdownFilter,
      PulseLoader,
      SurveyCard,
    },
    mixins: [permissionsHelper, MomentTimezone],
    data() {
      return {
        isLoading: true,
        selectedSurvey: null,
        surveyToDelete: null,
        initSurvey: {
          title: null,
          customDesign: false,
          visible: false,
          isDefault: false,
          customForms: [{ id: 0, name: 'All Custom Forms', label: 'Form' }],
          backgroundColor: null,
          questions: [
            { 
              questionText: '',
              questionType: 'choice',
              choices: [
                { emoji: '🙂', score: '0', text: '', order: 0 },
              ],
            },
          ],
          rules: [],
          trigger: {
            matchAll: false,
            displayQuestion: false,
            allowAgents: false,
            buttonText: null,
            customSurveyQuestionId: null,
            conditions: [
              {
                start:
                { 
                  name: "status", 
                  fieldAttributeType: "status", 
                  label: "Status",
                }, 
                middle: "is",
                end: "Closed",
              },
            ],
          },
        },
        disabled: false,
        currentSection: null,
        deleteTemplate: null,
        showDeleteButton: false,
        surveysData: [],
        surveyResponses: [],
        page: 0,
        perPage: 20,
        pageCount: 1,
        selectedResponse: null,
        selectedFilterSurvey: null,
        selectedStatus: null,
        surveyStatuses: [
          { name: 'Opened' },
          { name: 'Unopened' },
          { name: 'Completed' },
        ],
        isLoadingResponses: false,
        surveySearch: '',
        activeSort: 'name',
        activeSortDirection: 'asc',
        isDeleteBtnDisabled: false,
      };
    },
    computed: {
      ...mapGetters(['displaySurveySection']),
      tableHeaders() {
        return [
          { "title": "Name", "name": "name", "sortBy": 'name'},
          { "title": "Respondent", "name": "respondent", "sortBy": 'respondent'},
          { "title": "Status", "name": "status", "sortBy": 'status'},
          { "title": "Last Action Date", "name": "last_action_date", "sortBy": 'last_action_date'},
          { "title": "Score", "name": "score", "sortBy": 'score'},
          { "title": "Comments", "name": "comment_count", "sortBy": 'comment_count'},
          { "title": "Linked Ticket"},
          { "title": "Actions"},
        ];
      },
      surveyOptions() {
        return this.surveysData.map((survey) => ({
            id: survey.id,
            name: survey.title,
          }));
      },
      isHelpDeskSettings(){
        return this.$route.name === 'settings-survey';
      },
    },
    methods: {
      onWorkspaceChange() {
        this.resetResponsesFilters();
        this.handleBasicUserAccess('Surveys');
        this.fetchCustomSurveys();
        this.fetchSurveyResponses();
      },
      fetchCustomSurveys() {
        http
          .get('/custom_surveys.json')
          .then((res) => {
            this.surveysData = res.data.customSurveys;
            this.isLoading = false;
          })
          .catch((error) => {
            this.isLoading = false;
            this.emitError(`Sorry, there was an error fetching surveys. ${error.response.data.message}`);
          });
      },
      openSurveyModal(survey) {
        this.selectedSurvey = survey;
        this.$refs.editSurveyModal.open();
      },
      resetData() {
        this.disabled = false;
        this.selectedSurvey = null;
      },
      openDeleteTemplateModal(survey) {
        if (this.survey?.isDefault) {
          return;
        }
        this.surveyToDelete = { ...survey };
        this.$refs.deleteTemplateModal.open();
      },
      closeDeleteTemplateModal() {
        this.surveyToDelete = null;
      },
      handleClose() {
        this.closeDeleteTemplateModal();
        this.$refs.deleteTemplateModal.close();
      },
      deleteSurveyTemplate() {
        this.isDeleteBtnDisabled = true;
        http
          .delete(`/custom_surveys/${this.surveyToDelete.id}.json`)
          .then(() => {
            const sIdx = this.surveysData.findIndex((sv) => sv.id === this.surveyToDelete.id);
            this.surveysData.splice(sIdx, 1);
            this.handleClose();
            this.emitSuccess('Survey deleted successfully!');
            this.isDeleteBtnDisabled = false;
          })
          .catch(error => {
            this.handleClose();
            this.isDeleteBtnDisabled = false;
            this.emitError(`Sorry, there was an error deleting survey. ${error.response.data.message}`);
          });
      },
      statusColor(status) {
        const statusClasses = {
          completed: "bg-blue-subtle text-blue-dark",
          unopened: "bg-red-subtle text-red-dark",
          opened: "bg-orange-subtle text-orange-dark",
        };

        return statusClasses[status] || "bg-blue-subtle text-secondary";
      },
      fetchSurveyResponses() {
        this.isLoadingResponses = true;
        const params = {
          per_page: this.perPage,
          page: this.page,
          status_filter: this.selectedStatus,
          survey_filter: this.selectedFilterSurvey,
          search_filter: this.surveySearch,
          sort_column: this.activeSort,
          sort_direction: this.activeSortDirection,
        };

        http
          .get('/custom_surveys_responses.json', { params })
          .then((res) => {
            this.surveyResponses = res.data.surveyResponses;
            this.pageCount = res.data.totalPages;
            this.isLoadingResponses = false;
          })
          .catch(() => {
            this.isLoadingResponses = false;
            this.emitError('Sorry, there was an error fetching survey responses.');
          });
      },
      toggleVisibility(survey) {
        survey.visible = !survey.visible;
        // this.surveysData[index].visible = !this.surveysData[index].visible;
        const params = { visible: survey.visible };
        http
          .put(`/custom_surveys/${survey.id}/toggle_active.json`, params)
          .then(() => {
            this.emitSuccess('Custom Survey updated successfully.');
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error updating survey. ${error.response.data.message}`);
            survey.visibility = !survey.visibility; // Reset visiblity on error to prevent a false state
          });
      },
      openModal() {
        this.openSurveyModal(this.initSurvey);
      },
      pageSelected(p) {
        this.page = p - 1;
        this.fetchSurveyResponses();
      },
      openTicket(response) {
        window.open(`/help_tickets/${response.helpTicketId}`, '_blank');
      },
      openResponseModal(response) {
        if (response.status === 'completed') {
          this.selectedResponse = response;
          this.$refs.submittedResponseModal.open();
        } else {
          this.$refs.noResponseModal.open();
        }
      },
      closeResponseModal() {
        this.selectedResponse = null;
        this.$refs.submittedResponseModal.close();
      },
      getUpdatedAt(date) {
        return date ? this.timezoneDatetime(date, Vue.prototype.$timezone) : null;
      },
      resendSurveyEmail(response) {
        http
          .get('/custom_surveys_response/resend_email', { params: response })
          .then(() => {
            this.emitSuccess('Email has been sent to the respondent successfully.');
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error while resending survey email. ${e.response.data.message}`);
          });
      },
      filterStatus(status) {
        this.selectedStatus = status;
        this.page = 0;
        this.pageCount = 1;
        this.fetchSurveyResponses();
      },
      filterSurvey(survey) {
        this.selectedFilterSurvey = survey;
        this.page = 0;
        this.pageCount = 1;
        this.fetchSurveyResponses();
      },
      updateSearch: _debounce(
        function updateSurveySearch(input) {
          this.surveySearch = input.target.value;
          this.page = 0;
          this.pageCount = 1;
          this.fetchSurveyResponses();
        },
        700
      ),
      sortResponses(header) {
        this.activeSort = header.sortBy;
        this.activeSortDirection = this.activeSortDirection === 'asc' ? 'desc' : 'asc';
        this.page = 0;
        this.pageCount = 1;
        this.fetchSurveyResponses();
      },
      resetResponsesFilters() {
        this.page = 0;
        this.selectedStatus = null;
        this.selectedFilterSurvey = null;
        this.surveySearch = '';
        this.surveyResponses = [];
      },
    },
  };
</script>

<style lang="scss" scoped>
  .response-text {
    width: 15rem;
  }
  
  .preview-template-modal :deep {
    .sweet-content {
      padding: 0 0 .5rem 0;
    }
  }

  .input-text-field {
    width: 30rem;
  }

  .no-responses-present {
    height: 30vh;
  }

  .modal-top {
    top: 3rem;
  }

  .disabled-link {
    opacity: 0.5;
  }

  .add-new-survey-box {
    min-height: 8rem;
  }
</style>
