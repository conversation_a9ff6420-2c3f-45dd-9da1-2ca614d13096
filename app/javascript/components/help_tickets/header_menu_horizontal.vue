<template>
  <div class="mt-4">
    <div class="sub-menu clearfix">
      <div
        v-if="!permissions"
        class="module-sub-tabs float-left my-sm-2 my-md-0"
      >
        <div
          v-for="(index) in 3"
          :key="`primary-menu-item-${index}`"
          class="sub-menu-item"
        >
          <span
            class="sub-menu-item skeleton skeleton__item w-100"
            style="font-size: 1.75rem; min-width: 3.75rem;"
          />
        </div>
        <span class="sub-menu-separator" />
        <div
          v-for="(index) in 4"
          :key="`secondary-menu-item-${index}`"
          class="sub-menu-item"
        >
          <span
            class="sub-menu-item skeleton skeleton__item w-100"
            style="font-size: 1.75rem; min-width: 3.5rem;"
          />
        </div>

      </div>
      <div
        v-else
        class="module-sub-tabs float-left my-sm-2 my-md-0 sub-tabs-overflow"
      >
        <router-link
          v-if="isWrite || isReadAny"
          id="dashboardBtn"
          class="sub-menu-item"
          to="/dashboard"
          :class="{ 'router-link-exact-active': isDashboard }"
          @click.native="onModuleLinkClick"
        >
          <i class="genuicon-nav-dashboard sub-menu-item__icon" />
          Dashboard
        </router-link>
        <router-link
          to="/"
          class="sub-menu-item"
          :class="{ 'router-link-exact-active': isHelpTickets }"
          data-tc-tickets
          @click.native="onModuleLinkClick"
        >
          <i class="genuicon-nav-tickets sub-menu-item__icon" />
          Tickets
        </router-link>
        <div v-tooltip.bottom="tooltipMessage">
          <div
            :class="disableLinks"
            class="module-sub-tabs"
          >
            <router-link
              v-if="displayPeople && isAdminOrAgent"
              class="sub-menu-item"
              :to="peopleRoutes"
            >
              <i class="nulodgicon-person-stalker sub-menu-item__icon" />
              People
            </router-link>
            <router-link
              v-if="isWrite"
              id="incoming_btn"
              class="sub-menu-item"
              data-tc-incoming
              to="/incoming"
              :class="{ 'router-link-exact-active': isIncomingRoute }"
              @click.native="onModuleLinkClick"
            >
              <i class="genuicon-nav-incoming sub-menu-item__icon" />
              Incoming
              <span
                v-if="incomingCount > 0"
                class="data-badge sub-menu-badge"
              >
                {{ incomingCount }}
              </span>
            </router-link>
            <span class="sub-menu-separator" />
            <router-link
              v-if="isWriteAny || isReadAny || isBasicAccess"
              class="sub-menu-item"
              to="/workspaces"
              :class="{'router-link-exact-active': isWorkspacesPage}"
              data-tc-workspaces-tab
              @click.native="onModuleLinkClick"
            >
              <i class="genuicon-workspace sub-menu-item__icon" />
              Workspaces
            </router-link>
            <router-link
              v-if="isWriteAny || isReadAny || isBasicAccess"
              class="sub-menu-item"
              to="/articles"
              :class="{'router-link-exact-active': isResourcesRoute}"
              data-tc-resources-tab
              @click.native="onModuleLinkClick"
            >
              <i class="genuicon-nav-resources sub-menu-item__icon" />
              Resources
            </router-link>
            <router-link
              v-if="isWrite || isRead"
              class="sub-menu-item"
              to="/reports/saved"
              data-tc-reporting-tab
              :class="{ 'router-link-exact-active': isReportsRoute }"
              @click.native="onModuleLinkClick"
            >
              <i class="genuicon-nav-reporting sub-menu-item__icon" />
              Reporting
            </router-link>
            <router-link
              v-if="isWrite || isRead"
              class="sub-menu-item"
              to="/scheduled_tasks"
              :class="{ 'router-link-exact-active': isTaskSchedulerRoute }"
              data-tc-task-scheduler-tab
              @click.native="onModuleLinkClick"
            >
              <i class="genuicon-nav-task-scheduler sub-menu-item__icon" />
              Task Scheduler
            </router-link>
            <router-link
              v-if="isWrite"
              class="sub-menu-item"
              to="/automated_tasks"
              :class="{ 'router-link-exact-active': isAutomations }"
              data-tc-automation-tab
              @click.native="onModuleLinkClick"
            >
              <i class="genuicon-nav-automation sub-menu-item__icon" />
              Automation
            </router-link>
          </div>
        </div>
      </div>
      <common-sub-menu
        help-center-link="helpdesk"
        reports
        :class="disableLinks"
        @on-permissions-click="goToTicketPermissions"
      />
    </div>
    <sweet-modal
      ref="staffInModule"
      v-sweet-esc
      title="People with Help Desk module permissions"
      width="50%"
      data-tc-view-modal="help desk permissions"
      modal-theme="dark-header theme-centered-title"
      @close="setTopUsersToZero"
    >
      <template slot="default">
        <module-users
          :load-data="showStaff"
          :mod-name="'helpdesk'"
          :permission-name="'HelpTicket'"
        />
      </template>
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapMutations, mapGetters } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from "mixins/permissions_helper";
  import ModuleUsers from "components/shared/group_permissions/module_users.vue";
  import CommonSubMenu from "components/shared/common_sub_menu_actions.vue";
  import Pusher from 'common/pusher';
  import mspHelper from 'mixins/msp_helper';
  import ticketHelper  from 'mixins/ticket_helper';
  import adminUser from 'mixins/admin_specific';

  export default {
    components: {
      SweetModal,
      ModuleUsers,
      CommonSubMenu,
    },
    mixins: [ permissionsHelper, Pusher, mspHelper, ticketHelper, adminUser],
    data() {
      return {
        showStaff: false,
        incomingCount: 0,
        displayPeople: false,
      };
    },
    computed: {
      ...mapGetters(['topUsers']),
      isResourcesRoute() {
        return (
          this.$route.path.startsWith('/responses') ||
          this.$route.path.startsWith('/task_checklists') ||
          this.$route.path.startsWith('/surveys') ||
          this.$route.path.startsWith('/articles') ||
          this.$route.path.startsWith('/faqs') ||
          this.$route.path.startsWith('/documents')
      );
    },
    isAdminOrAgent() {
      return this.isAdmin || this.isAgent;
    },
    isIncomingRoute() {
      return (
          this.$route.path.startsWith('/incoming')
      );
    },
    isImportPage() {
      return this.$route.path === "/import_help_tickets";
    },
    isWorkspacesPage() {
      return this.$route.path.match(/workspaces/);

    },
    isDashboard() {
      return this.$route.name === 'ticket-dashboard';
    },
    isHelpTickets() {
      return this.$route.name === 'help-tickets' || this.$route.name === 'help-tickets-all';
    },
    peopleRoutes() {
      return this.$route.path.includes('agents') ? '/agents' : '/people';
    },
    isLogs() {
      return (
          this.$route.path.startsWith('/ticket_activities') ||
          this.$route.path.startsWith('/settings/event_log')
      );
    },
    isReportsRoute() {
      return this.$route.path.includes('reports');
    },
    isTaskSchedulerRoute() {
      return this.$route.path.includes('scheduled_tasks');
    },
    isAutomations() {
      return this.$route.name === 'automated-tasks';
    },
  },
  methods: {
    ...mapMutations('GlobalStore', ['setVerticalNav']),
    ...mapMutations([
      'setTopUsers',
    ]),
    shouldDisplayPeople() {
      http
        .get('/help_tickets/people/should_display_people.json')
        .then((res) => {
          this.displayPeople = res.data.displayPeople;
        }) 
        .catch(() => {
          this.emitError("Something went wrong. Please refresh the page and try again.");
        });
    },
    onModuleLinkClick() {
      const navTheme = document.cookie.split('; ').find(row => row.startsWith('nav-theme='));
      if (navTheme && navTheme.split('=')[1] === 'vertical') {
        this.setVerticalNav(true); 
      }
    },
    onWorkspaceChange() {
      this.shouldDisplayPeople();
      if (this.isWrite) {
        this.fetchTicketEmailCount();
        this.setupPusherListeners();
      }
    },
    setupPusherListeners() {
      Pusher.then(() => {
        if (this.$pusher) {
          const channel = this.$pusher.subscribe(this.$currentCompanyGuid);
          channel.bind('help-ticket-email-count', () => {
            this.fetchTicketEmailCount();
          });
        }
      });
    },
    fetchTicketEmailCount() {
      const url = '/ticket_email_counts.json';
      http
          .get(url)
          .then((res) => {
            this.incomingCount = res.data.count;
          });
    },

    topThreeUsers() {
      http.get("/module_permissions/top_three_module_users.json?mod=HelpTicket").then((res) => {
        this.setTopUsers(res.data.users);
        this.showStaff = true;
        this.$refs.staffInModule.open();
      }).catch(() => {
        this.emitError(`Sorry, there was an error fething module users.`);
      });
    },
    goToTicketPermissions() {
      if (this.topUsers === undefined || this.topUsers.length === 0) {
        this.topThreeUsers();
      }
    },
    setTopUsersToZero() {
      this.setTopUsers([]);
    },
  },
};
</script>

<style lang="scss" scoped>
.sub-tabs-overflow {
  @media (max-width: 1145px) {
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 10px;
  }

  &::-webkit-scrollbar {
    transition: opacity 0s linear;
    width: 0.25rem;
    height: 0.5rem;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: $themed-very-muted;
    border-radius: 0.25rem;
  }
  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}
</style>
