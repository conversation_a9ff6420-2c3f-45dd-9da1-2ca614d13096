<template>
  <Teleport to="body">
    <sweet-modal
      ref="scheduledTaskFormModal"
      v-sweet-esc
      blocking
      :title="scheduledTaskTitle"
      modal-theme="right"
      class="scheduled-task-modal"
      :data-tc-title="scheduledTaskTitle"
      @close="handleCancel"
    >
      <template
        v-if="scheduledTask"
        slot="default"
      >
        <div
          v-if="!showToggleNavigations && !notifyRecipient"
          class="text-left mb-4"
        >
          <div 
            class="subpage-menu"
          >
            <div
              v-for="option in options"
              :key="option.title"
              :class="['clickable subpage-menu__item', isActiveClass(option.title)]"
              :data-tc-task-submenu="option.title"
              @click="setViewPage(option.title)"
            >
              {{ option.title }}
            </div>
          </div>
        </div>
        <div
          v-if="isTaskDetailSection"
        >
          <div 
            v-if="!isScheduledComment" 
            class="form-group"
          >
            <label 
              data-tc-label="task name"
              class="task-label"
            >
              Scheduled Task Name
              <span class="required"/>
            </label>
            <input
              id="name"
              v-model="scheduledTask.name"
              type="text"
              class="form-item form-control"
              name="name"
              :disabled="!isWrite"
              data-tc-field="task name"
            >
            <span
              v-if="nameError"
              class="form-text small text-danger"
            >
              {{ nameError }}
            </span>
          </div>
          <div 
            v-if="!isScheduledComment"
            class="form-group" 
          >
            <label 
              data-tc-label="task description"
              class="task-label"
            >
              Task Description
              <span class="required"/>
            </label>
            <textarea
              id="description"
              v-model="scheduledTask.description"
              class="form-item form-control"
              rows="4"
              name="description"
              :disabled="!isWrite"
              data-tc-field="task description"
            />
            <span
              v-if="descriptionError"
              class="small text-danger"
            >
              {{ descriptionError }}</span>
          </div>
          <div 
            v-if="!isScheduledComment"
            class="form-group"
          >
            <label 
              data-tc-label="task assignee"
              class="task-label"
            >
              Assign to
              <span class="required"/>
            </label>
            <contributors-select
              class="users-select d-inline-block"
              name="assignee"
              placeholder="Select Assignee"
              :value="scheduledTask.assignee || taskAssignee"
              :disabled="!isWrite"
              @select="onSelectRecipient"
              @remove="onRemoveRecipient"
            />
            <span
              v-if="assigneeError"
              class="text-danger smallest"
            >
              {{ assigneeError }}
            </span>
          </div>
          <div
            v-if="!isScheduledComment"
            class="text-right h6"
            data-tc-section="recurring"
          >
            <span
              class="font-weight-normal mt-2 mr-1"
              data-tc-label="recurring"
            >
              Recurring
            </span>
            <material-toggle
              ref="recurringToggle"
              :init-active="scheduledTask.recurring"
              :disabled="!isWrite"
              @toggle-sample="updateToggle"
            />
          </div>
          <div class="form-group">
            <label 
              data-tc-label="task time"
              :class="{'notification-label' : isScheduledComment}"
              class="mb-2 task-label"
            >
              {{ isScheduledComment ? 'Schedule Time' : 'Task Time' }}
            </label>
            <div
              class="row d-flex justify-content-center"
              data-tc-row="start time"
            >
              <label
                class="col-auto font-weight-normal mt-2 start-end-label"
                data-tc-label="task start time"
              >
                Start:
                <span
                  v-if="!isTaskRecurring"
                  class="required"
                />
              </label>
              <div
                v-if="!isTaskRecurring"
                class="col field-adjust p-0"
                data-tc-column="start time"
              >
                <nice-datepicker
                  v-model="scheduledTask.taskStartedAt.date"
                  :set-format="format"
                  class="date-popup"
                  :class="{ 'border-red': startDateError }"
                  name="taskStartedAt"
                  :disabled="!isWrite"
                  @input="setNonRecurrenceDate($event, 'taskStartedAt')"
                />
                <span
                  v-if="startDateError"
                  class="text-danger smallest"
                >
                  {{ startDateError }}
                </span>
              </div>
              <div
                class="inner-addon manual-timepicker-holder col"
                :class="{ 'field-adjust pr-0': !isTaskRecurring }"
              >
                <template v-if="isScheduledComment">
                  <custom-time-picker v-model="scheduledTask.taskStartedAt.startTime" />
                </template>
                <template v-else>
                  <i class="text-muted genuicon genuicon-android-time time-icon pt-2" />
                  <vue-timepicker
                    v-model="scheduledTask.taskStartedAt.startTime"
                    lazy
                    hide-clear-button
                    class="timepicker-popup"
                    manual-input
                    input-class="manual-timepicker-input"
                    input-width="100%"
                    placeholder="--:--"
                    :disabled="!isWrite"
                    data-tc-input="start hour mintues"
                  />
                </template>
                <span
                  v-if="startTimeError"
                  class="form-text small text-danger"
                >
                  {{ startTimeError }}
                </span>
              </div>
              <div class="col">
                <select
                  id="timezone"
                  v-model="startedAtTimezone"
                  v-tooltip="{
                    content: selectedStartTimezone,
                    show: hoveredIndex === 1 && showStartedAtTooltip,
                    trigger: 'manual'
                  }"
                  class="form-control"
                  :class="{ 'truncate pr-4': !isTaskRecurring }"
                  name="timezone"
                  :disabled="!isWrite"
                  @mouseover="hoveredIndex = 1"
                  @mouseleave="hoveredIndex = null"
                  @change="updateStartedAtTimezone"
                >
                  <option
                    v-for="tz in timezoneNames()"
                    :key="tz"
                    :value="tz"
                  >
                    {{ timezoneMapping[tz] }}
                  </option>
                </select>
              </div>
            </div>
            <div
              v-if="!isScheduledComment"
              class="row d-flex justify-content-center mt-3"
              data-tc-row="end time"
            >
              <label
                class="col-auto font-weight-normal mt-2 start-end-label"
                data-tc-label="task end time"
              >
                End:
                <span
                  v-if="!isTaskRecurring"
                  class="required"
                />
              </label>
              <div
                v-if="!isTaskRecurring"
                class="col field-adjust p-0"
                data-tc-column="end time"
              >
                <nice-datepicker
                  v-model="scheduledTask.taskEndedAt.date"
                  :set-format="format"
                  class="date-popup"
                  :class="{ 'border-red': endDateError }"
                  name="taskEndedAt"
                  :disabled="!isWrite"
                  @input="setNonRecurrenceDate($event, 'taskEndedAt')"
                />
                <span
                  v-if="endDateError"
                  class="text-danger smallest"
                >
                  {{ endDateError }}
                </span>
              </div>
              <div 
                class="inner-addon manual-timepicker-holder col"
                :class="{ 'field-adjust pr-0': !isTaskRecurring }"
              >
                <i class="text-muted genuicon genuicon-android-time time-icon pt-2" />
                <vue-timepicker
                  v-model="scheduledTask.taskEndedAt.endTime"
                  lazy
                  hide-clear-button
                  manual-input
                  input-class="manual-timepicker-input"
                  class="timepicker-popup"
                  input-width="100%"
                  placeholder="--:--"
                  :disabled="!isWrite"
                  data-tc-input="end hours mintues"
                />
                <span
                  v-if="endTimeError"
                  class="form-text small text-danger"
                >
                  {{ endTimeError }}
                </span>
              </div>
              <div class="col">
                <select
                  id="timezone"
                  v-model="endedAtTimezone"
                  v-tooltip="{
                    content: selectedEndTimezone,
                    show: hoveredIndex === 2 && showEndedAtTooltip,
                    trigger: 'manual'
                  }"
                  class="form-control"
                  :class="{ 'truncate pr-4': !isTaskRecurring }"
                  name="timezone"
                  :disabled="!isWrite"
                  @mouseover="hoveredIndex = 2"
                  @mouseleave="hoveredIndex = null"
                  @change="updateEndedAtTimezone"
                >
                  <option
                    v-for="tz in timezoneNames()"
                    :key="tz"
                    :value="tz"
                  >
                    {{ timezoneMapping[tz] }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div
            v-if="isTaskRecurring"
            ref="recurringSection"
            class="form-group"
          >
            <label 
              data-tc-label="recurrence pattern"
              class="task-label mt-3"
            >
              Recurrence Pattern
            </label>
            <div class="column align-items-center mt-1">
              <div class="row align-items-center justify-content-between mx-1 mt-2 mb-3 border-b-300">
                <div>
                  <input
                    id="daily"
                    v-model="selectedPattern"
                    class="mr-1"
                    type="radio"
                    value="daily"
                    :disabled="!isWrite"
                    data-tc-radio="daily"
                  >
                  <label
                    for="daily"
                    class="font-weight-normal"
                    data-tc-label="daily"
                  >Daily</label>
                </div>
                <div class="mt-1">
                  <input
                    id="weekly"
                    v-model="selectedPattern"
                    class="mr-1"
                    type="radio"
                    value="weekly"
                    :disabled="!isWrite"
                    data-tc-radio="weekly"
                  >
                  <label
                    for="weekly"
                    class="font-weight-normal"
                    data-tc-label="weekly"
                  >Weekly</label>
                </div>
                <div class="mt-1">
                  <input
                    id="monthly"
                    v-model="selectedPattern"
                    class="mr-1"
                    type="radio"
                    value="monthly"
                    :disabled="!isWrite"
                    data-tc-radio="monthly"
                  >
                  <label 
                    for="monthly"
                    class="font-weight-normal"
                    data-tc-label="monthly"
                  >Monthly</label>
                </div>
                <div class="mt-1">
                  <input
                    id="yearly"
                    v-model="selectedPattern"
                    class="mr-1"
                    type="radio"
                    value="yearly"
                    :disabled="!isWrite"
                    data-tc-radio="yearly"
                  >
                  <label
                    for="yearly"
                    class="font-weight-normal"
                    data-tc-label="yearly"
                  >Yearly</label>
                </div>
              </div>
              <div class="col px-1">
                <div v-show="selectedPattern === 'daily'">
                  <div class="d-flex align-items-center">
                    <input
                      id="revision"
                      v-model="scheduledTask.recurrence.recurrencePattern.daily.recurrenceType"
                      class="mr-2"
                      type="radio"
                      value="revision"
                      :disabled="!isWrite"
                    >
                    <label
                      for="revision"
                      class="mb-0 mr-2 font-weight-normal"
                    >
                      Every
                    </label>
                    <div>
                      <input
                        id="revisionCount"
                        v-model="scheduledTask.recurrence.recurrencePattern.daily.revisionCount"
                        type="number"
                        :min="1"
                        :step="1"
                        class="d-inline-block form-control number-input-width"
                        :class="{ 'border-red': dailyRevisionError  && !isDayRevisionDisable }"
                        name="daily[revision]"
                        :disabled="!isWrite || isDayRevisionDisable"
                        data-tc-input="every"
                      >
                      <i
                        v-if="dailyRevisionError && !isDayRevisionDisable" 
                        v-tooltip="`${dailyRevisionError}`"
                        class="nulodgicon-alert sub-menu-badge"
                      />
                    </div>
                    <label 
                      for="revision" 
                      class="mb-0 ml-2 font-weight-normal"
                    >
                      day(s)
                    </label>
                  </div>
                  <div class="d-flex align-items-center my-2">
                    <input
                      id="everyWeekday"
                      v-model="scheduledTask.recurrence.recurrencePattern.daily.recurrenceType"
                      class="mr-2"
                      type="radio"
                      value="everyWeekday"
                      :disabled="!isWrite"
                    >
                    <label 
                      for="everyWeekday" 
                      class="font-weight-normal mb-0 mt-1"
                    >
                      Every weekday
                    </label>
                  </div>
                </div>
                <div v-show="selectedPattern === 'weekly'">
                  <div>
                    <div class="d-flex flex-wrap align-items-center mt-n1">
                      <label class="font-weight-normal my-1 mr-2">
                        Every
                      </label>
                      <div class="d-flex align-items-center my-1">
                        <div class="mr-2">
                          <input
                            id="weeklyRevision"
                            v-model="scheduledTask.recurrence.recurrencePattern.weekly.revisionCount"
                            type="number"
                            :min="1"
                            :step="1"
                            :class="{ 'border-red': weeklyRevisionError }"
                            name="weekly[revision]"
                            class="d-inline-block form-control number-input-width"
                            :disabled="!isWrite"
                          >
                          <i
                            v-if="weeklyRevisionError"
                            v-tooltip="`${weeklyRevisionError}`"
                            class="nulodgicon-alert sub-menu-badge"
                          />
                        </div>
                        <label class="mb-0 font-weight-normal">
                          week(s) on: 
                        </label>
                      </div>
                    </div>
                    <div>
                      <div class="d-flex flex-wrap mt-3">
                        <div 
                          v-for="(day, index) in days" 
                          :key="index" 
                          class="d-inline-flex align-items-center font-weight-normal mr-4 mb-2"
                        >
                          <input
                            :id="day"
                            class="checkbox mr-2 clickable" 
                            type="checkbox"
                            :checked="isSelected(day)"
                            :value="scheduledTask.recurrence.recurrencePattern.weekly.selectedDays"
                            :disabled="!isWrite"
                            @change="updateSelectedDays(day)"
                          >
                          <label 
                            class="font-weight-normal mb-0"
                            :for="day"
                          >{{ day }}</label>
                        </div>
                      </div>
                      <span
                        v-if="weekSelectError"
                        class="text-danger smallest"
                      >
                        {{ weekSelectError }}
                      </span>
                    </div>
                  </div>
                </div>
                <div v-show="selectedPattern === 'monthly'">
                  <div class="d-flex align-items-baseline">
                    <input
                      id="monthDay"
                      v-model="scheduledTask.recurrence.recurrencePattern.monthly.recurrenceType"
                      class="mr-2"
                      type="radio"
                      value="monthDay"
                      :disabled="!isWrite"
                    >
                    <div class="d-flex flex-wrap align-items-center mt-n1">
                      <div class="d-flex align-items-center mr-2 my-1">
                        <label 
                          for="monthDay" 
                          class="mb-0 mr-2 font-weight-normal"
                        >
                          Day 
                        </label>
                        <div>
                          <input
                            id="day"
                            v-model="scheduledTask.recurrence.recurrencePattern.monthly.monthDay"
                            type="number"
                            :min="1"
                            :step="1"
                            :class="{ 'border-red': monthlyDayError }"
                            class="d-inline-block form-control number-input-width"
                            name="monthly[day]"
                            :disabled="!isWrite || isMonthyDayDisable"
                          >
                          <i
                            v-if="monthlyDayError" 
                            v-tooltip="`${monthlyDayError}`"
                            class="nulodgicon-alert sub-menu-badge"
                          />
                        </div>
                      </div>
                      <label class="font-weight-normal mr-2 my-1">
                        of every 
                      </label>
                      <div class="d-flex align-items-center my-1">
                        <div class="mr-2">
                          <input
                            id="revisionMonth"
                            v-model="scheduledTask.recurrence.recurrencePattern.monthly.dayMonthRevision"
                            type="number"
                            :min="1"
                            :step="1"
                            :class="{ 'border-red': monthlyDayRevisionError && !isMonthyDayDisable }"
                            class="d-inline-block form-control number-input-width"
                            name="monthly[dayRevision]"
                            :disabled="!isWrite || isMonthyDayDisable"
                          >
                          <i
                            v-if="monthlyDayRevisionError && !isMonthyDayDisable" 
                            v-tooltip="`${monthlyDayRevisionError}`"
                            class="nulodgicon-alert sub-menu-badge"
                          />
                        </div>
                        <label class="mb-0 font-weight-normal">
                          month(s)
                        </label>
                      </div>
                    </div>
                  </div>
                  <div class="d-flex align-items-baseline mt-3">
                    <input
                      id="monthWeekDay"
                      v-model="scheduledTask.recurrence.recurrencePattern.monthly.recurrenceType"
                      class="mr-2"
                      type="radio"
                      value="monthWeekDay"
                      :disabled="!isWrite"
                    >
                    <div class="d-flex flex-wrap align-items-center mt-n1">
                      <label 
                        for="monthWeekDay" 
                        class="mr-2 my-1 font-weight-normal"
                      >
                        The
                      </label>
                      <div class="mr-2 my-1">
                        <select
                          id="selectedWeek"
                          v-model="scheduledTask.recurrence.recurrencePattern.monthly.selectedWeek"
                          class="form-control select-width"
                          :disabled="!isWrite || isMonthyWeekDisable"
                        >
                          <option
                            v-for="week in totalWeeks"
                            :key="week"
                            :value="week"
                          >
                            {{ week }}
                          </option>
                        </select>
                      </div>
                      <div class="mr-2 my-1">
                        <select
                          id="weekDayName"
                          v-model="scheduledTask.recurrence.recurrencePattern.monthly.weekDayName"
                          class="form-control select-width"
                          :disabled="!isWrite || isMonthyWeekDisable"
                        >
                          <option
                            v-for="day in daysNames"
                            :key="day"
                            :value="day"
                          >
                            {{ day }}
                          </option>
                        </select>
                      </div>
                      <label class="mr-2 my-1 font-weight-normal">
                        of every
                      </label>
                      <div class="d-flex align-items-center my-1">
                        <div class="mr-2">
                          <input
                            id="revisionMonth"
                            v-model="scheduledTask.recurrence.recurrencePattern.monthly.weekMonthRevision"
                            type="number"
                            :min="1"
                            :step="1"
                            :class="{ 'border-red': monthlyWeekRevisionError && !isMonthyWeekDisable }"
                            class="d-inline-block form-control number-input-width"
                            name="monthly[weekRevision]"
                            :disabled="!isWrite || isMonthyWeekDisable"
                          >
                          <i
                            v-if="monthlyWeekRevisionError && !isMonthyWeekDisable" 
                            v-tooltip="`${monthlyWeekRevisionError}`"
                            class="nulodgicon-alert sub-menu-badge"
                          />
                        </div>
                        <label class="mb-0 font-weight-normal">
                          month(s)
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-show="selectedPattern === 'yearly'">
                  <div class="d-flex flex-wrap align-items-center mt-n1">
                    <label class="mr-2 my-1 font-weight-normal">
                      Recur every 
                    </label>
                    <div class="d-flex align-items-center my-1">
                      <div class="mr-2">
                        <input
                          id="yearlyRevision"
                          v-model="scheduledTask.recurrence.recurrencePattern.yearly.revisionCount"
                          type="number"
                          :min="1"
                          :step="1"
                          :class="{ 'border-red': yearlyRevisionError }"
                          name="yearly[revision]"
                          class="d-inline-block form-control number-input-width"
                          :disabled="!isWrite"
                        >
                        <i
                          v-if="yearlyRevisionError" 
                          v-tooltip="`${yearlyRevisionError}`"
                          class="nulodgicon-alert sub-menu-badge"
                        />
                      </div>
                      <label class="font-weight-normal mb-0">
                        year(s): 
                      </label>
                    </div>
                  </div>
                  <div class="d-flex align-items-baseline mt-3">
                    <input
                      id="monthDayOption"
                      v-model="scheduledTask.recurrence.recurrencePattern.yearly.recurrenceType"
                      class="mr-2"
                      type="radio"
                      value="monthDayOption"
                      :disabled="!isWrite"
                    >
                    <div class="d-flex flex-wrap align-items-center mt-n1">
                      <label 
                        for="monthDayOption"
                        class="my-1 mr-2 font-weight-normal"
                      >
                        On 
                      </label>
                      <select
                        id="yearMonthName"
                        v-model="scheduledTask.recurrence.recurrencePattern.yearly.dayMonthName"
                        class="d-inline-block form-control select-width mr-2 my-1"
                        :disabled="!isWrite || isMonthDayOptionDisable"
                        @change="selectedMonth"
                      >
                        <option
                          v-for="month in monthNames"
                          :key="month"
                          :value="month"
                        >
                          {{ month }}
                        </option>
                      </select>
                      <div class="my-1">
                        <input
                          id="yearMonthDay"
                          v-model="scheduledTask.recurrence.recurrencePattern.yearly.monthDay"
                          type="number"
                          :min="1"
                          :step="1"
                          :class="{ 'border-red': yearlyDayError && !isMonthDayOptionDisable }"
                          class="d-inline-block form-control number-input-width"
                          name="yearly[monthDay]"
                          :disabled="!isWrite || isMonthDayOptionDisable"
                        >
                        <i
                          v-if="yearlyDayError && !isMonthDayOptionDisable"
                          v-tooltip="`${yearlyDayError}`"
                          class="nulodgicon-alert sub-menu-badge"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="d-flex align-items-baseline mt-3">
                    <input
                      id="monthWeekOption"
                      v-model="scheduledTask.recurrence.recurrencePattern.yearly.recurrenceType"
                      class="mr-2"
                      type="radio"
                      value="monthWeekOption"
                      :disabled="!isWrite"
                    >
                    <div class="d-flex flex-wrap align-items-center mt-n1">
                      <label 
                        for="monthWeekOption" 
                        class="my-1 mr-2 font-weight-normal"
                      >
                        On the
                      </label>
                      <select
                        id="yealyWeekDay"
                        v-model="scheduledTask.recurrence.recurrencePattern.yearly.weekDay"
                        class="d-inline-block form-control select-width my-1 mr-2"
                        :disabled="!isWrite || isMonthWeekOptionDisable"
                      >
                        <option
                          v-for="week in totalWeeks"
                          :key="week"
                          :value="week"
                        >
                          {{ week }}
                        </option>
                      </select>
                      <select
                        id="yealyMonthDay"
                        v-model="scheduledTask.recurrence.recurrencePattern.yearly.dayName"
                        class="d-inline-block form-control select-width my-1 mr-2"
                        :disabled="!isWrite || isMonthWeekOptionDisable"
                      >
                        <option
                          v-for="day in daysNames"
                          :key="day"
                          :value="day"
                        >
                          {{ day }}
                        </option>
                      </select>
                      <label
                        class="my-1 mr-2 font-weight-normal"
                      >
                        of
                      </label>
                      <select
                        id="yealyMonthName"
                        v-model="scheduledTask.recurrence.recurrencePattern.yearly.weekMonthName"
                        class="d-inline-block form-control select-width my-1"
                        :disabled="!isWrite || isMonthWeekOptionDisable"
                      >
                        <option
                          v-for="month in monthNames"
                          :key="month"
                          :value="month"
                        >
                          {{ month }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div 
            v-if="isTaskRecurring"
            class="form-group"
          >
            <label class="task-label mt-2">
              Range of recurrence
            </label>
            <div class="row mt-2">
              <div class="col-auto">
                <div class="row"> 
                  <label class="col-auto font-weight-normal mt-2 pr-0">
                    Start:
                    <span class="required" />
                  </label>
                  <div class="col">
                    <nice-datepicker
                      v-model="scheduledTask.recurrence.recurrenceDate.startDate"
                      class="date-popup date-popup--recurrence"
                      name="recurrenceStartDate"
                      :set-format="format"
                      :class="{ 'border-red': recurrenceStartDateError }"
                      :disabled="!isWrite"
                      @input="setRecurrenceDate($event, 'startDate')"
                    />
                    <span
                      v-if="recurrenceStartDateError"
                      class="text-danger smallest"
                    >
                      {{ recurrenceStartDateError }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="col-auto">
                <div class="row mb-2">
                  <div class="col-auto recurrence-end-label mt-2 pr-0">
                    <input
                      id="endByDate"
                      v-model="scheduledTask.recurrence.recurrenceDate.endDateType"
                      class="mr-1"
                      type="radio"
                      value="endByDate"
                      :disabled="!isWrite"
                    >
                    <label 
                      class="font-weight-normal" 
                      for="endByDate"
                    >
                      End By:
                    </label>
                  </div>
                  <div class="col">
                    <nice-datepicker
                      v-model="scheduledTask.recurrence.recurrenceDate.endDate"
                      class="date-popup--recurrence"
                      name="recurrenceEndDate"
                      :set-format="format"
                      :class="{ 'border-red': recurrenceEndDateError && !isEndByDateDisable }"
                      :disabled="!isWrite || isEndByDateDisable"
                      @input="setRecurrenceDate($event, 'endDate')"
                    />
                    <span
                      v-if="recurrenceEndDateError && !isEndByDateDisable"
                      class="text-danger smallest"
                    >
                      {{ recurrenceEndDateError }}
                    </span>
                  </div>
                </div>
                <div class="row mb-2">
                  <div class="col-auto recurrence-end-label mt-2 pr-0">
                    <input
                      id="endAfterOccurrences"
                      v-model="scheduledTask.recurrence.recurrenceDate.endDateType"
                      class="mr-1"
                      type="radio"
                      value="endAfterOccurrences"
                      :disabled="!isWrite"
                    >
                    <label 
                      class="font-weight-normal" 
                      for="endAfterOccurrences"
                    >
                      End After:
                    </label>
                  </div>
                  <div class="col">
                    <span>
                      <input
                        id="endOccurrences"
                        v-model="scheduledTask.recurrence.recurrenceDate.occurances"
                        type="number"
                        name="endOccurrences"
                        class="d-inline-block form-control occurrences-width"
                        :min="1"
                        :step="1"
                        :class="{ 'border-red': recurrenceOccurancesError && !isOccurrencesDisable }"
                        :disabled="!isWrite || isOccurrencesDisable"
                      >
                      <i
                        v-if="recurrenceOccurancesError && !isOccurrencesDisable" 
                        v-tooltip="`${recurrenceOccurancesError}`"
                        class="nulodgicon-alert sub-menu-badge"
                      />
                    </span>
                    <span>
                      occurrences
                    </span>
                  </div>
                </div>
                <div>
                  <input
                    id="no_recurrence_end"
                    v-model="scheduledTask.recurrence.recurrenceDate.endDateType"
                    class="mr-1"
                    type="radio"
                    value="noEndDate"
                    :disabled="!isWrite"
                  >
                  <label 
                    class="font-weight-normal" 
                    for="no_recurrence_end"
                  >
                    No End Date:
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div 
          v-if="isTaskDetailSection && isScheduledComment" 
          class="col-sm-12 p-0 mt-5"
        >
          <p class="mb-3 font-weight-semibold text-gray-800 heading-text-size">
            Either add your comment to the ticket or send a reminder at the scheduled time
          </p>
          <div class="mb-1">
            <div class="flex items-center">
              <label 
                class="flex items-center radio-btn has-tooltip" 
                @click="commentOption = 'creator'"
              >
                <input
                  v-model="commentOption"
                  type="radio"
                  class="mr-2"
                  value="creator"
                >
              </label>
              <span class="font-weight-normal">Add your comment to the Ticket at the scheduled time</span>
            </div>
            <div 
              v-if="commentOption === 'creator'" 
              class="flex items-center ml-5"
            >
              <label 
                class="clickable flex items-center" 
                @click.stop.prevent="notifyOnComment = !notifyOnComment"
              >
                <input
                  type="checkbox"
                  class="hidden"
                  :checked="notifyOnComment"
                >
                <i class="nulodgicon-checkmark checkbox mr-2" />
              </label>
              <span class="font-weight-normal">Notify me when the comment is added</span>
            </div>
          </div>
          <div class="flex items-center mt-2">
            <label 
              class="flex items-center radio-btn" 
              @click="commentOption = 'reminder'"
            >
              <input
                v-model="commentOption"
                type="radio"
                class="mr-2"
                value="reminder"
              >
            </label>
            <span class="font-weight-normal">Remind me to click add comment at the scheduled time</span>
          </div>
        </div>
        <div
          v-if="isTaskDetailSection && isScheduledComment && notifyRecipient"
          class="mt-4"
        >
          <label
            class="notification-label"
          >
            Who will be reminded at the Scheduled Time <span class="text-danger">*</span>
          </label>
          <div class="mt-2">
            <people-list
              :object="currentHelpTicket"
              :is-quick-view="false"
              :active-dropdown="activeDropdown"
              :is-click-allowed="isClickAllowed"
              :is-help-ticket-show="true"
              :current-recipient="currentNotificationRecipient"
              is-scheduled-comment
              :selected-recipients-list="notificationRecipients"
              @notification-recipients="setNotificationRecipients"
            />
          </div>
          <span v-if="showRecipientsError">
            <small
              class="subject-error-message" 
              :class="{ visible: showRecipientsError }"
            >
              Recipient(s) must be selected in order set a reminder
            </small>
          </span>
          <div class="mt-4">
            <label class="notification-label">
              Reminder Subject: <span class="text-danger">*</span>
            </label>
            <div 
              class="subject-wrapper" 
              :class="{ 'has-error': subjectError }"
            >
              <input
                v-model.trim="notificationSubject"
                type="text"
                class="form-control mt-1 subject-input"
                :disabled="!isWrite"
              >
            </div>
            <small 
              class="subject-error-message" 
              :class="{ visible: subjectError }"
            >
              Reminder subject is required and should not be empty
            </small>
          </div>
        </div>
        <div
          v-if="!isTaskDetailSection"
          :class="{ 'mt-4' : isScheduledComment }"
        >
          <task-notifications
            v-if="!isTaskDetailSection"
            :selected-scheduled-task="scheduledTask"
            :is-scheduled-comment="isScheduledComment"
            :notification-recipients="notificationRecipients"
            @update-notifications="updateNotifications"
          />
        </div>
        <div 
          v-else-if="isTaskDetailSection && isScheduledComment && notifyRecipient"
          class="mt-2"
        >
          <label
            v-if="isScheduledComment"
            class="notification-label"
          >
            How will the reminder get sent? <span class="text-danger">*</span>
          </label>
          <task-notifications
            v-if="isTaskDetailSection"
            :selected-scheduled-task="scheduledTask"
            :is-scheduled-comment="isScheduledComment"
            :notification-recipients="notificationRecipients"
            @update-notifications="updateNotifications"
          />
        </div>
      </template>
      <div slot="button">
        <button
          class="btn btn-link text-secondary mr-2"
          @click.stop="handleCancel"
        >
          Cancel
        </button>
        <button
          v-if="isWrite"
          :is-saving="isSaving"
          class="btn submit-button font-weight-semi-bold"
          saving-content="Saving"
          :disabled="isSaveDisabled"
          data-tc-btn="save scheduler task"
          @click.stop.prevent="handleSaveOrNext"
        >
          {{ 'Save' }}
        </button>
      </div>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import http from 'common/http';
  import dates from 'mixins/dates';
  import { SweetModal } from 'sweet-modal-vue';
  import _cloneDeep from 'lodash/cloneDeep';
  import permissionsHelper from 'mixins/permissions_helper';
  import VueTimepicker from "vue2-timepicker";
  import MomentTimezone from 'mixins/moment-timezone';
  import NiceDatepicker from 'components/shared/nice_datepicker';
  import strings from 'mixins/string';
  import MaterialToggle from 'components/shared/material_toggle.vue';
  import ScheduledTaskData from 'mixins/scheduled_task_data';
  import ContributorsSelect from "../../shared/contributors_select.vue";
  import TaskNotifications from './task_notifications.vue';
  import CustomTimePicker from './custom_time_picker.vue';
  import PeopleList from '../../shared/custom_forms/field_renderer/people_list.vue';

  export default {
    $_veeValidate: {
      validator: "new",
    },
    components: {
      SweetModal,
      ContributorsSelect,
      MaterialToggle,
      VueTimepicker,
      PeopleList,
      NiceDatepicker,
      CustomTimePicker,
      TaskNotifications,
    },
    mixins: [dates, permissionsHelper, strings, MomentTimezone, ScheduledTaskData],
    props: {
      selectedTask: {
        type: Object,
        default: () => {},
      },
      isScheduledComment: {
        type: Boolean,
        default: false,
      },
      skipTaskDetails: {
        type: Boolean,
        default: false,
      },
      currentHelpTicket: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        format: "YYYY-MM-DD",
        selectedPattern: 'daily',
        selectedDays: [],
        days: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
        totalWeeks: ['First', 'Second', 'Third', 'Fourth', 'Last'],
        daysNames: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
        monthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
        scheduledTask: _cloneDeep(this.selectedTask),
        option: "Task Details",
        availableDays: 31,
        isSaving: false,
        weekSelectError: null,
        taskAssignee: null,
        nameError: '',
        descriptionError: '',
        assigneeError: '',
        endDateError: '',
        recurrenceEndDateError: '',
        recurrenceOccurancesError: '',
        startDateError: '',
        recurrenceStartDateError: '',
        startTimeError: '',
        endTimeError: '',
        dailyRevisionError: '',
        weeklyRevisionError: '',
        monthlyDayError: '',
        monthlyDayRevisionError: '',
        monthlyWeekRevisionError: '',
        yearlyRevisionError: '',
        yearlyDayError: '',
        initialStartDate: null,
        initialRecurrenceDate: null,
        selectedStartTimezone: null,
        selectedEndTimezone: null,
        startedAtTimezone: null,
        endedAtTimezone: null,
        hoveredIndex: null,
        sendToCreator: false,
        notifyRecipient: false,
        notificationRecipients: [],
        wentToNotificationFromNext: false,
        currentCompanyUser: null,
        notificationSubject: '',
        subjectError: false,
        commentOption: 'creator',
        notifyOnComment: false,
        sendEmailRecipient: {},
        showRecipientsError: false,
        currentNotificationRecipient: {},
      };
    },
    computed: {
      options() {
        return [
          {
            title: this.isScheduledComment ? "Comment Details" : "Task Details",
            active: false,
          },
          {
            title: "Notification",
            active: false,
          },
        ];
      },
      isSaveDisabled() {
        // Case 1: Save is disabled if already saving
        if (this.isSaving) return true;

        // Case 2: If in "Notification" tab and no notification toggle is enabled
        if (this.isScheduledComment && this.notifyRecipient) {
          const anyNotificationEnabled = this.scheduledTask?.notifications.some(n => n.enabled);
          return !anyNotificationEnabled;
        }

        // Case 3: If in "Task Details" and both checkboxes are off
        if (this.isScheduledComment && (!this.sendToCreator && !this.notifyRecipient)) {
          return true;
        }

        return false;
      },
      showToggleNavigations() {
        return !this.notifyRecipient && this.isScheduledComment;
      },
      isTaskDetailSection() {
        const expectedTitle = this.isScheduledComment ? 'Comment Details' : 'Task Details';
        return this.option === expectedTitle;
      },
      scheduledTaskTitle() {
        if (this.isScheduledComment) {
          return this.scheduledTask?.id ? 'Reschedule Comment' : 'Schedule Comment';
        }
        return this.scheduledTask?.id ? 'Edit Scheduled Task' : 'Create Scheduled Task';
      },
      isTaskRecurring() {
        return this.scheduledTask?.recurring;
      },
      isEndByDateDisable() {
        return this.scheduledTask?.recurrence.recurrenceDate.endDateType !== 'endByDate';
      },
      isMonthyWeekDisable() {
        return this.scheduledTask?.recurrence.recurrencePattern.monthly?.recurrenceType !== 'monthWeekDay';
      },
      isMonthyDayDisable() {
        return this.scheduledTask?.recurrence.recurrencePattern.monthly?.recurrenceType !== 'monthDay';
      },
      isMonthDayOptionDisable() {
        return this.scheduledTask?.recurrence.recurrencePattern.yearly?.recurrenceType !== 'monthDayOption';
      },
      isMonthWeekOptionDisable() {
        return this.scheduledTask?.recurrence.recurrencePattern.yearly?.recurrenceType !== 'monthWeekOption';
      },
      isDayRevisionDisable() {
        return this.scheduledTask?.recurrence.recurrencePattern.daily?.recurrenceType !== 'revision';
      },
      isOccurrencesDisable() {
        return this.scheduledTask?.recurrence.recurrenceDate.endDateType !== 'endAfterOccurrences';
      },
      showStartedAtTooltip() {
        return this.selectedStartTimezone && this.selectedStartTimezone.length > 22;
      },
      showEndedAtTooltip() {
        return this.selectedEndTimezone && this.selectedEndTimezone.length > 22;
      },
    },
    watch: {
      selectedTask() {
        this.scheduledTask = _cloneDeep(this.selectedTask);
        if (this.scheduledTask?.id) {
          this.selectedPattern = Object.keys(this.scheduledTask.recurrence.recurrencePattern)[0] || 'daily';
          if (!this.initialStartDate) {
            this.initialStartDate = this.scheduledTask.taskStartedAt.date;
          }
          if (!this.initialRecurrenceDate) {
            this.initialRecurrenceDate = this.scheduledTask.recurrence.recurrenceDate.startDate;
          }
        }
      },
      commentOption: {
        handler(newVal) {
          this.sendToCreator = newVal === 'creator';
          this.notifyRecipient = newVal === 'reminder';
          if (newVal === 'reminder') {
            this.notifyOnComment = false;
            const desktopNotification = this.scheduledTask.notifications?.find(n => n.notificationType === 'desktop');
            if ( this.scheduledTask?.id && this.scheduledTask?.sendToCreatorComment) {
              const emailNotification = this.scheduledTask.notifications?.find(n => n.notificationType === 'email');
              if (emailNotification) {
                emailNotification.enabled = false;
                emailNotification.value.recipients = [];
              }
              desktopNotification.enabled = true;
              desktopNotification.value.recipients = [this.sendEmailRecipient];
            } else if (!this.scheduledTask?.id && desktopNotification && this.sendEmailRecipient) {
              desktopNotification.enabled = true;
              desktopNotification.value.recipients = [this.sendEmailRecipient];
            }
          } else if (newVal === 'creator') {
            this.notifyOnComment = this.scheduledTask?.sendEmailToCreatorComment;
          }
        },
        immediate: true,
      },
      scheduledTask() {
        if (this.scheduledTask && (!this.endedAtTimezone || !this.startedAtTimezone)) {
          this.startedAtTimezone = this.scheduledTask.taskStartedAt.timeZone;
          this.endedAtTimezone = this.scheduledTask.taskEndedAt.timeZone;
          this.selectedStartTimezone = this.timezoneMapping[this.startedAtTimezone];
          this.selectedEndTimezone = this.timezoneMapping[this.endedAtTimezone];
        }
      },
      notificationSubject(newVal) {
        if (newVal && this.subjectError) {
          this.subjectError = false;
        }
      },
      isScheduledComment: {
        immediate: true,
        handler(newVal) {
          const title = newVal ? "Comment Details" : "Task Details";
          this.option = title;
        },
      },
    },
    methods: {
      handleSaveOrNext() {
        if (!this.sendToCreator && !this.notifyRecipient) return;

        if (this.notifyRecipient) {
          this.subjectError = !this.notificationSubject;
          this.showRecipientsError = !this.notificationRecipients.length;
          if (this.subjectError || this.showRecipientsError) return;
        } else {
          this.subjectError = false;
        }
        this.saveSchedulerTask();
      },
      async onWorkspaceChange() {
        await this.fetchCurrentCompanyUser();
      },
      async fetchCurrentCompanyUser(params) {
        try {
          const res = await http.get(`/current_company_user.json`, { params });
          this.currentCompanyUser = res.data;
          this.addCurrentUserToRecipients();
        } catch (error) {
          this.$store.commit('GlobalStore/setErrorMessage', `Sorry, there was an error loading the user (${error.response.data.message}).`);
        }
      },
      setScheduledCommentSubject() {
        this.notificationSubject = this.scheduledTask.commentSubject;
      },
      setScheduledCommentRecipients() {
        if (this.scheduledTask?.id && !this.scheduledTask?.sendToCreatorComment) {
          const notification = this.scheduledTask.notifications.find(n => n.value?.recipients?.length);
          this.notificationRecipients = notification ? [...notification.value.recipients] : [];
          if (this.notificationRecipients.length) {
            this.notifyRecipient = true;
          }
        }
      },
      setNotificationRecipients(recipients) {
        this.notificationRecipients = recipients;
        if (this.scheduledTask?.notifications?.length) {
          this.scheduledTask.notifications.forEach((notification) => {
            if (notification.enabled) {
              notification.value.recipients = [...this.notificationRecipients];
            }
          });
        }
        if (this.notificationRecipients.length) {
          this.showRecipientsError = false;
        }
      },
      setSendToCreatorEmail() {
        if (this.scheduledTask?.id) {
          this.notifyOnComment = this.scheduledTask.sendEmailToCreatorComment;
          if (this.scheduledTask.sendToCreatorComment) {
            this.commentOption = 'creator';
          } else {
            this.commentOption = 'reminder';
          }
        }
      },
      toggleEmailNotificationForComment(scheduledTask) {
        const emailNotification = scheduledTask.notifications.find((n) => n.notificationType === "email");
        const allNotifications = scheduledTask.notifications;
        if (!emailNotification || !allNotifications || !this.sendEmailRecipient) return;

        if (this.sendToCreator && !this.notifyOnComment) {
          allNotifications.forEach((n) => {
            if (n.enabled && n.value.recipients.length) {
              n.enabled = false;
              n.value.recipients = [];
            }
          });
          return;
        }
        if (this.sendToCreator && this.notifyOnComment) {
          allNotifications.forEach((n) => {
            if (n.notificationType === "email") {
              n.enabled = true;
              n.value.recipients = [this.sendEmailRecipient];
              this.setEmailSubjectToNotifyUser();
            } else {
              n.enabled = false;
              if (n.value?.recipients) {
                n.value.recipients = [];
              }
            }
          });
          return;
        }
      },
      setEmailSubjectToNotifyUser() {
        if (!this.currentHelpTicket || Object.keys(this.currentHelpTicket).length === 0) {
          return;
        }
        this.notificationSubject = `Rescheduled Ticket - #${this.currentHelpTicket.ticketNumber} ${this.currentHelpTicket.subject}`;
      },
      addCurrentUserToRecipients() {
        if (!this.currentCompanyUser || !this.currentCompanyUser.user) return;

        const recipient = {
          id: this.currentCompanyUser.contributorId,
          name: `${this.currentCompanyUser.user.firstName} ${this.currentCompanyUser.user.lastName}`,
          email: this.currentCompanyUser.user.email,
          rootId: this.currentCompanyUser.id,
          type: "CompanyUser",
          avatarThumbUrl: this.currentCompanyUser.avatarUrl || null,
          active: this.currentCompanyUser.activeStatus,
        };
        this.currentNotificationRecipient = { ...recipient };
        this.sendEmailRecipient = { ...recipient };
        const alreadyAdded = this.notificationRecipients.some(r => r.id === recipient.id);
        if (!alreadyAdded) {
          this.notificationRecipients.push(recipient);
        }
      },
      setSubjectValues() {
        if (!this.currentHelpTicket || Object.keys(this.currentHelpTicket).length === 0) {
          return;
        }

        if (this.scheduledTask?.commentSubject) {
          this.setScheduledCommentSubject();
        } else {
          this.notificationSubject = `Reminder to click add for Rescheduled Ticket - #${this.currentHelpTicket.ticketNumber} ${this.currentHelpTicket.subject}`;
        }
      },
      switchToNotificationTab() {
        this.setViewPage('Notification');
      },
      setNonRecurrenceDate(date, dateType) {
        this.scheduledTask[dateType].date = this.formatDate(date);
      },
      setRecurrenceDate(date, dateType) {
        this.scheduledTask.recurrence.recurrenceDate[dateType] = this.formatDate(date);
      },
      updateStartedAtTimezone(event) {
        this.startedAtTimezone = event.target.value;
        this.selectedStartTimezone = this.timezoneMapping[event.target.value];
      },
      updateEndedAtTimezone(event) {
        this.endedAtTimezone = event.target.value;
        this.selectedEndTimezone = this.timezoneMapping[event.target.value];
      },
      isSelected(day) {
        return this.scheduledTask.recurrence.recurrencePattern.weekly.selectedDays?.includes(day);
      },
      getMaxDaysInMonth(month) {
        const currentYear = new Date().getFullYear();
        const thirtyOneDaysMonths = ["January", "March", "May", "July", "August", "October", "December"];
        const thirtyDaysMonths = ["April", "June", "September", "November"];

        if (thirtyOneDaysMonths.includes(month)) {
          return 31;
        } else if (thirtyDaysMonths.includes(month)) {
          return 30;
        } else if ((currentYear % 4 === 0 && currentYear % 100 !== 0) || currentYear % 400 === 0) {
          return 29;
        }
        return 28;
      },
      selectedMonth() {
        this.availableDays = this.getMaxDaysInMonth(this.scheduledTask.recurrence.recurrencePattern.yearly.dayMonthName);
      },
      onSelectRecipient(item) {
        this.scheduledTask.assigneeId = item.id;
        this.taskAssignee = item;
      },
      onRemoveRecipient() {
        this.scheduledTask.assigneeId = null;
        this.taskAssignee = null;
      },
      updateSelectedDays(day) {
        const index = this.scheduledTask.recurrence.recurrencePattern.weekly.selectedDays.indexOf(day);
        if (index === -1) {
          this.scheduledTask.recurrence.recurrencePattern.weekly.selectedDays.push(day);
        } else {
          this.scheduledTask.recurrence.recurrencePattern.weekly.selectedDays.splice(index, 1);
        }
      },
      isActiveClass(option) {
        return { 'router-link-exact-active': this.option === option };
      },
      setViewPage(option) {
        this.option = option;

        if (option !== 'Notification') {
          this.wentToNotificationFromNext = false;
        }
      },
      updateToggle() {
        this.scheduledTask.recurring = !this.scheduledTask.recurring;
        this.endDateError = null;
        this.startDateError = null;

        this.$nextTick(() => {
          const section = this.$refs.recurringSection;
          if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
          }
        });
      },
      saveSchedulerTask() {
        if (this.validateForm()) {
          if (this.scheduledTask) {
            this.scheduledTask?.id ? this.updateScheduledTask() : this.saveScheduledTask();
          }
        } else {
          this.isSaving = false;
          this.emitError('Please correct the highlighted errors before submitting.');
        }
      },
      saveScheduledTask() {
        const newScheduledTask = _cloneDeep(this.scheduledTask);
        const { selectedPattern } = this;
        const { recurrencePattern } = newScheduledTask.recurrence;
        newScheduledTask.recurrence.recurrencePattern = { [selectedPattern]: recurrencePattern[selectedPattern] };
        newScheduledTask.taskStartedAt.timeZone = this.startedAtTimezone;
        newScheduledTask.taskEndedAt.timeZone = this.endedAtTimezone;
        const params = { scheduled_task: newScheduledTask };
        if (this.isScheduledComment) {
          const { date, startTime, timeZone } = newScheduledTask.taskStartedAt;
          if (date && startTime && timeZone) {
            const convertedStartTime = this.convertTimeFormat(startTime, 'to24');
            const localMoment = this.$moment.tz(`${date} ${convertedStartTime}`, 'YYYY-MM-DD HH:mm', timeZone);
            const utcMoment = localMoment.utc();
            newScheduledTask.taskStartedAt.date = utcMoment.format('YYYY-MM-DD');
            newScheduledTask.taskStartedAt.startTime = utcMoment.format('HH:mm');
            newScheduledTask.taskStartedAt.utc_time = utcMoment.toISOString();
          }
          params.skip_validations = true;
          if (this.sendToCreator && this.notifyOnComment) {
            this.setEmailSubjectToNotifyUser();
          }
          if (this.notificationSubject.trim() !== "") {
            params.comment_subject = this.notificationSubject.trim();
          }
          if (this.sendToCreator) {
            params.send_to_creator_comment = this.sendToCreator;
          }
          if (this.notifyOnComment) {
            params.notifyOnComment = this.notifyOnComment;
          }
          this.toggleEmailNotificationForComment(newScheduledTask);
        }
        this.isSaving = true;
        http
          .post('/scheduled_tasks.json', params)
          .then((response) => {
            const createdTaskId = response.data.scheduledTaskId;
            this.$emit("input", createdTaskId);
            this.close();
            this.isSaving = false;
            if (!this.isScheduledComment) {
              this.emitSuccess('Scheduled Task created successfully.');
            }
          })
          .catch(error => {
            this.isSaving = false;
            this.emitError(`Sorry there was an error creating the Scheduled Task. ${error?.response?.data.message}`);
          });
      },
      updateScheduledTask() {
        const updatedTask = _cloneDeep(this.scheduledTask);
        const { selectedPattern } = this;
        const { recurrencePattern } = updatedTask.recurrence;
        updatedTask.recurrence.recurrencePattern = { [selectedPattern]: recurrencePattern[selectedPattern] };
        updatedTask.taskStartedAt.timeZone = this.startedAtTimezone;
        updatedTask.taskEndedAt.timeZone = this.endedAtTimezone;
        const params = { scheduled_task: updatedTask };
        if (this.isScheduledComment) {
          const { date, startTime, timeZone } = updatedTask.taskStartedAt;
          if (date && startTime && timeZone) {
            const convertedStartTime = this.convertTimeFormat(startTime, 'to24');
            const localMoment = this.$moment.tz(`${date} ${convertedStartTime}`, 'YYYY-MM-DD HH:mm', timeZone);
            const utcMoment = localMoment.utc();
            updatedTask.taskStartedAt.date = utcMoment.format('YYYY-MM-DD');
            updatedTask.taskStartedAt.startTime = utcMoment.format('HH:mm');
            updatedTask.taskStartedAt.utcTime = utcMoment.toISOString();
          }
          params.skip_validations = true;
          if (this.sendToCreator && this.notifyOnComment) {
            this.setEmailSubjectToNotifyUser();
          }
          if (this.notifyRecipient && this.notificationSubject.trim() !== "") {
            params.comment_subject = this.notificationSubject.trim();
          }
          params.sendToCreatorComment = this.sendToCreator;
          params.notifyOnComment = this.notifyOnComment;
          this.toggleEmailNotificationForComment(updatedTask);
        }
        this.isSaving = true;
        http
          .put(`/scheduled_tasks/${updatedTask.id}.json`, params)
          .then(() => {
            if (this.isScheduledComment) {
              this.fetchScheduledComments();
            }
            this.isSaving = false;
            this.$emit("input", false);
            this.close();
            if (!this.isScheduledComment) {
              this.emitSuccess("Scheduled Task updated successfully!");
            }
          })
          .catch((error) => {
            this.isSaving = false;
            this.emitError(`Sorry there was an error updating this Scheduled Task. ${error?.response?.data.message}`);
          });
      },
      fetchScheduledComments() {
        const helpticketId = this.currentHelpTicket.id;
        const contributorId = this.$currentContributorId;
        this.$store.dispatch('fetchScheduledComments', { helpticketId, contributorId });
      },
      updateNotifications(notifications) {
        this.scheduledTask.notifications = notifications;
      },
      handleCancel() {
        this.resetData();
        this.close();
        this.$emit('cancel');
      },
      open() {
        this.$refs.scheduledTaskFormModal.open();
        this.initialStartDate = null;
        this.initialRecurrenceDate = null;
      },
      resetScheduledCommentErrors() {
        this.subjectError = false;
        this.showRecipientsError = false;
      },
      close() {
        this.startedAtTimezone = null;
        this.endedAtTimezone = null;

        if (this.$route.query.new) {
          this.$router.replace('/scheduled_tasks');
        }
        if (this.$refs.scheduledTaskFormModal.visible) {
          this.$refs.scheduledTaskFormModal.close();
        }
        if (this.isScheduledComment) {
          this.commentOption = 'creator';
          this.addCurrentUserToRecipients();
          this.setSubjectValues();
          this.resetScheduledCommentErrors();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>

  .heading-text-size {
    font-size: 1.0rem;
    font-weight: 500 !important;
  }

  .subject-wrapper.has-error .subject-input {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.15rem rgba(220, 53, 69, 0.25);
  }

  .subject-error-message {
    color: #dc3545;
    font-size: 0.75rem;
    transition: opacity 0.1s ease, max-height 0.1s ease;
    opacity: 0;
    max-height: 0;

    &.visible {
      opacity: 1;
      max-height: 1.875rem;
    }
  }

  :checked ~ .checkbox {
    background-color: $themed-link;
    border-color: $themed-link;
  }

  .submit-button {
    background: $themed-dark-drawer-bg;
    color: white;
  }

  .sub-menu-badge {
    min-width: unset;
    top: -0.8rem;
    left: -0.7rem;
  }

  .notification-label {
    color: $themed-dark;
    font-size: 1.0rem;
    font-weight: 500;
    margin-bottom: 0;
  }

  .date-popup {
    :deep(.mx-datepicker) {
      .mx-datepicker-popup {
        left: -2.125rem !important;
      }
    }
  }

  .date-popup--recurrence {
    max-width: 10rem;
  }

  .timepicker-popup {
    :deep(.dropdown) {
      z-index: 13 !important;
    }
  }

  .number-input-width {
    width: 3.6rem;
    padding: 0.5rem;
  }

  .task-label {
    font-size: 1.1rem;
  }

  .select-width {
    display: inline-block;
    padding-right: 1.625rem;
    width: auto;
  }

  .occurrences-width {
    width: 5rem;
  }

  .scheduled-task-modal {
    :deep(.sweet-title) {
      background-color: $themed-dark-drawer-bg;
      color: white;
    }

    :deep(.sweet-action-close) {
      color: white !important;
    }

    :deep(.sweet-content-content) {
      width: 100%;
    }

    :deep(.sweet-buttons) {
      bottom: 0;
      left: 0;
      position: sticky;
      width: 100%;
      z-index: 5;
      background-color: var(--themed-box-bg);
    }
  }

  .time-icon {
    z-index: 1;
  }
  .start-end-label {
    width: 5rem;
  }

  .recurrence-end-label {
    width: 6.5rem;
  }

  .field-adjust {
    flex-grow: 0.7;
  }

  @media only screen and (min-width: 768px) {
    .scheduled-task-modal {
      :deep(.sweet-modal) {
        width: 40rem !important;
      }
    }
  }

  @media only screen and (min-width: 992px) {
    .scheduled-task-modal {
      :deep(.sweet-modal) {
        width: 50rem !important;
      }
    }
  }
</style>
