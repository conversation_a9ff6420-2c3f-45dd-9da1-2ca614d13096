<template>
  <Teleport to="body">
    <sweet-modal
      ref="modal"
      title="Ticket Summary"
      modal-theme="dark-header theme-right theme-sticky-footer"
      width="50%"
      @close="$emit('close')"
    >
      <div>
        <div
          v-if="summary"
          class="mt-2 prose prose-sm max-w-none"
          v-html="renderedSummary"
        />
        <div
          v-else-if="!summary && isLoading"
          class="d-flex h6 text-muted mt-2"
        >
          Generating summary
          <pulse-loader
            class="ml-3"
            color="#0d6efd"
            size="0.5rem"
            loading
          />
        </div>
      </div>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import { marked } from "marked";
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import consumer from "common/consumer";
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    components: {
      SweetModal,
      PulseLoader,
    },
    mixins: [ permissionsHelper ],
    props: {
      ticketId: {
        type: Number,
        default: 0,
        require: true,
      },
    },
    data() {
      return {
        summary: "",
        isLoading: false,
        renderedSummary: "",
        ticketSummarySubscription: null,
      };
    },
    computed: {
      generateSummaryText() {
        return this.summary ? 'Regenerate Summary' : 'Generate Summary';
      },
    },
    methods: {
      open() {
        this.$refs.modal.open();
      },
      close() {
        this.$refs.modal.close();
      },
      requestSummary() {
        this.subscribeSummaryChannel();
        this.summary = "";
        this.isLoading = true;
        const params = { ticket_id: this.ticketId };
        http
          .get(`/help_tickets/ticket_summaries.json`, { params })
          .then(() => {})
          .catch((e) => {
            this.isLoading = false;
            this.emitError(`Sorry, there was an error while generating ticket summary. ${e.response.data.message}`);
          });
      },
      subscribeSummaryChannel() {
        const vm = this;
        if (!this.ticketSummarySubscription) {
          this.ticketSummarySubscription = consumer.subscriptions.create(
            { channel: "TicketSummaryChannel", ticket_id: vm.ticketId, company_user_id: vm.$currentCompanyUserId },
            {
              received(data) {
                if (data.status === 'done') {
                  vm.isLoading = false;
                } else if (data.status === 'error') {
                  vm.isLoading = false;
                  vm.emitError('Sorry, there was an error while generating ticket summary. Please try again a few moments later.');
                }

                if (data.message) {
                  vm.summary = data.message;
                  vm.renderedSummary = marked(vm.summary);
                }
              },
              disconnected: () => {
                vm.ticketSummarySubscription = null;
              },
            }
          );
        }
      },
    },
  };
</script>

<style lang="scss">
  .box--with-heading {
    &:hover {
      .header__arrow-wrap {
        background-color: var(--themed-light-hover-bg) !important;
        color: $themed-secondary !important;
      }
    }
  }

  .prose h3 {
    font-size: 1.4rem;
  }
  .prose strong {
    font-weight: bold;
  }

  .prose ul {
    list-style: disc !important;
    padding-left: 1.5rem !important;
  }

  .prose li {
    margin-bottom: 0.25rem;
  }

  .prose ul li::marker {
    font-size: 1.4rem;
  }
</style>
