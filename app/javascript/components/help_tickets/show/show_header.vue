<template>
  <div
    v-if="currentHelpTicket"
    :class="{ 'w-100': !isQuickView, 'dismissible-container__sticky-header preview-header': isQuickView }"
    :style="isQuickView && { width: 'calc(100% + 3rem)' }"
    style="display: table"
  >
    <div
      class="row justify-content-end m-0"
      :class="{ 'align-items-center mb-2': !isQuickView, 'align-items-start mb-3 mt-0': isQuickView }"
    >
      <div class="col-auto p-0">
        <Teleport
          to=".quick-view-container"
          :disabled="!isQuickView"
        >
          <span :class="{'align-items-center badge box-badge--filters bg-lighter text-light d-inline-flex justify-content-center ml-1 py-1 px-2 rounded not-as-small shadow-button': isQuickView}">
            <i
              v-tooltip="priorityTooltip"
              class="priority-flag"
              :class="[
                isDefaultPriority(priority) ? `genuicon-priority-${priority}` : 'genuicon-priority-generic',
                { 'position-relative ml-0': isQuickView, 'ml-0': isSplitPaneView, 'priority-flag-position': !isQuickView && editingSubject }
              ]"
              :style="{'color': priorityFlagColor}"
            />
          </span>
        </Teleport>
      </div>
      <div class="col p-0">
        <div class="d-inline">
          <div :class="{ 'mr-2': isQuickView }">
            <field-renderer
              v-if="subjectField"
              v-tooltip="{ content: subjectValue, classes: 'custom-tooltip' }"
              :form-field="subjectField"
              :value="subjectValues"
              :object="ticket"
              :show-label="false"
              :is-quick-view="isQuickView"
              class="ml-1"
              object-class="ml-1 mb-md-1"
              :class="{ 'pr-4': !isQuickView }"
              @edit="toggleSubjectEdit"
              @refresh="refreshTicketAndActivities"
            />
          </div>
        </div>
      </div>

      <div
        v-if="!isQuickView"
        class="col-auto text-muted mb-2 p-0"
      >
        <div class="d-flex align-items-center">
          <div
            v-if="!isSplitPaneView"
            class="d-inline-block position-relative align-top"
          >
            <a
              v-if="!navigatedFromParent"
              class="text-secondary mr-4"
              data-tc-icon="Back to all tickets"
              @click="redirectBack"
            >
              <i class="nulodgicon-arrow-left-c white mr-2" />
              <span class="p--responsive">Back to <strong>all tickets</strong></span>
            </a>
            <div
              v-else
              v-click-outside="closeDropdown"
              class="dropdown"
            >
              <a
                id="dropdownMenuButton"
                class="text-secondary mr-4"
                role="button"
                @click="toggleDropdown"
              >
                <i class="nulodgicon-arrow-left-c white mr-2" />
                <span class="p--responsive">Back to</span>
              </a>
              <div
                v-if="showDropdown"
                class="dropdown-menu show"
                aria-labelledby="dropdownMenuButton"
              >
                <a
                  class="dropdown-item"
                  @click="redirectBack"
                >
                  All tickets
                </a>
                <a
                  class="dropdown-item"
                  @click="redirectToParent"
                >
                  Parent company tickets
                </a>
              </div>
            </div>
          </div>
          <div
            v-if="currentHelpTicket.archived"
            class="d-inline-block"
          >
            <a
              v-if="canEditTicket"
              v-tooltip="{ content: 'Delete', classes: 'custom-tooltip' }"
              aria-label="Delete"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView }"
              role="button"
              data-tc-delete
              @click.stop.prevent="openDeleteConfirmationPopup"
            >
              <i class="nulodgicon-trash-b" />
            </a>

            <a
              v-if="canEditTicket"
              v-tooltip="{ content: 'Unarchive', classes: 'custom-tooltip' }"
              aria-label="Unarchive"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView }"
              role="button"
              data-tc-unarchive
              @click.stop.prevent="$refs.unArchiveModal.open"
            >
              <i class="nulodgicon-unarchive" />
            </a>
          </div>
          <div
            v-else
            class="d-inline-block"
          >
            <a
              v-if="isSplitPaneView"
              v-tooltip="{ content: 'Help Center Ticket', classes: 'custom-tooltip' }"
              aria-label="Help Center Ticket"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm ml-2"
              role="button"
              @click="redirectToTicketShow"
            >
              <i class="nulodgicon-external-link" />
            </a>
            <a
              v-if="canEditTicket"
              v-tooltip="{ content: 'Archive', classes: 'custom-tooltip' }"
              aria-label="Archive"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView, 'btn-icon-circle-sm': isSplitPaneView }"
              role="button"
              data-tc-archive
              @click.stop.prevent="$refs.archiveModal.open"
            >
              <i class="nulodgicon-archive" />
            </a>
            <a
              v-if="isWrite"
              v-tooltip="{ content: currentHelpTicket.muteNotification ? 'Unmute' : 'Mute', classes: 'custom-tooltip' }"
              :aria-label="currentHelpTicket.muteNotification ? 'Unmute' : 'Mute'"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView, [notificationStatus]: true, 'btn-icon-circle-sm': isSplitPaneView }"
              role="button"
              data-tc-mute
              @click.stop.prevent="$refs.notificationsModal.open"
            >
              <i :class="notificationIcon" />
            </a>
            <a
              v-if="isWrite || isScoped"
              v-tooltip="{ content: 'Print', classes: 'custom-tooltip' }"
              aria-label="Print"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView, 'btn-icon-circle-sm': isSplitPaneView }"
              role="button"
              @click.stop.prevent="printCurrentTicket"
            >
              <i class="nulodgicon-file-o" />
            </a>
            <a
              v-tooltip="{ content: 'More Actions', classes: 'custom-tooltip' }"
              v-click-outside="closeQuickViewActions"
              aria-label="More actions"
              href="#"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'quick-view-edit-btn': isQuickView, 'btn-icon-circle-sm': isSplitPaneView }"
              role="button"
              data-tc-more-actions
              @click.stop.prevent="toggleQuickViewActions"
            >
              <i class="genuicon-ellipsis-v text-secondary d-inline-block mt-0.5" />
              <i
                v-if="isModernViewNotSeen"
                :class="{'settings-notification-indicator t-0':!isSplitPaneView}"
              />
            </a>
            <span
              v-if="isSplitPaneView"
              v-tooltip="'Close Preview'"
              v-click-outside="closeQuickViewActions"
              class="btn btn-light btn-flat btn-icon-circle ml-2"
              :class="{ 'btn-icon-circle-sm': isSplitPaneView }"
              @click="$emit('close-quick-view')"
            >
              <i class="nulodgicon-android-close big" />
            </span>
          </div>
        </div>

        <div
          class="dropdown-menu split-view-action-dropdown not-as-small"
          :class="{'show mr-n3': showQuickViewActions && isSplitPaneView}"
        >
          <div v-if="currentHelpTicket.archived && isQuickView">
            <div
              v-if="canEditTicket"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="$refs.unArchiveModal.open"
            >
              <i class="nulodgicon-unarchive mr-2 small" />Unarchive ticket
            </div>
            <div
              v-if="canEditTicket"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="openDeleteConfirmationPopup"
            >
              <i class="nulodgicon-trash-b mr-2 small" />Delete ticket
            </div>
          </div>
          <div v-else-if="isQuickView">
            <div
              v-if="isWrite || isScoped"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="printCurrentTicket"
            >
              <i class="nulodgicon-file-o mr-2 small" />Print ticket
            </div>
            <div
              v-if="isWrite"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="$refs.notificationsModal.open"
            >
              <i class="nulodgicon-bell-o mr-2 small" />{{ currentHelpTicket.muteNotification ? 'Unmute ticket' : 'Mute ticket' }}
            </div>
            <div
              v-if="canEditTicket"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="$refs.archiveModal.open"
            >
              <i class="nulodgicon-archive mr-2 small" />Archive
            </div>
          </div>
          <div v-if="shouldShowSecondaryTicketActions">
            <div
              v-for="(action, index) in secondaryActionOptions"
              :key="`secondary-action-${index}`"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="action.onClick"
            >
              <i
                class="mr-2 small"
                :class="action.icon"
              />
              {{action.label}}
            </div>
          </div>
        </div>
      </div>

      <div
        v-else-if="isQuickView"
        class="mr-n2"
      >
        <span
          v-tooltip="'Help Center Ticket'"
          class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm quick-view-edit-btn text-secondary ml-2"
          @click="redirectToTicketShow"
        >
          <i class="mt-1 nulodgicon-external-link small text-muted ml-1 clickable external-link-size" />
        </span>
        <span
          v-tooltip="'More Actions'"
          v-click-outside="closeQuickViewActions"
          class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm text-secondary ml-2 d-inline-flex justify-content-center align-items-center "
          @click.stop.prevent="toggleQuickViewActions"
        >
          <i class="genuicon-ellipsis-v text-secondary pt-1" />
        </span>
        <span
          v-tooltip="'Close Preview'"
          v-click-outside="closeQuickViewActions"
          class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm text-secondary ml-2 d-inline-flex justify-content-center align-items-center "
          @click="$emit('close-quick-view')"
        >
          <i class="nulodgicon-android-close big" />
        </span>
      </div>

      <div
        id="ticket-show-page-dropdown"
        class="dropdown-menu quick-view-action-dropdown not-as-small"
        :class="{'show': showQuickViewActions && !isSplitPaneView, 'mt-2 mr-n3': !isQuickView, 'mt-7':isBasicAccess}"
      >
        <span v-if="currentHelpTicket.archived && isQuickView">
          <div
            v-if="canEditTicket"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="$refs.unArchiveModal.open"
          >
            <i class="nulodgicon-unarchive mr-2 small" />Unarchive ticket
          </div>
          <div
            v-if="canEditTicket"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="openDeleteConfirmationPopup"
          >
            <i class="nulodgicon-trash-b mr-2 small" />Delete ticket
          </div>
        </span>
        <span v-else-if="isQuickView">
          <div
            v-if="isWrite || isScoped"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="printCurrentTicket"
          >
            <i class="nulodgicon-file-o mr-2 small" />Print ticket
          </div>
          <div
            v-if="isWrite"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="$refs.notificationsModal.open"
          >
            <i class="nulodgicon-bell-o mr-2 small" />{{ currentHelpTicket.muteNotification ? 'Unmute ticket' : 'Mute ticket' }}
          </div>
          <div
            v-if="canEditTicket"
            class="text-secondary dropdown-text-menu px-3 py-2"
            @click.stop.prevent="$refs.archiveModal.open"
          >
            <i class="nulodgicon-archive mr-2 small" />Archive
          </div>
        </span>
        <div v-if="shouldShowSecondaryTicketActions">
          <div
            v-for="(action, index) in secondaryActionOptions"
            :key="`secondary-action-${index}`"
            class="text-secondary dropdown-text-menu px-3 py-2"
            :data-tc-action-btn="action.label"
            @click.stop.prevent="action.onClick"
          >
            <i
              class="mr-2 small"
              :class="action.icon"
            />{{action.label}}
          </div>
          <hr
            v-if="!isQuickView && isModernHelpTicketViewEnabled"
            class="divider"
          >
        </div>
        <modern-view-toggle
          v-if="!isQuickView && isModernHelpTicketViewEnabled"
          :is-modern-view-not-seen="isModernViewNotSeen"
          @toggle-modern-view="setModernViewSeen"
        />
      </div>
    </div>
    <div
      class="row justify-content-between align-items-center mx-0"
      :class="{'mb-1': !isQuickView, 'mr-n5 pr-0 mb-0 mt-n1': isQuickView}"
    >
      <div class="ml-1 small row mt-0">
        <span
          class="text-muted p-0"
          data-tc-ticket-number
        >
          #{{ currentHelpTicket.ticketNumber }}
        </span>
        <span class="text-fair px-1">
          •
        </span>
        <span
          class="text-muted p-0"
          @mouseover="changeFormHovering = true"
          @mouseleave="changeFormHovering = false"
        >
          {{ currentHelpTicket.customForm.name }}
          <edit-field-button
            v-if="canEditTicketForm"
            :icon-class="'nulodgicon-chevron-down'"
            :hovering="changeFormHovering"
            :align-right="false"
            @openEdit="openEdit"
          />
        </span>
        <span class="text-fair px-1">
          •
        </span>
        <span
          v-tooltip="`Created at ${createdAt}`"
          class="text-muted p-0"
          @click="openHistoryTab"
        >
          <i class="genuicon-calendar-o align-middle" />
          <span class="font-weight-semi-bold">
            <span v-if="!isQuickView">{{ createdAt }}</span>
          </span>
        </span>
        <span
          v-if="$isReseller"
          class="text-fair px-1"
        >
          •
        </span>
        <span
          v-if="$isReseller"
          class="text-muted p-0"
        >
          <i class="genuicon-company align-middle"/> 
          <span class="not-so-small text-muted">
            {{ currentHelpTicket.company.name }}
          </span>
        </span>
        <span v-if="showAppSessions">
          <app-sessions
            :app-sessions="appSessions"
            :avatar-session="avatarSession"
            :is-quick-view="isQuickView"
            :current-help-ticket="currentHelpTicket"
            @show-app-sessions="showAppSessions"
            @session="emitSession"
            @all-sessions="emitAllSessions"
          />
        </span>
      </div>
      <div
        class="d-flex"
        :class="{'mr-3': isQuickView}"
      >
        <div
          v-if="tasksTotalCount > 0"
          v-tooltip="{ content: isQuickView ? `Tasks: ${tasksCompletedCount}/${tasksTotalCount } completed`: '' }"
          :class="{
            'btn-link smallest text-muted px-2 border-radius-4 mr-2 clickable d-flex align-items-center': !isQuickView, 
            'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center': isQuickView
          }"
          data-tc-open-tasks-tab
          @click="openTasksTab"
        >
          <i
            class="genuicon-clipboard-with-check align-middle"
            :class="{ 'mr-1': !isQuickView }"
          />
          <span v-if="!isQuickView">
            <span>Tasks:</span>
            <span class="font-weight-semi-bold">
              <span>{{ tasksCompletedCount }}</span>/<span>{{ tasksTotalCount }} completed</span>
            </span>
          </span>
        </div>

        <div
          v-if="isWriteAny && timeSpentExist"
          v-tooltip="{ content: isQuickView ? `Time Spent: ${ticket.totalTimeSpent}`: '' }"
          :class="{
            'btn-link smallest text-muted px-2 border-radius-4 mr-2 clickable d-flex align-items-center': !isQuickView, 
            'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center': isQuickView
          }"
          @click="openTimeSpentTab"
        >
          <i
            class="genuicon-android-time align-middle"
            :class="{ 'mr-1 mt-0.5': !isQuickView, 'not-as-small': isQuickView }"
          />
          <span v-if="!isQuickView">
            <span>Time Spent:</span>
            <span class="font-weight-semi-bold">
              <span>{{ totalTimeSpent }}</span>
            </span>
          </span>
        </div>

        <div
          v-if="showTimer"
          class="smallest text-muted pl-2 pr-1 py-1 border-radius-4 bg-themed-lighter"
          :class="{ 'bg-themed-lighter': !isQuickView, 'bg-themed-light': isQuickView }"
        >
          <div
            class="timepicker-holder"
            :class="{'small': isQuickView}"
          >
            <vue-timepicker
              v-model="stopWatchTimer"
              lazy
              hide-clear-button
              input-class="timepicker-input border-0 small timepicker-disabled-input"
              placeholder="00:00"
              disabled
              :format="isRunning? 'HH:mm:ss' : 'HH:mm'"
              @change="calculateDifference($event)"
            />
            <img
              v-tooltip="'Reset stopwatch'"
              src="https://nulodgic-static-assets.s3.amazonaws.com/images/reset_icon.png"
              height="16"
              class="clickable ml-3"
              style="filter: var(--themed-light-icon-filter); opacity: 0.8"
              @click="resetWatch"
            >
            <img
              v-if="!isRunning"
              v-tooltip="'Start timer'"
              src="https://nulodgic-static-assets.s3.amazonaws.com/images/play_icon.png"
              height="18"
              class="clickable ml-1"
              @click="toggleRunning"
            >
            <img
              v-else
              v-tooltip="'Stop timer'"
              src="https://nulodgic-static-assets.s3.amazonaws.com/images/stop_icon.png"
              height="18"
              class="clickable ml-1"
              @click="toggleRunning"
            >
          </div>
        </div>

        <div>
          <div
            v-if="!showTimer && isWriteOrScopedPermission"
            :class="{
              'btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm text-secondary mt-n0.5': !isQuickView, 
              'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center': isQuickView
            }"
            @click="toggleTimer"
          >
            <img
              v-tooltip="'Run stopwatch to record time spent'"
              src="https://nulodgic-static-assets.s3.amazonaws.com/images/stopwatch_icon.png"
              :class="{'mb-1': !isQuickView}"
              :height="isQuickView ? 14 : 18"
              style="filter: var(--themed-light-icon-filter)"
            >
          </div>
          <div
            :class="{
              'btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm text-secondary mt-n0.5': !isQuickView, 
              'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center': isQuickView
            }"
            @click="openModal"
          >
            <i
              v-tooltip="'Resources'"
              class="genuicon-nav-resources"
              :class="{'mb-1': !isQuickView}"
            />
          </div>
          <div
            v-if="displayAiSummary"
            class="btn btn-link btn-flat btn-icon-circle text-secondary"
            :class="{
              'btn-icon-circle-sm mt-n0.5 ml-n0.5': !isQuickView, 
              'btn btn-link btn-flat btn-icon-circle btn-icon-circle-xs text-secondary ml-1 d-inline-flex justify-content-center align-items-center' : isQuickView
            }"
            @click="openAiModal"
          >
            <img
              v-tooltip="'Generate Ticket Summary via AI'"
              src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/ai_images/ai-tooling.svg"
              :class="{
                'mb-1 ml-0.5': !isQuickView ,
                'light-icon-filter': isDarkMode,
              }"
              :width="isQuickView ? 16 : 20"
            >
            <span
              v-if="!isTicketSummarySeen"
              class="notification-indicator"
              :class="{
                'indicator-position' : !isQuickView,
                'indicator-quickview-position' : isQuickView,
              }"
            />
          </div>
        </div>

        <resource-select ref="articleSelect" />
        <ticket-summary
          v-if="displayAiSummary"
          ref="ticketSummaryModal"
          :ticket-id="currentHelpTicket.id"
        />
      </div>
    </div>

    <hr
      v-if="!isQuickView"
      class="my-0"
    >

    <Teleport to="body">
      <sweet-modal
        ref="unArchiveModal"
        v-sweet-esc
        title="Before you unarchive this ticket..."
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div class="text-center">
            <h6 class="mb-3">
              Are you sure you want to unarchive this help ticket?
            </h6>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary mr-2"
          data-tc-cancel-unarchive-modal
          @click.stop="$refs.unArchiveModal.close"
        >
          No, keep it.
        </button>
        <button
          slot="button"
          data-tc-modal-unarchive-btn
          class="btn btn-primary"
          @click.stop="okUnarchive"
        >
          Yes, unarchive it.
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="notificationsModal"
        v-sweet-esc
        :title="`Before you ${notificationStatus} notifications`"
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div class="text-left">
            <h6 class="mb-3">
              Are you sure you want to {{ notificationStatus }} all notifications for this help ticket?
            </h6>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary mr-2"
          @click.stop="$refs.notificationsModal.close"
        >
          Cancel
        </button>
        <button
          slot="button"
          class="btn btn-primary text-capitalize"
          @click.stop="toggleMute"
        >
          {{ notificationStatus }}
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="archiveModal"
        v-sweet-esc
        title="Before you archive this ticket..."
        :blocking="disabled"
        :hide-close-button="disabled"
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div class="text-left">
            <p class="mb-3">
              Are you sure you want to archive this help ticket? You can unarchive it any time later.
            </p>
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary"
          :disabled="disabled"
          data-tc-cancel-archive-btn
          @click.stop="$refs.archiveModal.close"
        >
          Cancel
        </button>
        <button
          slot="button"
          class="btn btn-link text-danger"
          data-tc-modal-archive-btn
          :disabled="disabled"
          @click.stop="okArchive"
        >
          Archive
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="deleteModal"
        v-sweet-esc
        title="Before you delete this ticket..."
        data-tc-view-title="delete ticket"
        @close="verticalContainerClass"
        @open="verticalContainerClass"
      >
        <template slot="default">
          <div>
            <warning-message
              v-if="currentHelpTicket"
              ref="warningMessage"
              :entity="currentHelpTicket"
              entity-type="HelpTicket"
            />
          </div>
        </template>
        <button
          slot="button"
          class="btn btn-link text-secondary mr-2"
          data-tc-cancel-delete-btn
          @click.stop="$refs.deleteModal.close"
        >
          No, keep it.
        </button>
        <button
          slot="button"
          class="btn btn-primary"
          data-tc-modal-delete-btn
          @click.stop="okDelete"
        >
          Yes, delete it.
        </button>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <merge-ticket-modal
        ref="mergeTicketModal"
        class="ticket-modal"
      />
    </Teleport>

    <Teleport to="body">
      <move-ticket-modal
        ref="moveTicketModal"
        class="ticket-modal"
        :workspaces="workspaceOptions"
        :forms="forms"
      />
    </Teleport>
    
    <ticket-print
      v-if="printingHelpTicket && currentHelpTicket"
      ref="ticketPrint"
      :is-bulk-printing="false"
    />

    <Teleport to="body">
      <clone-ticket-modal
        ref="cloneTicketModal"
        class="ticket-modal"
        :is-quick-view="isQuickView"
        @load-ticket="refreshTicket"
      />
    </Teleport>
  </div>
</template>

<script>
  import { mapMutations, mapGetters, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import _get from 'lodash/get';
  import http from 'common/http';
  import VueMoment from 'vue-moment';
  import moment from 'moment-timezone';
  import customFormHelper from "mixins/custom_form_helper";
  import dates from 'mixins/dates';
  import customForms from 'mixins/custom_forms';
  import multiCompany from 'mixins/multi_company';
  import strings from 'mixins/string';
  import VueTimepicker from 'vue2-timepicker';
  import permissionsHelper from 'mixins/permissions_helper';
  import MomentTimezone from 'mixins/moment-timezone';
  import EditFieldButton from 'components/shared/custom_forms/edit_field_button.vue';
  import permissions from 'mixins/custom_forms/permissions';
  import helpTickets from 'mixins/help_ticket';
  import pushers from 'mixins/pushers';
  import vClickOutside from 'v-click-outside';
  import ModernViewToggle from './modern_view_toggle.vue';
  import WarningMessage from '../../shared/warning.vue';
  import AppSessions from '../../shared/app_sessions.vue';
  import FieldRenderer from '../../shared/custom_forms/renderer.vue';
  import MoveTicketModal from './move_ticket_modal.vue';
  import MergeTicketModal from './merge_ticket_modal.vue';
  import CloneTicketModal from './clone_ticket_modal.vue';
  import TicketPrint from './ticket_print.vue';
  import ResourceSelect from './resource_select.vue';
  import TicketSummary from './ticket_summary.vue';

  Vue.use(VueMoment, {
    moment,
  });

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      SweetModal,
      FieldRenderer,
      AppSessions,
      WarningMessage,
      VueTimepicker,
      MoveTicketModal,
      EditFieldButton,
      TicketPrint,
      MergeTicketModal,
      CloneTicketModal,
      ResourceSelect,
      ModernViewToggle,
      TicketSummary,
    },
    mixins: [
      dates,
      customForms,
      customFormHelper,
      strings,
      permissionsHelper,
      MomentTimezone,
      permissions,
      multiCompany,
      helpTickets,
      pushers,
    ],
    props: {
      isQuickView: {
        type: Boolean,
        default: false,
      },
      appSessions: {
        type: Array,
        default: () => [],
      },
      avatarSession: {
        type: Object,
        default: () => {},
      },
      isSplitPaneView: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        secondaryActionOptions: [
          { label: 'Merge ticket', icon: 'genuicon-merge-icon', onClick: () => {this.openMergeTicketModal();}},
          { label: 'Move ticket', icon: 'genuicon-nav-transactions', onClick: () => {this.openMoveTicketModal();}},
          { label: 'Clone ticket', icon: 'genuicon-edit-copy', onClick: () => {this.openCloneTicketModal();}},
        ],
        editingSubject: false,
        disabled: false,
        showAppSessionsSection: false,
        showTimer: false,
        isRunning: false,
        startTime: null,
        stopWatchTimer: null,
        timer: null,
        elapsedTime: 0,
        isSamePage: false,
        stopWatch: null,
        changeFormHovering: false,
        currentWorkspaceForms: [],
        printingHelpTicket: false,
        showQuickViewActions: false,
        navigatedFromParent: false,
        showDropdown: false,
        isTicketSummarySeen: false,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['workspaceOptions']),
      ...mapGetters([
        'currentHelpTicket',
        'tasksTotalCount',
        'tasksCompletedCount',
        'currentCompany',
        'isModernHelpTicketViewEnabled',
        'displayAiSummary',
      ]),
      ...mapGetters('customForms', ['customForms']),
      ticket() {
        return this.currentHelpTicket;
      },
      isModernViewNotSeen() {
        const isModernHelpTicket = localStorage.getItem('is-modern-help-ticket');
        return !this.$hasSeenModuleWalkthrough.ticket_modern_view && isModernHelpTicket === 'false';
      },
      totalTimeSpent() {
        return this.ticket?.totalTimeSpent ?? ''; 
      },
      isScopedWriteForTicket() {
        return ((this.isScopedAny || this.isBasicAccess) && this.isWritableFromCustomFormObject(this.ticket));
      },
      createdAt() {
        if (this.ticket && this.ticket.createdAt) {
          return this.timezoneDatetime(this.ticket.createdAt, Vue.prototype.$timezone);
        }
        return null;
      },
      subjectField() {
        if (this.ticket) {
          return this.getField(this.ticket, 'subject');
        }
        return null;
      },
      subjectValues() {
        if (this.ticket) {
          return this.getValuesForName(this.ticket, 'subject');
        }
        return [];
      },
      subjectValue() {
        if (this.ticket) {
          return this.getValuesForName(this.ticket, 'subject')[0].valueStr;
        }
        return null;
      },
      priority() {
        if (this.ticket) {
          const values = this.getValuesForName(this.ticket, 'priority');
          return _get(values, "[0].valueStr", '');
        }
        return null;
      },
      priorityFlagColor() {
        if (this.ticket) {
          const priorityField = this.ticket.customForm.formFields.find(field => field.fieldAttributeType === "priority");
          if (priorityField) {
            const option = priorityField.options.find(obj => obj.name.toLowerCase() === this.priority);
            if (option) {
              return option.color;
            }
          }
        }
        return null;
      },
      priorityTooltip() {
        return `${this.titleize(this.priority)} Priority`;
      },
      notificationStatus() {
        return this.currentHelpTicket.muteNotification ? "unmute" : "mute";
      },
      notificationIcon() {
        return this.currentHelpTicket.muteNotification ? "genuicon-bell-slash-o" : "nulodgicon-bell-o";
      },
      canEditTicketForm() {
        // Placeholder for the proper permissions check
        return false;
      },
      forms() {
        return this.currentWorkspaceForms;
      },
      timeSpentExist() {
        return this.ticket.totalTimeSpent !== '0 hr 0 min';
      },
      isWriteOrScopedPermission() {
        return this.isWrite || this.isSpecificPermission('scoped');
      },
      shouldShowSecondaryTicketActions() {
        return this.isWrite && (this.workspaceOptions && this.workspaceOptions.length > 0 || this.forms && this.forms.length > 1);
      },
      canEditTicket() {
        return this.isWrite || this.isScopedWriteForTicket;
      },
    },
    watch: {
      elapsedTime() {
        this.stopWatchTimer = new Date(this.elapsedTime).toISOString().substr(11, 8);
      },
    },
    methods: {
      ...mapMutations([
        'setTickets',
        'setCurrentHelpTicket',
      ]),
      ...mapActions(
        'customForms',
        ['fetchCustomForms']
      ),
      ...mapActions(['isHtModernViewEnabled']),
      redirectBack() {
        this.setTickets([]);
        this.isOnlyBasicRead ? this.$router.push('/end_user_tickets') : this.$router.push('/');
      },
      emitAllSessions(sessions) {
        this.$emit("all-sessions", sessions);
      },
      emitSession(session) {
        this.$emit("session", session);
      },
      redirectToParent() {
        const url = `/user_accesses.json?company_id=${this.$defaultCompanyId}&redirect_route=/help_tickets?workspace_id=${this.ticket.workspaceId}`;
          http.get(url)
            .then((res) => {
              window.open(res.data.url, '_blank');
            });
      },
      onWorkspaceChange() {
        this.navigatedFromParent = window?.location?.search?.includes('parent');
        this.isHtModernViewEnabled();
        this.showFirstTimeHelpdesk('show');
        this.setupWorkspacePusherListeners();
        if (!this.workspaceOptions?.length) {
          this.$store.dispatch('GlobalStore/fetchWorkspaces');
        }
        this.populateStopWatch();
        this.fetchCustomForms({
          companyModule: 'helpdesk',
          privilegeName: 'HelpTicket',
          status: 'active',
        }).then(() => {
          this.currentWorkspaceForms = this.customForms;
        });
        this.isTicketSummarySeen = this.$hasSeenModuleWalkthrough.ticket_ai_summary;
      },
      redirectToTicketShow() {
        if ((this.isQuickView || this.isSplitPaneView) && !this.currentCompany) {
          window.open(`/help_tickets/${this.currentHelpTicket.id}`, '_blank');
        } else {
          const url = `/user_accesses.json?company_id=${this.currentHelpTicket.company.id}&redirect_route=/help_tickets/${this.currentHelpTicket.id}?workspace_id=${this.currentHelpTicket.workspaceId}`;
          http.get(url)
            .then((res) => {
              window.open(res.data.url, '_blank');
            });
        }
      },
      toggleQuickViewActions() {
        this.showQuickViewActions = !this.showQuickViewActions;
      },
      closeQuickView() {
        this.$emit('close-quick-view');
      },
      closeQuickViewActions(event) {
        if (!document.getElementById('ticket-show-page-dropdown')?.contains(event.target)) {
          this.showQuickViewActions = false;
        }
      },
      resetWatch() {
        if (this.elapsedTime) {
          this.isRunning = false;
          this.startTime = null;
          this.stopWatchTimer = null;
          this.elapsedTime = 0;
          clearInterval(this.timer);
          this.isSamePage = !this.isSamePage;
          if (this.stopWatch) {
            http
              .delete(`/tickets/${this.currentHelpTicket.id}/stop_watch_timers/${this.stopWatch.id}`)
              .catch(() => {
                this.emitError(`Sorry, an error occured, try again.`);
              });
          }
        };
      },
      populateStopWatch() {
        const params = { 
          company_id: this.currentCompany, 
        };
        http.get(`/tickets/${this.ticketId}/stop_watch_timers.json`, { params }) 
          .then(res => {
            this.stopWatch = res.data;
            if (this.stopWatch && !this.isSamePage) {
              this.setTimeAttributes();
            }
          });
      },
      calculateDifference(event) {
        const hours = Number(event.data.HH);
        const minutes = Number(event.data.mm);
        const seconds = Number(event.data.ss);
        this.calculateElapsedTime(hours, minutes, seconds);
      },
      setTimeAttributes() {
        if (this.$currentCompanyUserId === this.stopWatch.companyUserId) {
          this.isSamePage = !this.isSamePage;
          this.isRunning = this.stopWatch.isStartTime;
          this.startTime = this.stopWatch.startTime;
          this.showTimer = true;
          this.endTime = moment(new Date()).format('HH:mm:ss');

          const timeDifference = moment.utc(moment(this.endTime, "HH:mm:ss").diff(moment(this.startTime, "HH:mm:ss"))).format("HH:mm:ss").split(':');

          const hours = timeDifference[0];
          const minutes = timeDifference[1];
          const seconds = timeDifference[2];

          this.calculateElapsedTime(hours, minutes, seconds);
          this.startInterval();
        }
      },
      toggleRunning() {
        this.isRunning = !this.isRunning;

        if (this.isRunning) {
          this.isSamePage = !this.isSamePage;
          this.setStartedTime();
          this.startInterval();
        } else {
          this.saveComment();
        };
      },
      startInterval() {
        this.timer = setInterval(() => {
          this.elapsedTime += 1000;
        }, 1000);
      },
      setStartedTime() {
        this.startTime = moment(new Date()).format('HH:mm:ss');
        const stopWatch = { company_user_id: this.$currentCompanyUserId, start_time: this.startTime, end_time: null, is_start_time: true };

        http
          .post(`/tickets/${this.currentHelpTicket.id}/stop_watch_timers.json`, { stopWatch, company_id: this.currentHelpTicket.company.id })
          .then(() => {
            this.populateStopWatch();
          })
          .catch(() => {
            this.emitError(`Sorry, an error occured. Try Again.`);
          });
      },
      saveComment() {
        const params = {
          help_ticket_comment:
          {
            commentBody: null, help_ticket_id: this.ticket.id, contributorId: this.$currentContributorId,
            timeSpent: {
              id: null,
              companyUserId: this.$currentCompanyUserId,
              endTime: moment(new Date()).format('HH:mm'),
              startTime: this.startTime.substr(0,5),
              timeSpent: moment.utc(moment(moment(new Date()).format('HH:mm:ss'), "HH:mm:ss").diff(moment(this.startTime, "HH:mm:ss"))).format("HH:mm:ss"),
              startedAt: new Date(),
            },
          },
          company_id: this.ticket.company.id,
        };
        http
          .post(`/ticket_comments.json`, params)
          .then(() => {
            this.resetWatch();
            this.emitSuccess(`Time spent entry added`);
            this.$store.dispatch('fetchTimeSpents', this.ticketId);
            this.$store.dispatch('fetchTicket', this.ticketId);
            this.$store.dispatch('fetchComments', this.ticketId);
          })
          .catch(() => {
            this.emitError('Sorry, there was an error adding the comment.');
          });
      },
      calculateElapsedTime(hours, minutes, seconds) {
        this.elapsedTime = (hours * 60 * 60 * 1000) + (minutes * 60 * 1000) + (seconds * 1000);
      },
      toggleTimer() {
        this.showTimer = !this.showTimer;
      },
      refreshTicketAndActivities() {
        this.refreshTicket();
        this.refreshActivities();
      },
      refreshTickets() {
        this.$store.dispatch("fetchTickets");
      },
      refreshTicket() {
        this.$store.dispatch("fetchTicket", this.ticketId);
      },
      refreshActivities() {
        this.$store.dispatch("fetchTicketActivities", this.ticketId);
      },
      toggleSubjectEdit(value) {
        this.editingSubject = value;
      },
      okDelete() {
        this.$refs.deleteModal.close();
        http
          .delete(`/tickets/${this.ticket.id}.json`)
          .then(() => {
            this.setCurrentHelpTicket(null);
            this.refreshTickets();
            this.emitSuccess("Help ticket successfully deleted");
            if (!this.isQuickView) {
              this.$router.push('/');
            } else {
              this.closeQuickView();
            }
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error deleting the help ticket (${error.response.data.message}).`);
          });
      },
      okArchive() {
        this.disabled = true;
        const params = {
          company_id: this.ticket.company.id,
        };
        http
          .post(`/tickets/${this.ticket.id}/archive.json`, params)
          .then(() => {
            this.$store.commit('setPage', 0);
            this.emitSuccess("Help ticket successfully archived");
            this.$refs.archiveModal.close();
            this.refreshTickets();
            this.disabled = true;
            if (!this.isQuickView) {
              this.isOnlyBasicRead ? this.$router.push('/end_user_tickets') : this.$router.push('/');
            } else {
              this.closeQuickView();
            }
          })
          .catch(error => {
            let errorMessage = error.message;

            if (error.response) {
              errorMessage = error.response.data.message;
            }
            this.emitError(`Sorry, there was an error archiving the help ticket (${errorMessage}).`);
            this.$refs.archiveModal.close();
            this.disabled = true;
          });
      },
      okUnarchive() {
        http
          .post(`/tickets/${this.ticket.id}/unarchive.json`)
          .then(() => {
            this.emitSuccess("Help ticket successfully unarchived");
            this.$refs.unArchiveModal.close();
            this.refreshTickets();
            if (!this.isQuickView) {
              this.isOnlyBasicRead ? this.$router.push('/end_user_tickets') : this.$router.push('/');
            } else {
              this.closeQuickView();
            }
          })
          .catch(error => {
            this.$refs.unArchiveModal.close();
            this.emitError(`Sorry, there was an error unarchiving the help ticket (${error.response.data.message}).`);
          });
      },
      showAppSessions(sessionsCount) {
        this.showAppSessionsSection = sessionsCount > 0;
      },
      openDeleteConfirmationPopup() {
        this.$refs.warningMessage.open();
        this.$refs.deleteModal.open();
      },
      openHistoryTab() {
        this.$emit('set-active-component', 'History');
      },
      openTimeSpentTab() {
        this.$emit('set-active-component', 'TimeSpent');
      },
      openTasksTab() {
        this.$emit('set-active-component', 'Tasks');
      },
      toggleMute() {
        http
          .put(`/tickets/${this.currentHelpTicket.id}/update_notification_status.json`, { muteNotification: !this.currentHelpTicket.muteNotification, company_id: this.currentHelpTicket.company.id })
          .then(() => {
            this.emitSuccess('Successfully updated mute notification status');
            this.$refs.notificationsModal.close();
            this.refreshTicket();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error updating mute status`);
          });
      },
      setModernViewSeen(isModern){
        if(!this.$hasSeenModuleWalkthrough.ticket_modern_view && isModern){
          http
            .put('/check_out_my_module_onboarding', { module_name: 'ticket_modern_view' })
            .then(()=>{
              Vue.prototype.$hasSeenModuleWalkthrough.ticket_modern_view = true;
            })
            .catch(() => {
              this.emitError('Sorry, there was an error marking modern view as seen');
            });
        }
      },
      setAISummarySeen() {
        http
          .put('/check_out_my_module_onboarding', { module_name: 'ticket_ai_summary' })
          .then(()=>{
            this.isTicketSummarySeen = true;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error marking ai summary as seen');
          });
      },
      openMoveTicketModal() {
        this.$refs.moveTicketModal.open();
      },
      openMergeTicketModal() {
        this.$refs.mergeTicketModal.open();
      },
      openCloneTicketModal() {
        this.$refs.cloneTicketModal.open();
      },
      printCurrentTicket() {
        this.printingHelpTicket = true;
        requestAnimationFrame(() => {
          this.$refs.ticketPrint.printHelpTicket();
          this.printingHelpTicket = false;
        });
      },
      toggleDropdown() {
        this.showDropdown = !this.showDropdown;
      },
      closeDropdown() {
        this.showDropdown = false;
      },
      openModal() {
        this.$refs.articleSelect.open();
      },
      openAiModal() {
        this.$refs.ticketSummaryModal.open();
        if (!this.isTicketSummarySeen) {
          this.setAISummarySeen();
        }
        this.$refs.ticketSummaryModal.requestSummary();
      },
    },
  };
</script>

<style lang="scss">

  .custom-tooltip {
    z-index: 51;
  }

</style>

<style scoped lang="scss">

  .icon {
    color: $pastel-blue-100;
  }

  .multiselect {
    :deep {
      width: 10rem;

      .multiselect__tags {
        padding-top: 0.4rem;
        padding-left: 0.75rem;
        min-height: 0.75rem;
        height: 1.875rem;
        border-radius: 0.2rem;
        margin-right: 0.5rem !important;
      }

      .multiselect__select {
        height: 1.875rem;
      }

      .multiselect__content-wrapper {
        width: 9.6rem;
      }
    }
  }

  .priority-flag {
    margin-left: -1.25rem;
  }

  .priority-flag-position {
    position: relative;
    top: -1.25rem;
  }

  .border-radius-4 {
    border-radius: 4px;
  }

  .ticket-modal {
    :deep(.sweet-modal) {
      overflow: unset;
    }
  }

  .close-cross:before {
    color: $themed-muted !important;
    font-size: 1.25rem;
  }

  .quick-view-action-dropdown {
    left: unset;
    right: 3.5rem;
    top: 2.8125rem;
  }

  .split-view-action-dropdown {
    left: unset;
    right: 1rem;
    top: 2.4rem;
  }

  .external-link-size {
    font-size: 0.938rem;
  }

  .genuicon-android-time:before {
    position: relative;
    top: 1px;
  }

  .preview-header {
    top: -1.5rem !important;
    position: sticky !important;
  }

  .nulodgicon-android-close:before {
    color: $themed-secondary;
  }

  .divider {
    border: none;
    border-top: 1px solid #c1c1c1;
    margin: 0.5rem 0;
  }

  .bot-icon-size {
    font-size: 1.3rem;
  }

  .indicator-position {
    margin-top: 3.7rem;
    margin-right: 1.6rem;
  }

  .indicator-quickview-position {
    margin-top: 3.7rem;
    margin-right: 2.25rem;
  }
</style>
