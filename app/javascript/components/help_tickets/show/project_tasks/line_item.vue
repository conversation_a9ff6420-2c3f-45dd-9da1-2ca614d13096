<template>
  <div
    class="row"
    :class="isSelectedCss"
    @click="selectTask"
  >
    <div
      v-if="!isChecklistEdit"
      class="col-sm-12 d-flex col-md-auto pt-md-1"
    >
      <span
        class="pr-2 handle genuicon-draggable"
        :data-tc-task-cursor="value.description"
      />
      <i
        v-if="value.parentProjectTaskId"
        v-tooltip="'Unlink this task'"
        class="nulodgicon-unlink text-muted clickable pr-2"
        :data-tc-task-unlink="value.description"
        @click="unlink"
      />
      <i
        v-if="!isCompleted"
        :class="taskCss"
        class="genuicon-calendar-o"
      />
      <i
        v-else
        :class="taskCss"
        class="genuicon-calendar-check-o"
      />
    </div>
    <div class="col">
      <div class="row">
        <div
          class="pl-1"
          :class="{ 'col-sm-6 col-md-9': !isQuickView }"
          :data-tc-tasks="value.description"
        >
          <div
            class="mb-1 d-flex align-items-center font-weight-semi-bold text-secondary"
            data-tc-task-description
            :data-tc-view-task-description="value.description"
          >
            {{ value.description }}
            <div
              v-if="isPredefinedTask"
              class="ml-2"
            >
              <span class="badge bg-light">
                Checklist Task
              </span>
            </div>
          </div>
          <div v-if="isQuickView && !isChecklistEdit">
            <task-status
              :is-quick-view="isQuickView"
              :completed-at="completedAt"
              :due-at="dueAt"
              :value-description="value.description"
              @mark-as-complete="markAsComplete"
            />
          </div>
          <div class="not-as-small text-muted">
            <members-label
              class="d-inline font-weight-semi-bold"
              :members="value.assignees"
            />
            <template v-if="isWritableObject">
              <span class="px-2 text-muted small">&bull;</span>
              <span>
                <a
                  href="#"
                  class="small"
                  data-tc-edit-task-btn
                  @click.stop.prevent="toggleEditTask"
                >Edit</a>
                <span class="px-2 text-muted small">&bull;</span>
                <a
                  href="#"
                  class="small"
                  @click.stop.prevent="openDeleteConfirmationPopup"
                >
                  Delete
                </a>
              </span>
            </template>
          </div>
        </div>

        <div
          v-if="!isQuickView && !isChecklistEdit"
          class="col-sm-6 col-md-3 pl-0"
        >
          <task-status
            :is-quick-view="isQuickView"
            :completed-at="completedAt"
            :due-at="dueAt"
            :value-description="value.description"
            @mark-as-complete="markAsComplete"
          />
        </div>
      </div>
    </div>
    <div v-if="!isChecklistEdit">
      <Teleport to="body">
        <sweet-modal
          ref="deleteModal"
          v-sweet-esc
          title="Before you Proceed..."
        >
          <template slot="default">
            <div :class="{ 'text-red': hasSubTasks, 'text-themed-black': !hasSubTasks }">
              {{ warningMessage }}
            </div>
          </template>
          <button
            slot="button"
            class="btn btn-link text-secondary mr-2"
            @click.stop="$refs.deleteModal.close"
          >
            Cancel
          </button>
          <button
            slot="button"
            class="btn btn-danger"
            data-tc-modal-delete-btn
            @click.stop="deleteTask"
          >
            Delete
          </button>
        </sweet-modal>
      </Teleport>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import http from 'common/http';
import { SweetModal } from 'sweet-modal-vue';
import moment from 'moment-timezone';
import MomentTimezone from 'mixins/moment-timezone';
import MembersLabel from "components/shared/members_label.vue";
import permissionsHelper from "mixins/permissions_helper";
import helpTickets from 'mixins/help_ticket';
import fieldPermissions from 'mixins/custom_forms/field_permissions';
import TaskStatus from './task_status.vue';

export default {
  components: {
    MembersLabel,
    SweetModal,
    TaskStatus,
  },
  mixins: [permissionsHelper, MomentTimezone, fieldPermissions, helpTickets],
  props: ['value', 'selectedTaskIdsArr', 'object', 'isChecklistEdit', 'isQuickView'],
  computed: {
    ...mapGetters(['currentHelpTicket', 'currentHelpTicketDraft', 'enableTicketDrafts']),
    isSelectedCss() {
      return {
        "row-selected": this.isSelected,
      };
    },
    hasSubTasks() {
      return this.value.subTasks && this.value.subTasks.length > 0;
    },
    warningMessage() {
      if (this.hasSubTasks) {
        return " Note:This task has subtasks that will also be deleted. Would you like to delete?";
      }
      return "This Task will be permanently deleted. Would you like to delete?";
    },
    isCompleted() {
      return this.value.completedAt;
    },
    taskCss() {
      const dueAt = moment.utc(this.value.dueAt);
      if (this.isCompleted) {
        return {
          "text-success": true,
        };
      }
      const today = moment();
      if (today > dueAt) {
        return { "text-danger": true };
      }
      return { "text-muted": true };
    },
    isSelected() {
      return this.selectedTaskIdsArr.includes(this.value.id);
    },
    dueAt() {
      if (this.value.dueAt) {
        return moment.parseZone(this.value.dueAt).format("MMM DD, YYYY");
      }
      return "Unassigned";

    },
    completedAt() {
      if (this.value.completedAt) {
        return moment.parseZone(this.value.completedAt).format("MMM DD, YYYY");
      }
      return "";
    },
    isPredefinedTask() {
      return this.value.predefinedTaskId;
    },
  },
  methods: {
    onWorkspaceChange() {
      if (this.enableEditing && this.currentHelpTicketDraft?.tasksData[this.value.id]) {
        this.toggleEditTask();
      }
    },
    toggleEditTask() {
      this.$emit('toggle-edit-task', this.value);
    },
    deleteTask() {
      this.$emit('delete-task',this.value.id);
    },
    unlink() {
      this.$emit('remove-sub-task', this.value.id);
    },
    openDeleteConfirmationPopup() {
      if (this.isChecklistEdit) {
        this.deleteTask();
      } else {
        this.$refs.deleteModal.open();
      }
    },
    selectTask(e) {
      if (e.ctrlKey || e.metaKey || e.shiftKey || e.altKey) {
        this.$emit('select-task', this.value.id);
      }
    },
    markAsComplete() {
      const task = this.value;
      task.completedAt = moment();
      http
        .put(`/tickets/${this.currentHelpTicket.id}/project_tasks/${task.id}.json`, { project_task: task, company_id: this.currentHelpTicket.company.id })
        .then(() => {
          this.$store.dispatch("fetchTasks", this.currentHelpTicket.id).catch(err => {
            this.emitError(`Sorry, there was an error loading tasks. ${err.response.data.message}`);
          });
        })
        .catch(err => {
          this.emitError(`Sorry, there was an error updating the task. ${err.response.data.message}`);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.handle {
  cursor: move;
  font-size: 1.125rem;
}

.row-selected {
  box-shadow: inset 0 0 0 1px $themed-very-fair !important;
  background: $themed-lighter !important;
  border-radius: $border-radius-sm;
}

.sub-task {
  border-bottom: 1px dashed $themed-very-fair;
}

.edit-task {
  z-index: 1;
  right: 10px;
}
</style>
