<template>
  <div>
    <div
      v-if="commentObj.itemType == 'activity'"
      class="my-3 position-relative"
    >
      <div class="d-flex align-items-center mb-3">
        <hr class="flex-grow-1 my-0" >
        <div class="px-2 text-nowrap font-size-small text-secondary">
          <div v-if="commentObj.activityType == 'people_list'">
            <span v-if="currentValue">
              {{ activityLabel }}
              <span class="badge bg-light text-dark rounded-pill pill-size">{{ currentValue }}</span>
              at {{ createdAt[1] }}
            </span>
            <span v-else-if="previousValue">
              <span class="badge bg-light text-dark rounded-pill pill-size">{{ previousValue }}</span>
              is removed from {{ activityLabel }} at {{ createdAt[1] }}
            </span>
          </div>
          <div v-if="commentObj.activityType == 'status'">
            <span v-if="currentValue">
              Moved to
              <span class="badge bg-light text-dark rounded-pill pill-size">{{ currentValue }}</span>
              at {{ createdAt[1] }}
            </span>
          </div>
        </div>
        <hr class="flex-grow-1 my-0" >
      </div>
    </div>
    <div
      v-else-if="commentObj.itemType == 'email'"
      class="my-3 position-relative"
    >
      <div class="d-flex align-items-center mb-3">
        <hr class="flex-grow-1 my-0" >
        <div class="px-2 text-nowrap font-size-small text-secondary">
          Created
          <span class="badge bg-light text-dark rounded-pill pill-size"> via email </span>
          at {{ createdAt[1] }}
        </div>
        <hr class="flex-grow-1 my-0" >
      </div>
    </div>
    <div
      v-else-if="isOriginatingEmailWithoutTimeSpent"
    >
      <div
        v-if="commentsVisibility"
        class="comments-container rounded mb-2 mt-0"
        data-tc-comment-body
        :data-comment-id="commentObj.id"
      >
        <div
          v-if="!showCommentEdit"
          v-click-outside="closeCommentActions"
          class="clearfix py-1 position-relative comment-actions-container"
        >
          <div
            class="row box-width" 
            :class="{ 
              'flex-row-reverse float-right comment-align-right': !isCreatorComment && !isEmailFile,
              'avatar-comment': isEmailFile,
              'flex-row-reverse float-right': isEmailFile && !isCreatorComment,
              'comment-align-left': !isEmailFile && isCreatorComment,
            }"
          >
            <div
              v-if="isEmailFile"
              class="col-auto d-flex align-items-center"
            >
              <avatar
                v-if="username"
                :size="40"
                :username="username"
                :src="userAvatar"
                class="d-inline-block logo-outline mt-1"
              />
            </div>
            <div
              class="col pl-0 email-comment-body"
              :class="{ 'comment-body': !isQuickView }"
            >
              <div
                v-if="isEmailFile"
                class="d-flex small mt-0 p--responsive"
                :class="{'justify-content-end' : !isCreatorComment}"
              >
                <img
                  v-if="sourceImgPath"
                  height="20"
                  :src="sourceImgPath"
                  :alt="toTitle(commentObj.source)"
                  class="mr-1 mb-1"
                >
                <span v-if="isCommentor">
                  <span class="text-muted"><strong>You</strong></span>
                </span>

                <span
                  v-else
                  class="text-muted"
                >
                  <strong v-if="username">
                    <strong>{{ username }}</strong>
                  </strong>
                </span>
              </div>
              <div>
                <trix-render
                  v-if="commentObj"
                  :value="commentObj.commentBody"
                  :last-comment="isEmailAttachments"
                  :is-email-attachments="isEmailAttachments"
                  :max-height-px="maxheight"
                  :is-attachment-comment="commentObj.isAttachmentComment"
                  :is-email-commnt="commentObj.source === 'email'"
                  :is-img="commentObj.isImg"
                  view-more
                  class=" p-2 rounded"
                  :class="{
                    'bg-light': isCreatorComment,
                    'bg-themed-modal-sticky-footer': !isCreatorComment,
                  }"
                  @display-comments="displayComments"
                />
              </div>
              <div
                v-if="isLastInCluster"
                class="d-flex small mt-0 p--responsive"
                :class="{'justify-content-end' : !isCreatorComment}"
              >
                <span class="text-muted">
                  {{ createdAt[0] }} • {{ createdAt[1] }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="!timespentComment"
      :class="{'mb-1': !lastComment}"
    >
      <div
        v-if="commentsVisibility"
        class="comments-container rounded mb-2 mt-0"
        data-tc-comment-body
        :data-comment-id="commentObj.id"
      >
        <div
          v-if="!showCommentEdit"
          v-click-outside="closeCommentActions"
          class="clearfix py-1 position-relative comment-actions-container"
          :class="{ 'private': commentObj.privateFlag }"
        >
          <div
            class="row box-width"
            :class="{
              'flex-row-reverse float-right': !isCreatorComment,
              'p-2': commentObj.privateFlag,
              [commentObj.firstInCluster 
                ? 'avatar-comment' 
                : isCreatorComment 
                  ? 'comment-align-left' 
                  : 'comment-align-right']: true
            }"
          >
            <div
              v-if="isFirstInCluster"
              class="col-auto d-flex align-items-center"
            >
              <avatar
                v-if="username"
                class="d-inline-block logo-outline mt-1"
                :size="40"
                :username="username"
                :src="userAvatar"
              />
            </div>
            <div
              class="col pl-0"
              :class="{ 'comment-body' : !isQuickView }"
            >
              <div
                v-if="isFirstInCluster"
                class="d-flex small mt-0 p--responsive"
                :class="{'justify-content-end' : !isCreatorComment}"
              >
                <img
                  v-if="sourceImgPath"
                  class="mr-1 mb-1"
                  height="20"
                  :src="sourceImgPath"
                  :alt="toTitle(commentObj.source)"
                >
                <span v-if="isCommentor">
                  <span class="text-muted"><strong>You</strong></span>
                </span>

                <span
                  v-else
                  class="text-muted"
                >
                  <strong v-if="username">
                    <strong>{{ username }}</strong>
                    <sup
                      v-if="commentObj.source === 'auto_generated'"
                      v-tooltip="'This is an auto-generated comment.'"
                      class="text-muted nulodgicon-information-circled small"
                    />
                  </strong>
                </span>
              </div>
              <div
                v-if="commentObj.parentComment"
                :key="commentObj.parentComment.id"
                class="d-flex flex-column"
              >
                <reply-trix-render
                  :id="commentObj.parentComment.id + commentObj.id"
                  :value="commentObj.parentComment.commentBody"
                  :contributor-name="commentObj.contributor && commentObj.contributor.name"
                  :comment-show="true"
                  :is-email-attachments="isEmailAttachments"
                  :is-attachment-comment="commentObj.isAttachmentComment"
                  :is-email-commnt="commentObj.source === 'email'"
                  view-more
                  class="reply-box-border"
                  data-tc-comment
                />
              </div>
              <div>
                <trix-render
                  v-if="commentObj"
                  :id="commentObj.id"
                  :value="commentObj.commentBody"
                  :last-comment="lastComment"
                  :is-attachment-comment="commentObj.isAttachmentComment"
                  :is-email-commnt="commentObj.source === 'email'"
                  :is-img="commentObj.isImg"
                  view-more
                  :is-email-attachments="isEmailAttachments"
                  class="p-2"
                  :class="{
                    'comment-box-border': commentObj.parentComment,
                    'rounded': !commentObj.parentComment,
                    'bg-light': isCreatorComment,
                    'bg-themed-modal-sticky-footer': !isCreatorComment,
                  }"
                  data-tc-comment
                  @display-comments="displayComments"
                />
              </div>
              <div
                v-if="isLastInCluster || lastComment"
                class="d-flex small mt-0 p--responsive"
                :class="{'float-right' : !isCreatorComment}"
              >
                <router-link
                  v-if="isMergedTicketComment"
                  :to="{ path: `${commentObj.mergedTicketId}` }"
                  target="_blank"
                >
                  <img
                    v-tooltip="getTooltip"
                    :src="mergedIcon"
                    class="source-icon mr-1"
                  >
                </router-link>

                <span class="text-muted">
                  {{ createdAt[0] }} • {{ createdAt[1] }}
                </span>
                <span
                  v-if="isCommentUpdated"
                  class="text-muted"
                >
                  <span class="mx-1">&bull;</span>
                  <span v-tooltip="`Last updated at: ${ updatedAt }`">
                    Edited
                  </span>
                </span>
              </div>
            </div>
            <div
              v-if="!isQuickView && !isCreatorComment"
              class="float-right px-2 align-items-center  d-flex"
              style="position: relative;"
              :class="{ 'flex-row-reverse' : !isCreatorComment }"
            >
              <div
                v-tooltip="'Comment Actions'"
                v-click-outside="closeCommentActions"
                class="d-flex clickable mx-2 float-right action-icons font-size-smaller"
                @click="showCommentActions = !showCommentActions"
              >
                <i class="genuicon-ellipsis-v" /> 
              </div>
              <i 
                v-tooltip="'Reply'"
                class="genuicon-reply-arrow reply-icon cursor-pointer"
                @click="replyToComment(commentObj)"
              />
              <div
                class="dropdown-menu comment-actions-dropdown not-as-small mt-4"
                :class="{ 'show': showCommentActions }"
              >
                <div
                  v-if="isSubjectExist && isWriteAny"
                  class="text-secondary dropdown-text-menu px-3 py-2"
                  @click.stop.prevent="openSplitTicketModal"
                >
                  <i class="genuicon-ticket mr-2 small" />Split Ticket
                </div>
                <div
                  v-if="commentObj.contributorId === currentContributorId && (isWriteAny || isBasicAccess)"
                  class="text-secondary dropdown-text-menu px-3 py-2"
                  @click.stop.prevent="toggleEditComment"
                >
                  <i class="genuicon-pencil-square-o mr-1" />Edit Comment
                </div>
                <div
                  v-if="canDelete"
                  :id="`delete-comment-icon-${commentObj.id}`"
                  class="text-secondary dropdown-text-menu px-2 py-0"
                  @click.stop="deleteComment"
                >
                  <i class="nulodgicon-trash-b remove-link clickable h5"/>Delete Comment
                </div>
              </div>
              <span
                :id="`delete-comment-loader-${commentObj.id}`"
                class="comment-action px-2 d-none"
              >
                <pulse-loader
                  :loading="true"
                  class="d-inline-block"
                  color="#000"
                  size="1rem"
                />
              </span>
            </div>
            <div v-else>
              <span
                v-tooltip="'Comment Actions'"
                v-click-outside="closeCommentActions"
                class="clickable mx-2 h-100 d-flex align-items-center float-right float-right action-icons font-size-smaller"
                @click="showCommentActions = !showCommentActions"
              >
                <i class="genuicon-ellipsis-v" />
              </span>
              <div
                class="dropdown-menu quickview-comment-actions-dropdown not-as-small mt-5"
                :class="{ 'show': showCommentActions }"
                :style="{ left: !isCreatorComment ? '0' : 'auto' }"
              >
                <div
                  v-if="showArticleButton"
                  class="text-secondary dropdown-text-menu px-3 py-2"
                  @click.stop.prevent="openArticleModal"
                >
                  <i class="genuicon-knowledge-base mr-2 small" />Create Article
                </div>
                <div
                  v-if="isSubjectExist && isWriteAny"
                  class="text-secondary dropdown-text-menu px-3 py-2"
                  @click.stop.prevent="openSplitTicketModal"
                >
                  <i class="genuicon-ticket mr-2 small" />Split Ticket
                </div>
                <div
                  v-if="commentObj.contributorId === currentContributorId && (isWriteAny || isBasicAccess)"
                  class="text-secondary dropdown-text-menu px-3 py-2"
                  @click.stop.prevent="toggleEditComment"
                >
                  <i class="genuicon-pencil-square-o mr-2 small" />Edit Comment
                </div>
                <div
                  v-if="canDelete"
                  :id="`delete-comment-icon-${commentObj.id}`"
                  class="text-secondary dropdown-text-menu px-3 py-2"
                  @click.stop="deleteComment"
                >
                  <i class="nulodgicon-trash-b mr-2 small"/>Delete Comment
                </div>
                <span
                  :id="`delete-comment-loader-${commentObj.id}`"
                  class="commentObj-action px-2 d-none"
                >
                  <pulse-loader
                    :loading="true"
                    class="d-inline-block"
                    color="#000"
                    size="0.5rem"
                  />
                </span>
              </div>
            </div>
          </div>
          <div
            v-if="commentObj.privateFlag"
            class="private-copy bg-yellow w-25"
            :style="{ left: !isCreatorComment ? '0' : 'auto' }"
          >
            <small class="private-text text-secondary">
              <i class="nulodgicon-locked pr-1 text-alternate" />
              <template
                v-for="(user, index) in privateContributors.slice(0,2)"
              >
                <span
                  :key="user.id"
                  class="p-1 private-user"
                >
                  <a
                    :href="profileLink(user)"
                    target="_blank"
                    class="text-alternate"
                  >
                    {{ userName(user) }}
                    <span v-if="privateContributors.length > 1 && index < privateContributors.length-1">,</span>
                  </a>
                </span>
              </template>
              <span
                v-if="privateContributors.length > 2"
                class="private-user clickable text-alternate"
                @click="openPrivateContributorsModal"
              >
                show all&hellip;
              </span>
            </small>
          </div>
          <private-contributors-list
            ref="privateContributorsListModal"
            :private-contributors="privateContributors"
          />
        </div>
        <edit-comment
          v-else
          usage="Comment"
          :comment="getComment"
          :private-contributors="privateContributors"
          :is-quick-view="isQuickView"
          is-editing
          show-private-users-option
          @update-save-status="toggleEditComment"
          @hide-editor="hideEditor"
          @private-contributors="updatePrivateContributors"
        />
        <Teleport to="body">
          <split-ticket-modal
            ref="splitTicketModal"
            :is-splitting="splittingTicket"
            :comment="commentObj"
            @select-user="selectUser"
            @remove-user="removeUser"
            @split-ticket="splitTicket"
          />
        </Teleport>
        <Teleport to="body">
          <article-modal 
            ref="articleModal"
            is-comment-article
            :selected-comment="commentObj"
            @article-created="loadHelpTicket"
          />
        </Teleport>
      </div>
    </div>
  </div>
</template>
<script>
  import MomentTimezone from 'mixins/moment-timezone';
  import { Avatar } from 'vue-avatar';
  import { mapGetters, mapActions, mapMutations } from 'vuex';
  import _get from 'lodash/get';
  import http from 'common/http';
  import strings from 'mixins/string';
  import permissionsHelper from "mixins/permissions_helper";
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import helpTickets from 'mixins/help_ticket';
  import vClickOutside from 'v-click-outside';
  import _cloneDeep from 'lodash/cloneDeep';
  import ArticleModal from 'components/knowledge_base/article_modal.vue';
  import customForms from 'mixins/custom_forms';
  import EditComment from '../modern_add_comment.vue';
  import PrivateContributorsList from './private_contributors_list.vue';
  import TrixRender from '../../../trix_render.vue';
  import ReplyTrixRender from '../../../reply_trix_render.vue';
  import SplitTicketModal from '../../split_ticket_modal.vue';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      Avatar,
      TrixRender,
      PrivateContributorsList,
      EditComment,
      SplitTicketModal,
      PulseLoader,
      ArticleModal,
      ReplyTrixRender,
    },
    mixins: [MomentTimezone, permissionsHelper, strings, helpTickets, customForms],
    props: ['comment', 'lastComment','commentsVisibility', 'isQuickView', 'isEmailAttachments', 'isEmailFile'],
    data() {
      return {
        showCommentEdit: false,
        isSplitting: false,
        selectedContributor: null,
        timeSpentCommentId: null,
        editableComment: {...this.comment},
        showCommentActions: false,
        commentObj: _cloneDeep(this.comment),
        showDropdown: false,
        replyMode: false,
        replyText: '',
        replyToCommentId: null,
        showActions: true,
      };
    },
    computed: {
      ...mapGetters([
        'companyUserOptions',
        'contractAssetTicketNames',
        'currentHelpTicket',
        'timeSpents',
        'currentHelpTicketDraft',
        'enableTicketDrafts',
      ]),
      showArticleButton() {
        return this.isSubjectExist && this.isWriteAny && this.commentObj.isAgentComment && !this.commentObj.isArticlePresent;
      },
      isCommentor() {
        if ((this.commentObj.contributorId || this.$superAdminUser) &&
          this.commentObj.contributorId === this.currentContributorId &&
          (this.commentObj.source === 'manually_added')) {
            return true;
          } else if (this.commentObj.itemType === 'originating_email') {
            return this.$currentUserEmail === this.commentObj.email;
        }
        return false;
      },
      splittingTicket() {
        return this.isSplitting;
      },
      isSubjectExist() {
        return this.commentObj.commentText;
      },
      currentContributorId() {
        return this.$currentContributorId;
      },
      isCommentUpdated() {
        return this.commentObj.updatedAt > this.commentObj.createdAt;
      },
      privateContributors() {
        return this.commentObj.privateContributors;
      },
      createdAt() {
        if (this.commentObj.itemType === 'email') {
          return ['', this.timezoneDatetime(this.commentObj.createdAt, Vue.prototype.$timezone)];
        }
        const timeZoneDatetime = this.timezoneDatetime(this.commentObj.createdAt, Vue.prototype.$timezone);
        return [moment(this.commentObj.createdAt).fromNow(), timeZoneDatetime];
      },
      updatedAt() {
        return this.timezoneDatetime(this.commentObj.updatedAt, Vue.prototype.$timezone);
      },
      username() {
        if (this.commentObj.source === "auto_generated") {
          return "Auto Generated ";
        } else if (!this.commentObj.contributor && this.commentObj.source === "manually_added") {
          return "System";
        }
        return _get(this, "comment.contributor.name") || _get(this, "comment.email") || "";
      },
      userAvatar() {
        return _get(this, "comment.contributor.avatar");
      },
      sourceImgPath() {
        if (this.commentObj.source === 'slack') {
          return "https://nulodgic-static-assets.s3.amazonaws.com/images/slack.svg";
        } else if (this.commentObj.source === 'ms_teams') {
          return "https://nulodgic-static-assets.s3.amazonaws.com/images/ms_teams.svg";
        } else if (this.commentObj.source === 'manually_added') {
          return 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/manual.png';
        }
        return null;
      },
      contractAssetTicketNamesPresent() {
        return this.contractAssetTicketNames && Object.keys(this.contractAssetTicketNames).length > 0;
      },
      timeSpent() {
        const timeSpents = this.timeSpents.find(timeSpent => timeSpent.helpTicketCommentId === this.commentObj.id);
        if (timeSpents) {
          this.setTimeSpentCommentId(timeSpents.helpTicketCommentId);
          return true;
        }
        return null;
      },
      timespentComment() {
        this.displayComments();
        return this.timeSpent && this.timeSpentCommentId;
      },
      canDelete() {
        return this.commentObj.contributorId === this.currentContributorId || this.isWrite;
      },
      getTooltip() {
        const data = this.commentObj.mergedTicketData;
        return data ? `#${data.ticketNumber} ${data.subject}` : '';
      },
      isMergedTicketComment() {
        return !!this.commentObj.mergedTicketId;
      },
      isCreatorComment() {
        return this.commentObj.contributorId === this.currentContributorId;
      },
      currentValue() {
        return this.commentObj?.data.currentValue;
      },
      previousValue() {
        return this.commentObj?.data.previousValue;
      },
      activityLabel() {
        if (this.commentObj?.data && this.commentObj.data?.activityLabel) {
          return this.commentObj?.data.activityLabel;
        }
        return '';
      },
      isFirstInCluster() {
        return this.commentObj.firstInCluster === true;
      },
      isLastInCluster() {
        return this.commentObj.isLast === true;
      },
      maxheight() {
        if (this.isEmailFile) {
          return 70;
        }
        return 100;
      },
      getComment() {
        if (this.commentObj.isAttachmentComment) {
          const comment = {...this.commentObj};
          comment.commentBody = comment?.oldCommentBody;
          return comment;
        }
        return this.commentObj;
      },
      isOriginatingEmailWithoutTimeSpent() {
        return !this.timespentComment && this.commentObj.itemType === 'originating_email';
      },
    },
    watch: {
      comment: {
        handler(newValue, oldValue) {
          if (newValue !== oldValue) {
            this.commentObj = _cloneDeep(this.comment);
          }
        },
        deep: true,
      },
    },
    methods: {
      ...mapMutations([
        'setCurrentHelpTicket',
        'setLoadingTicket',
        'setQuickViewTicketId',
        'setCommentReply',
        'setComments',
      ]),
      ...mapActions([
        'fetchSelectedContributor',
      ]),
      onWorkspaceChange() {
        if (this.enableEditing && this.currentHelpTicketDraft?.comments[this.editableComment.id]) {
          this.toggleEditComment();
        }
      },
      openPrivateContributorsModal() {
        this.$refs.privateContributorsListModal.open();
      },
      openArticleModal() {
        this.$refs.articleModal.reset();
        this.$refs.articleModal.open();
      },
      userName(user) {
        if (user.name) {
          return user.name;
        }
          return user.email;
      },
      closeCommentActions() {
        this.showCommentActions = false;
      },
      setTimeSpentCommentId(value) {
        this.timeSpentCommentId = value;
      },
      toggleEditComment() {
        this.showCommentEdit = !this.showCommentEdit;
        this.closeCommentActions();
      },
      hideEditor() {
        this.showCommentEdit = false;
      },
      updatePrivateContributors(updatedContributors) {
        this.commentObj.privateContributors = updatedContributors;
      },
      displayComments() {
        this.$emit('display-comments');
      },
      profileLink(user) {
        if (user.companyGroupId) {
          return `/company/groups/${user.companyGroupId}/edit`;
        }
        return `/company/users/${user.companyUserId}`;
      },
      openSplitTicketModal() {
        if (this.commentObj.contributorId) {
          this.fetchSelectedContributor({
            includes: this.commentObj.contributorId,
            company_id: this.currentHelpTicket.companyId,
          });
        }
        this.$refs.splitTicketModal.open();
      },
      selectUser(contributor) {
        this.selectedContributor = contributor;
      },
      removeUser() {
        this.selectedContributor = null;
      },
      createObject() {
        const ticketValues = [];
        this.currentHelpTicket.customForm.formFields.forEach((field) => {
          if (field.name === 'subject') {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: this.commentObj.commentText,
            });
          } else if (field.name === 'status') {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: field.defaultValue || 'Open',
            });
          } else if (field.name === 'priority') {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: field.defaultValue || 'low',
            });
          } else if (field.name === 'created_by' && this.commentObj.email && !this.selectedContributor) {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: this.commentObj.email,
            });
          } else if (field.name === 'created_by' && (this.commentObj.contributorId || this.selectedContributor)) {
            ticketValues.push({
              customFormFieldId: field.id,
              valueInt: this.selectedContributor ? this.selectedContributor.id : this.currentContributorId,
            });
          }
        });
        return ticketValues;
      },
      splitTicket() {
        const object = {
          source: 'splitted',
          commentId: this.commentObj.id,
          values: this.createObject(),
          customFormId: this.currentHelpTicket.customFormId,
        };
        const url = `/custom_forms/${this.currentHelpTicket.customForm.id}/custom_form_tickets.json`;
        this.isSplitting = true;
        http
          .post(url, { json: JSON.stringify(object)} )
          .then(res => {
            this.emitSuccess(`HelpTicket split successfully`);
            this.$refs.splitTicketModal.close();
            this.setComments([]);
            if (!this.isQuickView) {
              this.$router.push(`/${res.data.entity.id}`);
            } else {
              this.setQuickViewTicketId(res.data.entity.id);
            }
            this.loadHelpTicket();
          })
          .catch(() => {
            this.emitError('We encountered an error splitting the ticket. Please refresh the page and try again.');
          })
          .finally(() => {
            this.isSplitting = false;
          });
      },
      loadHelpTicket() {
        this.setCurrentHelpTicket(null);
        this.setLoadingTicket(true);
        this.$store.dispatch("fetchTicket", this.ticketId);
      },
      deleteComment() {
        if (this.editableComment.id) {
          const params = { 
            ticket_id: this.currentHelpTicket.id,
            company_id: this.currentHelpTicket.company.id,
          };

          const commentLoader = document.querySelector(`#delete-comment-loader-${this.editableComment.id}`);
          const commentDeleteIcon = document.querySelector(`#delete-comment-icon-${this.editableComment.id}`);

          commentDeleteIcon.classList.add("d-none");
          commentLoader.classList.remove("d-none");

          http
            .delete(`/ticket_comments/${this.editableComment.id}`, {params})
            .then(() => {
              this.emitSuccess(`Ticket comment deleted`);
              this.$store.dispatch('fetchTimeSpents', this.ticketId);
              this.$store.dispatch('fetchComments', this.ticketId);
              window.onbeforeunload = null;
            })
            .catch((error) => {
              this.emitError(error.response.data.message);
            })
            .finally(() => {
              commentDeleteIcon.classList.remove("d-none");
              commentLoader.classList.add("d-none");
              this.$store.dispatch("fetchTicket", this.ticketId);
          });
        }
      },
      onBlur() {
        this.showDropdown = false;
      },
      async submitReply(parentCommentId) {
        if (!this.replyText.trim()) return;

        try {
          const response = await http.post('/help_ticket_comments', {
            help_ticket_comment: {
              comment_body: this.replyText,
              help_ticket_id: this.commentObj.help_ticket_id,
              contributor_id: this.currentContributorId,
              parent_comment_id: parentCommentId,
            },  
          });

          this.$emit('comment-replied', response.data);
          this.cancelReply();
        } catch (error) {
          console.error('Failed to reply:', error);
        }
      },
      replyToComment(reply) {
        this.$emit('replied-response', reply);
      },
      toggleIcons(value) {
        this.showActions = value;
      },
      showCreatedAt() {
        return this.isA;
      },
    },
  };
</script>

<style lang="scss" scoped>
  $border-color: rgb(250, 235, 210);

  .nulodgicon-plus-round::before {
    top: 0
  }
  .private {
    outline-color: #ffb648 ;
    outline-style: auto;
  }

  .private-copy {
    border-radius: 0.25rem 0 0 0;
    top: 0;
    right: 0;
    padding: 0 0.5rem;
    position: absolute;

    .private-text {
      font-weight: 700;
      letter-spacing: 0.031rem;
    }
  }

  .comment-action {
    color: $gray-500;
    font-size: 1.125rem;
  }

  .dropdown-comment-action {
    display: flex;
  }

  .highlight-comment {
    margin-bottom: 0.625rem;
    transition: box-shadow 400ms;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.50);
  }

  .source-icon {
    width: 0.938rem;
    height: 0.938rem;
    cursor: pointer;
  }

  .remove-link {
    color: $themed-fair;
    &:hover {
      color: $danger;
    }
  }

  .comment-body {
    padding-right: 0;
  }

  .email-comment-right {
    margin-right: 4.563rem !important;
  }

  .email-comment-left {
    margin-left: 4.563rem !important;
  }

  .box-height {
    max-height: 3.125rem;
  }

  .quickview-comment-actions-dropdown {
    left: 21.25rem;
    top: 2rem;
    z-index: 1050;
  }

  .dropdown-container {
    position: relative;
    display: inline-block;
  }

  .comment-actions-dropdown {
    position: absolute;
    left: 0;
    z-index: 1050;
    width: max-content;
    display: none;
    top: auto;
  }

  .comment-actions-dropdown.show {
    display: block;
  }

  .comment-align-right {
    padding-right: 4.5rem !important;
  }

  .comment-align-left {
    padding-left: 4.5rem !important;
  }

  .avatar-comment {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  .reply-box-border {
    border-radius: 0.5rem 0.5rem 0 0 !important;
    background-color: var(--themed-very-light) !important;
    height: auto;
    padding: 0.5rem 0.5rem 0 0.5rem !important;
  }
  .comment-box-border {
    border-radius: 0 0 0.5rem 0.5rem !important;
    border: 1px solid var(--themed-fair);
    border-top: 0;
  }
  .comment-actions-container .action-icons,
  .comment-actions-container .reply-icon {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  .comment-actions-container:hover .action-icons,
  .comment-actions-container:hover .reply-icon {
    opacity: 0.8;
  }
  .font-size-small {
    font-size: smaller;
    color: #313131;
  }
  .pill-size {
    font-size: 100%;
    font-weight: 200;
  }
  .box-width {
    width: 63% !important;
  }
</style>
