<template>
  <div>
    <div 
      ref="commentsContainer"
      :class="{
        'adjust-section': !hideHeader,
        'show-section': hideHeader
      }"
      :style="sectionStyle"
      @scroll="handleScroll"
    >
      <div
        v-if="isLoadingMore && commentsPage > 0"
        class="loading-bar text-center py-2"
      >
        <span class="text-secondary font-weight-normal">Loading more comments</span>
        <span>
          <pulse-loader
            :loading="isLoadingMore"
            class="ml-3"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </div>
      <div
        v-if="!filteredComments.length > 0 && commentsCount && !scheduledComments.length"
        class="loading-bar"
        :class="{'d-none': commentsVisibility || allComments.length}"
      >
        <span class="float-left text-secondary font-weight-normal">Loading comments</span>
        <span>
          <pulse-loader
            :loading="!commentsVisibility && allComments.length === 0"
            class="ml-3 float-left"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </div>
      <div v-if="currentHelpTicket.hasOriginatingEmail">
        <modern-comment
          :comment="{ itemType: 'email', createdAt: currentHelpTicket.originatingEmail.createdAt }"
          :comments-visibility="commentsVisibility"
          :is-quick-view="isQuickView"
          @display-comments="displayComments"
          @replied-response="commentReply"
        />
        <div 
          v-for="(emailCmt, index) in emailComment"
          :key="index"
          class="d-flex flex-column"
        >
          <modern-comment
            :comment="emailCmt"
            :comments-visibility="commentsVisibility"
            :is-quick-view="isQuickView"
            :is-email-file="index === 0"
            :is-email-attachments="index > 1"
          />
        </div>
      </div>
      <div
        v-for="(ticketComment, index) in filteredComments"
        :key="ticketComment.id"
        class="d-flex flex-column"
      >
        <modern-comment
          :comment="ticketComment"
          :last-comment="index == filteredComments.length - 1"
          :comments-visibility="commentsVisibility"
          :is-quick-view="isQuickView"
          @display-comments="displayComments"
          @replied-response="commentReply"
        />
      </div>
    </div>
    <div 
      v-if="showNewMessageButton && !isScrollerAtBottom"
      :class="{ 'margin-comment-box': expandCommentSection}"
      class="d-flex justify-content-center new-message-button"
    >
      <button
        class="btn btn-sm btn-primary form-btn--responsive d-flex align-items-center"
        @click.stop="scrollToBottom"
      >
        <span>
          New Comment 
          <i class="genuicon-download" />
        </span>
      </button>
    </div>
    <Teleport
      to=".quick-view-container"
      :disabled="!isQuickView"
    >
      <div v-if="comment.commentBody">
        <reply-trix-render
          :value="comment.commentBody"
          :contributor-name="contributorName"
          :comment-reply="true"
          view-more
          class="reply-box-border"
          @clear-reply="cancelReply"
        />
      </div>
      <div
        v-if="scheduledComments.length"
        class="d-flex justify-content-start align-items-center p-1 rounded bg-light text-dark comments-text-banner"
      >
        <i class="ml-2 genuicon-clock-o" />
        <span>You have scheduled comments.</span>
        <a
          href="#"
          class="text-primary fw-medium no-underline-on-load"
          @click.prevent="$emit('show-scheduled-comments')"
        >
          See all scheduled comments
        </a>
      </div>
      <div
        v-if="currentHelpTicket"
        class="mb-1"
        :class="{'dismissible-container__sticky-footer': isQuickView }"
      >
        <button
          v-if="collapseCommentSection && !isReplyResponse"
          class="form-control clickable mb-3 text-muted text-left btn-sm"
          :disabled="disabled"
          @click.stop.prevent="toggleShowEditor"
        >
          Add new comment
        </button>

        <modern-add-comment
          v-else
          usage="Comment"
          show-private-users-option
          :comment="getActiveComment"
          :is-quick-view="isQuickView"
          :reply-comment="comment"
          @update-save-status="updateSaveStatus"
          @hide-editor="hideEditor"
          @remove-draft="removeDraft"
          @reply-added="cancelReply"
        />
      </div>
    </Teleport>
    <div
      v-if="isQuickView && currentHelpTicket"
      class="mt-0 ml-1"
      :class="{'mt-4': (filteredComments.length > 0)}"
    >
      <i class="genuicon-info-circled d-inline pt-0.5 mr-1 text-muted align-middle" />
      <span class="text-muted">Add a new comment at the bottom of the screen</span>
    </div>
  </div>
  
</template>

<script>
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import ticketAttachments from 'mixins/ticket_attachments';
  import http from 'common/http';
  import customForms from 'mixins/custom_forms';
  import _get from 'lodash/get';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from "mixins/permissions_helper";
  import suggestions from 'mixins/automated_tasks/suggestions';
  import helpTickets from 'mixins/help_ticket';
  import ModernComment from './modern_comment.vue';
  import ModernAddComment from '../modern_add_comment.vue';
  import ReplyTrixRender from '../../../reply_trix_render.vue';
  import string from '../../../../mixins/string';

  export default {
    components: {
      ModernAddComment,
      ModernComment,
      PulseLoader,
      ReplyTrixRender,
    },
    mixins: [suggestions, customForms, permissionsHelper, helpTickets, ticketAttachments, string],
    beforeRouteLeave(to, from, next) {
      if (this.commentSaveStatus) {
        next();
        /* eslint-disable-next-line no-alert */
      } else if (window.confirm("Are you sure you want to leave? You have some unsaved changes")) {
          next(true);
        } else {
          next(false);
        }
    },
    props: {
      newCommentId: {
        default: null,
      },
      isQuickView: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        commentSaveStatus: true,
        showEditor: false,
        commentsVisibility: false,
        draftComment: null,
        comment: {},
        ticketEmail: {},
        ticketAttachments: [],
        ticketEmailContriburorId: null,
        emailComment: [],
        isReplyResponse: false,
        showNewMessageButton: false,
        commentByDifferentUser: {},
        isScrollAtBottom: false,
        allComments: [],
        isLoadingMore: false,
        scrollThreshold: 50,
        lastScrollHeight: 0,
        shouldLoadMoreComments: false,
      };
    },
    computed: {
      ...mapGetters([
        'currentHelpTicket',
        'currentHelpTicketDraft',
        'enableTicketDrafts',
        'comments',
        'commentsPage',
        'commentsPageCount',
        'expandCommentSection',
        'hideHeader',
        'isCommentUpdated',
        'commentsCount',
        'scheduledComments',
      ]),
      ...mapGetters('GlobalStore', ['currentCompanyUser']),

      disabled() {
        return !this.isWrite && !this.isMyTicket && !((this.isScopedAny || this.isBasicAccess) && (this.isAssignedToMe || this.isFollower));
      },
      collapseCommentSection() {
        return !this.showEditor && !this.draftComment && !this.expandCommentSection;
      },
      isMyTicket() {
        if (!this.currentHelpTicket) {
          return false;
        }
        const values = this.getValuesForName(this.currentHelpTicket, 'created_by');
        if (values.length < 1) {
          return false;
        }
        return !!values.find((value) => value && value.valueInt && value.valueInt === this.$currentContributorId);
      },

      isAssignedToMe() {
        if (!this.currentHelpTicket) {
          return false;
        }
        const values = this.getValuesForName(this.currentHelpTicket, 'assigned_to');
        if (values.length < 1) {
          return false;
        }

        const value = values.find(val => val.valueInt === this.$currentContributorId);
        const groupMembers = this.currentHelpTicket.groupMemberIds;
        if (!value && groupMembers) {
          const parsedIds = JSON.parse(groupMembers);
          if (parsedIds.assigned_to?.includes(this.$currentContributorId)) {
            return true;
          }
        }
        return !!(value);
      },

      isFollower() {
        if (!this.currentHelpTicket) {
          return false;
        }
        else if (this.currentHelpTicket.allowFollower) {
          if (this.currentHelpTicket.followerIds.length < 1) {
            return false;
          }
          return this.currentHelpTicket.followerIds.includes(this.$currentContributorId);
        }
        return false;
      },
      filteredComments() {
        this.strctureReplyComments();
        let temp = [];
        if (this.allComments.length) {
          let previousCommentTime = null;
          const sorted = [...this.allComments].sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
          temp = sorted.filter((comment, index) => {
            const currentCommentTime = new Date(comment.createdAt).getTime();
            if (
              index === 0 ||
              sorted[index - 1].contributorId !== comment.contributorId ||
              sorted[index - 1].activityType ||
              (previousCommentTime && (currentCommentTime - previousCommentTime) > 5 * 60 * 1000)
            ) {
              comment.showDetails = true;
              comment.firstInCluster = true;
              comment.isLast = false;
              if (previousCommentTime) {
                sorted[index - 1].isLast = true;
              }
            } else {
              comment.showDetails = false;
              comment.firstInCluster = false;
              comment.isLast = false;
            }
            const updatedComment = {
              ...comment,
              commentBody: this.processCommentAttachments(comment).commentBody,
            };
            if (!comment.itemType && comment.contributorId !== this.$currentContributorId) {
              this.commentByDifferentUser = comment;
              comment.itemType = 'comment';
              comment.firstInCluster = true;
              comment.isLast = false;
            }
            if (updatedComment?.privateFlag) {
              const contributorId = this.$currentContributorId || '';
              const groupMemberIds = updatedComment.privateContributors.map(item => item.groupMembersIds).flat();
              const contributorIds = groupMemberIds.concat(updatedComment.privateContributorIds);
              return contributorIds.map(String).includes(contributorId.toString()) && updatedComment.commentBody;
            }
            if (updatedComment.itemType === 'activity') {
              if (!this.isCommentUpdated && !Object.keys(this.commentByDifferentUser).length &&  this.comment.contributorId !== this.$currentContributorId) {
                this.$nextTick(() => this.scrollToBottom());
              }
              return updatedComment;
            }
            previousCommentTime = currentCommentTime;
            return updatedComment.commentBody;
          });
        }
        return temp;
      },
      newComment() {
        return {
          commentBody: null,
          helpTicketId: _get(this, 'currentHelpTicket.id', null),
          privateFlag: false,
          privateContributorIds: [],
          contributorId: Vue.prototype.$currentContributorId,
          resolutionFlag: false,
        };
      },
      newCommentExists() {
       return this.newCommentId && this.allComments.some(com => com.id === this.newCommentId);
      },
      getActiveComment() {
        this.fetchDraftComment();
        return this.draftComment ? this.draftComment : this.newComment;
      },
      contributorName() {
        return this.comment.contributor?.name;
      },
      isScrollerAtBottom() {
        return this.isScrollAtBottom;
      },
      sectionStyle() {
        const baseVH = this.getBaseHeight;
        const adjustedVH = this.collapseCommentSection && !this.isReplyResponse ? baseVH + 12 : baseVH;

        return {
          height: `calc(${adjustedVH}vh - 30rem)`,
        };
      },
      getBaseHeight() {
        let vh;
        const width = window.innerWidth;

        if (this.hideHeader) {
          if (width >= 1600) vh = 104;
          else if (width >= 1450) vh = 106;
          else if (width >= 1300) vh = 109;
          else vh = 114;
        } else if (width >= 1600) vh = 96;
          else if (width >= 1300) vh = 98;
          else if (width >= 1200) vh = 99;
          else vh = 107;

        return vh;
      },
    },
    watch: {
      comments: {
        handler(newComments) {
          if (newComments.length) {
            if (this.commentsPage === 0) {
              this.allComments = newComments;
            } else {
              const container = this.$refs.commentsContainer;
              const previousScrollHeight = container?.scrollHeight || 0;
              const previousScrollTop = container?.scrollTop || 0;
              const existingIds = new Set(this.allComments.map(c => c.id));
              
              for (let i = 0; i < newComments.length; i += 1) {
                if (!existingIds.has(newComments[i].id)) {
                  this.allComments.unshift(newComments[i]);
                }
              }

              this.$nextTick(() => {
                if (container) {
                  const newScrollHeight = container.scrollHeight;
                  const heightDiff = newScrollHeight - previousScrollHeight;
                  container.scrollTop = previousScrollTop + heightDiff;
                }
              });
            }
            this.isLoadingMore = false;
          } else {
            this.allComments = newComments;
          }
        },
        immediate: true,
      },
      commentByDifferentUser: {
        handler() {
          const container = this.$refs.commentsContainer;
          if (!container) return;
          this.isScrollAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 1;
        },
        deep: true,
      },
    },
    updated() {
      if (this.newCommentExists && this.commentsVisibility) {
        this.openNewCommentModal();
      }
    },
    created() {
      if (this.enableEditing && ('newComment' in this.currentHelpTicketDraft.comments)) {
        this.toggleShowEditor();
      }
    },
    beforeDestroy() {
      this.$store.commit('setCommentsPage', 0);
      this.$store.commit('setCommentsPageCount', 0);
    },
    methods: {
      ...mapMutations(['setCommentsPage', 'setExpandCommentSection', 'setIsCommentUpdated']),
      ...mapActions('GlobalStore', ['fetchCurrentCompanyUser']),

      onWorkspaceChange() {
        this.commentByDifferentUser = {};
        this.$store.dispatch('fetchTimeSpents', this.ticketId);
        if (this.currentHelpTicket.hasOriginatingEmail) {
          this.fetchOriginatingEmail();
        }
        this.fetchCurrentCompanyUser({ fetch_permission: false });
        this.fetchDraftComment();
        this.fetchScheduledComments();
        this.fetchComments();
      },
      fetchScheduledComments() {
        const helpticketId = this.ticketId;
        const contributorId = this.$currentContributorId;
        this.$store.dispatch('fetchScheduledComments', { helpticketId, contributorId });
      },
      handleScroll(event) {
        const { scrollTop } = event.target;
        const container = this.$refs.commentsContainer;
        if (Math.abs(container.scrollHeight - container.scrollTop - container.clientHeight) <= 3) {
          this.isScrollAtBottom = true;
        }
        if (scrollTop < this.scrollThreshold && !this.isLoadingMore && this.commentsPage < this.commentsPageCount - 1) {
          this.loadMoreComments();
        }
      },
      loadMoreComments() {
        if (this.isLoadingMore) return;
        
        this.isLoadingMore = true;
        this.setCommentsPage(this.commentsPage + 1);
        this.shouldLoadMoreComments = true;
        this.fetchComments();
      },
      fetchComments() {
        const id = this.ticketId;
        this.$store.dispatch('fetchComments', id).then(() => {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        });
      },
      scrollToBottom() {
        if (this.$refs.commentsContainer && !this.shouldLoadMoreComments) {
          this.$refs.commentsContainer.scrollTop = this.$refs.commentsContainer.scrollHeight;
        }
        this.showNewMessageButton = false;
      },
      displayComments() {
        this.commentsVisibility = true;
        if (this.newCommentExists) {
          this.checkNewComment();
        }
        this.$nextTick(() => {
          const isDiffUserComment = Object.keys(this.commentByDifferentUser).length && 
          this.commentByDifferentUser.contributorId !== this.$currentContributorId;
          if (isDiffUserComment) {
            if (!this.isScrollAtBottom) {
              this.showNewMessageButton = true;
            }
          } else if(!this.isCommentUpdated) {
            this.scrollToBottom();
            this.showNewMessageButton = false;
          }
        });
      },
      updateSaveStatus(status) {
        this.commentSaveStatus = status;
        this.toggleShowEditor();
      },
      toggleShowEditor() {
        this.setExpandCommentSection(true);
        this.isReplyResponse = false;
      },
      hideEditor() {
        this.setExpandCommentSection(false);
      },
      openNewCommentModal() {
        this.$emit('open-modal');
      },
      checkNewComment() {
        this.$emit('go-to-comment');
      },
      pageSelected(p) {
        const helpTicketId = _get(this, 'currentHelpTicket.id', null);
        this.setCommentsPage(p - 1);
        this.$store.dispatch('fetchComments', helpTicketId);
      },
      fetchDraftComment() {
        const draft = localStorage.getItem(`user ${this.$currentCompanyUserId}- ticket ${this.currentHelpTicket?.id}`);
        if (draft) {
          this.draftComment = JSON.parse(this.decompressData(draft));
        }
      },
      removeDraft() {
        this.showEditor = true;
        this.draftComment = null;
      },
      commentReply(value) {
        this.isReplyResponse = true;
        this.comment = value;
        this.$store.commit('setHideHeader', false);
      },
      cancelReply() {
        this.showEditor = true;
        this.$store.commit('setHideHeader', false);
        this.comment = {};
      },
      strctureReplyComments() {
        this.allComments.forEach(comment => {
            if (comment.parentCommentId) {
                comment.parentComment = this.allComments.find(c => c.id === comment.parentCommentId);
            }
        });
      },
      fetchOriginatingEmail() {
        http
          .get(`/help_tickets/ticket_emails/${this.currentHelpTicket.id}`)
          .then((res) => {
            this.ticketEmail = res.data.email;
            this.ticketAttachments = res.data.attachments;
            this.ticketEmailContriburorId = res.data.contributorId;
            this.createEmailComment();
            this.commentsVisibility = true;
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error fetching Originating Email (${e.response.data.message}).`);
          });
      },
      createEmailComment() {
        const commentBodies = [
          this.generateFilePreview(this.ticketEmail, this.ticketAttachments),
          this.emailSubjectAndBody(this.ticketEmail.subject, this.ticketEmail.bodyText),
          ...this.ticketAttachments.map(attachment => this.generatePreviewFromComment(attachment.commentBody).commentBody),
        ].filter(body => body);

        const lastIndex = commentBodies.length - 1; 

        this.emailComment = commentBodies.map((body, index) => ({
          commentBody: body,
          createdAt: this.ticketEmail.createdAt,
          email: this.ticketEmail.from,
          itemType: 'originating_email',
          source: 'email',
          isLast: index === lastIndex,
          contributorId: this.ticketEmailContriburorId,
        }));
      },
      processCommentAttachments(comment) {
        const fileUrlRegex = /https?:\/\/[^\s"<>()]+\.[a-zA-Z0-9]{2,5}/g;
        const validDocTypes = ['.doc', '.xls', '.docx', '.xlsx', '.pdf', '.msg', '.txt', '.ppt', '.pptx', '.tiff', '.csv', '.xlsm', '.zip', '.mov', '.mp4'];
        const isValidDoc = (url) => validDocTypes.some(ext => url.toLowerCase().endsWith(ext));

        const body = comment.commentBody || '';
        const urls = body.match(fileUrlRegex);
        const isHtml = body.includes('<!DOCTYPE html') || body.includes('<!doctype html');
        const hasNonImageUrl = urls?.some(url => isValidDoc(url));

        comment.isImg = !hasNonImageUrl && urls?.length;
        if (!isHtml && hasNonImageUrl) {
          comment.oldCommentBody = body;
          comment.commentBody = this.generatePreviewFromComment(body);
          comment.isAttachmentComment = true;
        }

        return comment;
      },
    },
  };
</script>
<style lang="scss" scoped>

.comments-text-banner {
  gap: 10px; 
  border: 1px solid #ccc;
}

.no-underline-on-load {
  text-decoration: none;
  cursor: pointer;
}

.no-underline-on-load:hover {
  text-decoration: underline;
}

.loading-bar {
  font-size: 1.125rem;
  font-weight: bold;
  padding-top: 0.9375rem;
  text-align: center;
}

.hide-loading-bar {
  display: none;
}

.comments-container {
  visibility: hidden;
}

.reply-box-border {
  border-radius: 0.5rem 0.5rem 0 0 !important;
  border-color: #dfdfdf !important;
  background-color: var(--themed-very-light) !important;
  padding: 0.5rem 0.5rem 0 0.5rem !important;
}
.show-section,
.adjust-section {
  overflow-x: hidden;
  overflow-y: auto;
  overscroll-behavior: contain;
}

.new-message-button {
  position: absolute;
  bottom: 5rem;
  position-area: bottom;
}
.margin-comment-box{
  bottom: 12rem;
}
</style>
