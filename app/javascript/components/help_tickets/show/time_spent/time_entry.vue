<template>
  <div class="row">
    <div class="col-auto">
      <span class="action-icon action-icon--lg position-relative align-middle">
        <i
          class="genuicon-clock-o position-absolute"
          style="bottom: -3px"
        />
      </span>
    </div>

    <div class="col pl-0">
      <div data-tc-added-time-spent>
        <span>
          <a
            class="font-weight-semi-bold text-secondary"
            :href="userLink"
            target="_blank"
            data-tc-user-time-spent
            :data-tc-time-entry="username"
          >
            {{ username }}
          </a>
          spent
        </span>

        <strong :data-tc-time-spent="timeSpent.startTime" :data-tc-time1-spent="timeSpent.endTime">
          <time-duration :time-spent="timeSpent" />
        </strong>

        <span>
          <span v-if="timeSpentEntries.length > 1" >
            from
          </span>
          <span v-else>
            on
          </span>

          {{ calculateDates }}
        </span>
        <span>
          <template v-if="actionsEnabled">
            <a
              v-tooltip="'Edit time entry'"
              href="#"
              class="text-muted float-right"
              data-tc-edit-icon
              @click.stop.prevent="toggleEditTimeEntry"
            >
              <i class="nulodgicon-edit mb-0 clickable" />
            </a>
            <a
              v-tooltip="'Delete time entry'"
              href="#"
              class="text-muted float-right"
              data-tc-delete-icon
              @click="openDeleteModal"
            >
              <i class="nulodgicon-trash-b mb-0 clickable mr-3" />
            </a>
          </template>
          <span>
            <a
              v-if="comment && comment.commentBody"
              href="#"
              class="text-muted float-right"
              :data-tc-comment-icon="timeSpent.startTime"
              @click="toggleShowComment"
            >
              <i
                v-tooltip="tooltipText"
                class="genuicon-comment-o mb-0 clickable mr-3"
                :class="{'text-themed-link': showComment}"
              />
            </a>
          </span>
        </span>
      </div>
      <div
        v-if="timeSpent.startTime && timeSpent.endTime"
        class="mt-1"
      >
        <small data-tc-view-start-end-time>
          {{ timeSpent.startTime }} - {{ timeSpent.endTime }}
          <span
            v-if="timeSpent.entries.length > 0"
          >
            ({{ calculateDates }})
          </span>
        </small>
      </div>
      <div
        v-if="comment && showComment"
        class="w-75 bg-lighter p-2 mt-2"
        :data-tc-time-notes="comment.commentBody"
        v-html="comment.commentBody"
      />
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import _get from 'lodash/get';
import { mapGetters } from 'vuex';
import MomentTimezone from 'mixins/moment-timezone';
import permissionsHelper from "mixins/permissions_helper";
import TimeDuration from './time_duration.vue';

export default {
  components: {
    TimeDuration,
  },
  mixins: [MomentTimezone, permissionsHelper],
  props: ['timeSpent', 'comment', 'companyUsers'],
  data() {
    return {
      showComment: false
    }
  },
  computed: {
    ...mapGetters(['companyUserOptions', 'currentHelpTicket', 'currentHelpTicketDraft', 'enableTicketDrafts']),

    actionsEnabled() {
      const companyUserIds = this.companyUsers.map((cu) => cu['companyUserId']);
      return (this.isWrite || this.isScopedAny && companyUserIds.includes(this.timeSpent.companyUserId));
    },
    startedAt() {
      return this.$moment(this.timeSpent.startedAt).format('LL');
    },
    userLink() {
      return `/company/users/${this.timeSpent.companyUserId}`;
    },
    username() {
      return _get(this, 'timeSpent.userName', 'Unknown');
    },
    tooltipText() {
      return this.showComment ? 'Hide Comment' : 'Show Comment';
    },
    timeSpentEntries() {
      return this.timeSpent.entries.map(d => this.$moment(d.startedAt));
    },
    calculateDates() {
      let moments = this.timeSpentEntries;
      const minDate = moment.parseZone(this.$moment.min(moments));
      const maxDate = moment.parseZone(this.$moment.max(moments));
      if (moments.length > 1) {
        this.isMultiDate = true;
        return (`${ (minDate).format("LL") } to ${ (maxDate).format("LL") }`);
      } else if (moments.length > 0) {
        return this.$moment.tz(moments[0], Vue.prototype.$timezone).format("LL");
      }
    },
  },
  mounted() {
    if (this.comment?.id && this.currentHelpTicketDraft && this.enableTicketDrafts &&
        this.currentHelpTicketDraft.companyUserId === this.$currentCompanyUserId &&
        this.currentHelpTicketDraft?.timeSpents[this.comment.id]) {
          this.toggleEditTimeEntry();
        }
  },
  methods: {
    toggleEditTimeEntry() {
      this.$emit('toggle-edit-time-entry', this.timeSpent);
    },
    openDeleteModal() {
      this.$emit('delete-modal-open', this.timeSpent);
    },
    toggleShowComment() {
      this.showComment = !this.showComment;
    },
  },
};
</script>

<style lang="scss" scoped>
.action-icon {
  background-color: $teal;
}
</style>
