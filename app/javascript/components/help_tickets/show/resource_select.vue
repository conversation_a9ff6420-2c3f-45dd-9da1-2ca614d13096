<template>
  <Teleport to="body">
    <sweet-modal
      ref="modal"
      title="Resources"
      modal-theme="dark-header theme-right theme-sticky-footer"
      width="50%"
      @close="$emit('close')"
    >
      <div class="subpage-menu my-4 px-1">
        <div 
          v-for="tab in tabs" 
          :key="tab.name" 
          class="tab" 
          :class="{ active: activeTab === tab.name }"
          @click="handleTabClick(tab.name)"
        >
          {{ tab.label }}
        </div>
      </div>
      <div>
        <div class="search-wrap d-flex">
          <div class="w-100">
            <input
              ref="search"
              v-model="searchText"
              type="text"
              class="form-control search-input mb-4"
              :placeholder="getPlaceholder"
              name="search"
              @keyup="onSearch"
            >
            <i
              class="nulodgicon-ios-search-strong search-input-icon resource-search-icon"
              @click.prevent="$refs.search.focus"
            />
          </div>
          <nav
            v-if="pageCount > 1"
            class="mt-2"
          >
            <paginate
              ref="paginate"
              class="px-2 justify-content-center"
              :click-handler="handlePageClick"
              :container-class="'pagination pagination-sm'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="currentPage"
            />
          </nav>
        </div>
        <div
          v-if="isLoadingResources"
          class="pt-5 d-flex justify-content-center align-items-center"
        >
          <h6 class="mb-0">Loading {{ currentResource }}</h6>
          <pulse-loader
            :loading="true"
            class="ml-3 mt-1"
            color="#0d6efd"
            size="0.5rem"
          />
        </div>
        <div v-if="!isLoadingResources && activeResources && activeResources.length">
          <div
            v-for="resource in activeResources"
            :key="resource.id"
            class="box box--with-heading box--flat mb-3"
          >
            <div
              v-tooltip="getTooltipText(resource)"
              class="box__heading row w-100 justify-content-between align-items-center clickable selectable-option text-themed-dark bg-themed-lighter"
              @click="selectedResource = selectedResource === resource ? null : resource"
            >
              <div class="col task-group-info align-content-center px-0 d-inline-flex d-flex justify-content-between">
                <div>
                  <span
                    v-if="activeTab === 'faq'"
                    class="article-title font-weight-semi-bold base-font-size"
                    v-html="truncate(sanitizeHTML(resource.questionBody), 60)"
                  />
                  <span
                    v-else
                    class="article-title font-weight-semi-bold base-font-size"
                    v-html="truncate(sanitizeHTML(resource.title), 60)"
                  />
                </div>
                <div class="d-flex align-items-center">
                  <div class="d-flex align-items-center mr-3">
                    <div
                      v-if="getCategoryName(resource)"
                      class="d-flex align-items-center"
                    >
                      <span
                        class="badge badge-secondary ml-2 px-1 pt-1"
                        :data-tc-view-category="getCategoryName(resource)"
                      >
                        {{ getCategoryName(resource) }}
                      </span>
                    </div>
                  </div>
                  <span
                    v-if="activeTab !== 'response'"
                    class="px-2 visibility-status d-flex flex-start"
                  >
                    {{ resource.public ? "Public" : "Private" }}
                  </span>
                </div>
              </div>
              
              <div class="col-2 d-inline-flex p-0 justify-content-end align-items-center">
                <span 
                  v-tooltip="getTooltip"
                  class="mx-1 btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm text-secondary"
                  @click.stop="copyResource(resource)" 
                >
                  <i class="nulodgicon-link" />
                  <span 
                    v-if="copiedResourceId === resource.id" 
                    class="copied base-font-size"
                  >
                    Copied!
                  </span>
                </span>
                <a
                  v-if="activeTab !== 'response'"
                  v-tooltip="getUrlTooltip"
                  :href="getUrl(resource)"
                  target="_blank"
                  class="mx-1 btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm text-secondary"
                >
                  <i class="nulodgicon-external-link" />
                </a>

                <div class="mx-1 basic-transition header__arrow-wrap btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm text-secondary">
                  <i
                    class="mb-0"
                    :class="selectedResource === resource ? 'nulodgicon-chevron-up' : 'nulodgicon-chevron-down'" 
                  />
                </div>
              </div>
            </div>

            <div 
              v-if="selectedResource === resource"
              class="box__inner "
            >
              <div
                v-if="activeTab !== 'faq'"
                class="d-flex"
              >
                <div class="d-flex align-items-center">
                  <div  v-if="activeTab === 'knowledge'">
                    <avatar
                      v-tooltip="resource.authorFullName"
                      class="d-inline-block align-middle mr-1 logo-outline"
                      :size="30"
                      :src="resource.avatarThumbUrl"
                      :username="resource.authorFullName"
                    />
                    <span
                      v-if="getLocationName(resource)"
                    >
                      <span class="px-2">•</span>
                      <i class="nulodgicon-location h6"/>
                      {{ getLocationName(resource) }}
                    </span>
                    <span class="px-2">•</span>
                  </div>
                  Last Updated {{ getRelativeTime(resource.updatedAt) }}
                </div>
              </div>
              <hr
                v-if="activeTab !== 'faq'"
                class="separator-line"
              >
              <p
                v-if="resource.body"
                v-html="resource.body"
              />
              <p
                v-else-if="resource.description"
                v-html="sanitizeHTML(resource.description)"
              />
              <p
                v-else
                v-html="sanitizeHTML(resource.answerBody)"
              />
            </div>
          </div>
        </div>
        <div
          v-else-if="!isLoadingResources"
          class="h6 text-muted mt-4 text-center"
        >
          {{ getNoResourceMessage }}
        </div>
      </div>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import search from 'mixins/search';
  import strings from 'mixins/string';
  import { SweetModal } from 'sweet-modal-vue';
  import Paginate from 'vuejs-paginate';
  import _ from 'lodash';
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import http from 'common/http';
  import { Avatar } from 'vue-avatar';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from '../../../mixins/permissions_helper';

  export default {
    components: {
      SweetModal,
      Paginate,
      Avatar,
      PulseLoader,
    },
    mixins: [ strings, search, permissionsHelper ],
    data() {
      return {
        copiedResourceId: null,
        searchText: '',
        activeTab: 'knowledge',
        tabs: [
          { name: 'knowledge', label: 'Knowledge Base' },
          { name: 'response', label: 'Canned Responses' },
          { name: 'faq', label: 'FAQs' },
        ],
        resources: [],
        responses: [],
        responseCount: 0,
        perPage: 10,
        responsePageCount: 0,
        responsePage: 0,
        selectedResource: null,
        showResultCounts: false,
      };
    },
    computed: {
      ...mapGetters([
        'articles',
        'articlesPageCount',
        'articlesPage',
        'loadingStatus',
        'faqs',
        'faqsPageCount',
        'faqsPage',
        'categories',
        'articleLoadingStatus',
      ]),
      pageCount() {
        switch (this.activeTab) {
          case 'knowledge':
            return this.articlesPageCount;
          case 'response':
            return this.responsePageCount;
          case 'faq':
            return this.faqsPageCount;
          default:
            return 0;
        }
      },
      currentPage() {
        switch (this.activeTab) {
          case 'response':
            return this.responsePage;
          case 'faq':
            return this.faqsPage;
          default:
            return this.articlesPage;
        }
      },
      getPlaceholder() {
        switch (this.activeTab) {
          case 'response':
            return 'Search responses...';
          case 'faq':
            return 'Search faqs...';
          default:
            return 'Search articles...';
        }
      },
      getNoResourceMessage() {
        switch (this.activeTab) {
          case 'response':
            return 'No response found. Please modify your search.';
          case 'faq':
            return 'No question found. Please modify your search.';
          default:
            return 'No article found. Please modify your search.';
        }
      },
      getTooltip() {
        switch (this.activeTab) {
          case 'response':
            return 'Copy Text';
          default:
            return 'Copy Link';
        }
      },
      getUrlTooltip() {
        switch (this.activeTab) {
          case 'faq':
            return 'Open Faq';
          default:
            return 'Open Article';
        }
      },
      activeResources() {
        switch (this.activeTab) {
          case 'response':
            return this.responses;
          case 'faq':
            return this.faqs;
          default:
            return this.articles;
        }
      },
      isLoadingResources() {
        return this.loadingStatus || this.articleLoadingStatus;
      },
      currentResource() {
        switch (this.activeTab) {
          case 'response':
            return 'Responses';
          case 'faq':
            return "FAQ's";
          default:
            return "Articles";
        }
      },
    },
    methods: {
      ...mapMutations(['setArticlesPage', 'setFaqsPage', 'setLoadingStatus']),
      ...mapActions([
        'fetchArticles',
        'fetchFaqs',
        'fetchCategories',
      ]),
      onWorkspaceChange() {
        this.fetchSnippets();
      },
      open() {
        this.fetchArticles();
        this.$refs.modal.open();
      },
      close() {
        this.$refs.modal.close();
      },
      handlePageClick(p) {
        switch (this.activeTab) {
          case 'response':
            this.responsePage = p - 1;
            this.fetchSnippets();
            break;
          case 'faq':
            this.setFaqsPage(p - 1);
            this.fetchFaqs({ searchTerms: this.searchText });
            break;
          default:
            this.setArticlesPage(p - 1);
            this.fetchArticles({ search: this.searchText });
            break;
        }
      },
      onSearch: _.debounce(
        function () {
          switch (this.activeTab) {
            case 'response':
              this.responsePage = 0;
              this.fetchSnippets({ search: this.searchText });
              break;
            case 'faq':
              this.setFaqsPage(0);
              this.fetchFaqs({ searchTerms: this.searchText });
              break;
            default:
              this.setArticlesPage(0);
              this.fetchArticles({ search: this.searchText });
          }
        }, 
        500
      ),
      fetchSnippets() {
        this.setLoadingStatus(true);
        const url = '/snippets.json';
        http
          .get(url, { params: {search: this.searchText, per_page: this.perPage, page: this.responsePage + 1, index_page: true } })
          .then(res => {
            this.responses = res.data.snippets;
            this.responsePageCount = res.data.pageCount;
            this.setLoadingStatus(false);
          }).catch(error => {
            this.setLoadingStatus(false);
            this.emitError(`Sorry, there was an error fetching Responses. ${error.response.data.message}`);
          });
      },
      copyResource(resource) { 
        let resourceData = null;
        if (resource.slug) {
          resourceData = JSON.stringify({ slug: resource.slug, title: resource.title});        
        } else if (resource.description) {
          resourceData = JSON.stringify({ title: resource.description });
        } else if (resource.questionBody) {
          resourceData = JSON.stringify({ title: this.sanitizeHTML(resource.questionBody), id: resource.id });
        }
        navigator.clipboard.writeText(resourceData).then(() => {
            this.copiedResourceId = resource.id;
            setTimeout(() => {
              this.copiedArticleId = null;
            }, 1000);
          });
      },
      getRelativeTime(updatedAt) {
        if (!updatedAt) return "Unknown";

        const updatedDate = new Date(updatedAt);
        const now = new Date();
        const diffInSeconds = Math.floor((now - updatedDate) / 1000);

        const timeFormats = [
          { unit: "year", seconds: 31536000 },
          { unit: "month", seconds: 2592000 },
          { unit: "week", seconds: 604800 },
          { unit: "day", seconds: 86400 },
          { unit: "hour", seconds: 3600 },
          { unit: "minute", seconds: 60 },
        ];

        const format = timeFormats.find(({ seconds }) => diffInSeconds >= seconds);
        if (format) {
          const interval = Math.floor(diffInSeconds / format.seconds);
          return `${interval} ${format.unit}${interval > 1 ? "s" : ""} ago`;
        }
        return "Just now";
      },
      getUrl(resource) {
        const common = `${window.location.origin}/help_tickets/`;
        let url;
        if (resource.slug) {
          url = `${common}articles/${resource.slug}`;
        } else {
          url = `${common}faqs/${resource.id}`;
        }
        return url;
      },
      getTooltipText(resource) {
        switch(this.activeTab) {
          case 'faq':
            return  this.getTooltipContent(this.sanitizeHTML(resource.questionBody));
          default: 
            return this.getTooltipContent(resource.title);
        }
      },
      getTooltipContent(content) {
        return content && content.length > 60 ? content : null;
      },
      getCategoryName(resource) {
        if (this.activeTab === 'faq') {
          return resource.categoryName;
        } else if (this.activeTab === 'knowledge') {
          return resource.category;
        }
        return null;
      },
      getLocationName(resource) {
        return resource.location ? resource.location.name : '';
      },
      handleTabClick(tabName) {
        this.activeTab = tabName;
        switch (tabName) {
          case 'faq':
            this.fetchFaqs();
            break;
          case 'response':
            this.fetchSnippets();
            break;
          default:
            this.fetchArticles();
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .resource-search-icon{
    top: 35% !important;
  }
  .tab{
    border: none;
    border-radius: 20rem;
    color: var(--themed-dark);
    display: inline-block;
    margin: 0.1875rem;
    margin-left: 0;
    padding: 0 1rem;
    transition: all 0.1s ease-in-out;
    cursor: pointer;
  }
  .tab:hover {
    background-color: var(--themed-light-hover-bg);
    color: var(--themed-link);
  }
  .active {
    border: none;
    color: var(--themed-helpdesk-dark);
    background: var(--themed-helpdesk-light);
  }
  .copied {
    position: absolute;
    transform: translateX(-85%) translateY(-2rem);
    background-color: #0c0c0cc4;
    color: #fff;
    padding: 0.25rem 0.5rem;
    border-radius: 0.313rem;
    white-space: nowrap;
    opacity: 1;
    pointer-events: none;
    transition: opacity 1s ease-in-out;
    z-index: 9999;
  }
  .article-title {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  .separator-line {
    border: none;
    border-top: 0.093rem solid lightgray;
    margin: 1rem 0 2rem 0;
  }

  .box--with-heading {
    &:hover {
      .header__arrow-wrap {
        background-color: var(--themed-light-hover-bg) !important;
        color: $themed-secondary !important;
      }
    }
  }
  .visibility-status {
    width: 3rem;
  }
</style>
