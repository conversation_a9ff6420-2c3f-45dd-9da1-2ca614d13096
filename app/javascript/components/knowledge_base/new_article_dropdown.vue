<template>
  <div
    v-click-outside="onClickOutside"
    class="position-relative"
  >
    <a class="btn-group p-0">
      <button
        type="button"
        class="btn btn-primary px-3"
        @click.prevent.stop="openModal('form')"
      >
        <i class="nulodgicon-plus-round mr-2" />
        Add Article
      </button>
      <button
        id="dropdown_button"
        class="btn btn-primary-dark pl-1"
        type="button"
        @click.prevent.stop="toggleDropdown"
      >
        <i class="dropdown-toggle ml-2 arrow-down d-block" />
      </button>
    </a>

    <div
      v-if="showDropdown"
      class="dropdown-menu right-0 show"
    >
      <div
        class="dropdown-item cursor-pointer"
        @click.prevent.stop="openModal('form')"
      >
        Add Article Manually
      </div>
      <div
        class="dropdown-item cursor-pointer"
        @click.prevent.stop="openModal('importDocx')"
      >
        Microsoft Word Document
      </div>
      <div
        v-if="displayPDFImport"
        class="dropdown-item cursor-pointer"
        @click.prevent.stop="openModal('importPDF')"
      >
        PDF Document
      </div>
      <div
        v-if="displayDriveIntg"
        class="dropdown-item cursor-pointer"
        @click.prevent.stop="openModal('googleDrive')"
      >
        Import from Google Drive
      </div>
      <div
        v-if="displayOneDriveIntg"
        class="dropdown-item cursor-pointer"
        @click.prevent.stop="openModal('oneDrive')"
      >
        Import from One Drive
      </div>
    </div>
    <sweet-modal
      ref="attachmentModal"
      v-sweet-esc
      blocking
      :title="attachmentModalTitle"
    >
      <template slot="default">
        <attachment-input
          :key="isImportingPDF"
          :show-label="true"
          :specific-types="allowedFileTypes"
          :max-file-size="10"
          @input="addFiles"
        />
      </template>
    </sweet-modal>
    <importing-modal ref="fileImport"/>
    <google-drive-info-modal
      ref="driveInfoModal"
      @sync="getConsent"
    />
    <div v-if="displayDriveIntg && openFilePicker">
      <google-file-picker
        :key="key"
        :config="driveConfig"
        @input="addFiles"
        @set-loading="openLoadingModal"
        @reload="key += 1"
      />
    </div>
    <one-drive-file-picker
      v-if="displayOneDriveIntg"
      ref="oneDrivePicker"
      @set-loading="openLoadingModal"
      @input="addFiles"
    />
  </div>
</template>

<script>
  import http from 'common/http';
  import vClickOutside from 'v-click-outside';
  import { SweetModal } from 'sweet-modal-vue';
  import { mapMutations, mapGetters } from 'vuex';
  import mammoth from "mammoth";
  import permissionsHelper from 'mixins/permissions_helper';
  import isEmpty from 'lodash/isEmpty';
  import attachmentInput from '../shared/attachment_input.vue';
  import googleFilePicker from './google_file_picker.vue';
  import googleDriveInfoModal from './google_drive_info_modal.vue';
  import oneDriveFilePicker from './one_drive_file_picker.vue';
  import importingModal from './import_loader_modal.vue';

  export default {
    components: {
      SweetModal,
      attachmentInput,
      googleFilePicker,
      googleDriveInfoModal,
      oneDriveFilePicker,
      importingModal,
    },
    directives: {
      clickOutside: vClickOutside.directive,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        showDropdown: false,
        importFileSystem: false,
        displayDriveIntg: false,
        displayOneDriveIntg: false,
        displayPDFImport: false,
        key: 0,
        driveConfig: {
          developerKey: '',
          accessToken: '',
          clientId: '',
        },
        oneDriveClientId: null,
        openFilePicker: false,
        attachmentModalTitle: 'Upload Microsoft Word Document',
        isImportingPDF: false,
      };
    },
    computed: {
      ...mapGetters(['importingArticle']),
      allowedFileTypes() {
        return this.isImportingPDF ? ['.pdf'] : ['.docx'];
      },
    },
    watch: {
      'importingArticle': {
        handler() {
          this.importingArticle ? this.$refs.fileImport.open() : this.$refs.fileImport.close();
        },
      },
    },
    methods: {
      ...mapMutations(['setFileContent', 'setImportingArticle', 'setDisableArticles']),
      onWorkspaceChange() {
        this.gDriveImportStatus();
        this.oneDriveImportStatus();
        this.pdfImportStatus();
        if (this.$route.query.showModal) {
          this.setDisableArticles(true);
          const query = { ...this.$route.query };
          delete query.showModal;
          this.$router.replace({ query });

          this.fetchCredentials();
        }
      },
      pdfImportStatus() {
        http
          .get('/pdf_import_status')
          .then((res) => {
            this.displayPDFImport = res.data.displayStatus;
          });
      },
      gDriveImportStatus() {
        http
          .get('/integrations/google_drive/should_display_drive_import')
          .then((res) => {
            this.displayDriveIntg = res.data.displayStatus;
          });
      },
      oneDriveImportStatus() {
        http
          .get('/integrations/one_drive/should_display_drive_import')
          .then((res) => {
            this.displayOneDriveIntg = res.data.displayStatus;
          });
      },
      fetchCredentials() {
        http
          .get('/integrations/google_drive/credentials')
          .then((res) => {
            if (isEmpty(res.data)) {
              this.setDisableArticles(false);
              return;
            }

            this.driveConfig = {
              developerKey: res.data.developerKey,
              accessToken: res.data.token,
              clientId: res.data.clientId,
            };
            this.openFilePicker = true;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error while fetching files of your Google Drive. Please try again later.');
          });
      },
      validateFileSize(file) {
        return file.size > 10000000;
      },
      addFiles(file, validateSize = true) {
        if (validateSize && this.validateFileSize(file)) {
          this.stopImportLoading();
          this.key += 1;
          this.emitError('Sorry, that file is too large. Please upload file under 10MB.');
          return;
        }
        if (file.type === 'application/pdf') {
          this.convertToDocx(file);
        } else {
          this.setImportingArticle(true);
          this.extractDocxContent(file, validateSize);
        }
        this.$refs.attachmentModal.close();
      },
      convertToDocx(file) {
        this.setImportingArticle(true);
        const form = new FormData();
        form.append('file', file, file.name);
        http
          .post('https://8q5qsgnkjf.execute-api.us-west-2.amazonaws.com/prod/convert-pdf/', form, { responseType: 'blob' })
          .then(async (response) => {
            const blob = response.data;
            const newFile = new File([blob], file.name, { type: blob.type });
            this.addFiles(newFile, false);
          })
          .catch((err) => {
            this.handleError(err);
          });
      },
      stopImportLoading() {
        this.setDisableArticles(false);
        this.setImportingArticle(false);
      },
      openModal(type) {
        this.importFileSystem = false;
        switch (type) {
          case 'form':
            this.$router.push('/articles/actions/new');
            break;

          case 'importDocx':
            this.isImportingPDF = false;
            this.attachmentModalTitle = 'Upload Microsoft Word Document';
            this.importFileSystem = true;
            this.$refs.attachmentModal.open();
            break;

          case 'googleDrive':
            this.$refs.driveInfoModal.open();
            break;

          case 'oneDrive':
            this.$refs.oneDrivePicker.open();
            break;

          case 'importPDF':
            this.isImportingPDF = true;
            this.attachmentModalTitle = 'Upload PDF Document';
            this.importFileSystem = true;
            this.$refs.attachmentModal.open();
            break;

          default:
            this.$router.push('/articles/actions/new');
        }
        this.onClickOutside();
      },
      async extractDocxContent(file, validateSize = true) {
        const reader = new FileReader();
        reader.onload = async (event) => {
          try {
            const arrayBuffer = event.target.result;
            const result = await mammoth.convertToHtml({
              arrayBuffer,
              ignoreEmptyParagraphs: false,
              includeDefaultStyleMap: false,
            });
            if (validateSize && this.getSizeInMB(result.value) > 10) {
              this.stopImportLoading();
              this.emitError('Sorry, that file is too large. Please upload file under 10MB.');
              if (this.importFileSystem) {
                this.$refs.attachmentModal.open();
              }
              return;
            }
            this.setFileContent({ name: file.name, data: result.value });
            this.$router.push(`/articles/actions/new`);
            this.setDisableArticles(false);
          } catch (error) {
            this.handleError(error);
          }
        };
        reader.readAsArrayBuffer(file);
      },
      handleError(error) {
        this.stopImportLoading();
        this.setDisableArticles(false);
        if (error?.message?.startsWith("Can't find end of central directory")) {
          this.emitError("Sorry, we couldn't process your document due to compatibility issues. Please try importing a different document.");
          if (this.importFileSystem) {
            setTimeout(() => {
              this.$refs.attachmentModal.open();
            }, 200);
          }
        } else {
          this.emitError('Sorry, there was an error while importing the document.');
          throw error;
        }
      },
      toggleDropdown() {
        this.showDropdown = !this.showDropdown;
      },
      onClickOutside() {
        this.showDropdown = false;
      },
      openLoadingModal(loading) {
        loading ? this.setImportingArticle(true) : this.setImportingArticle(false);
      },
      getConsent() {
        window.location.href = '/integrations/google_drive/authorize';
      },
      getSizeInMB(text) {
        const sizeInBytes = new TextEncoder().encode(text).length;
        return sizeInBytes / (1024 * 1024);
      },
    },
  };

</script>

<style lang="scss" scoped>
  .right-0 {
    right: 0;
    left: auto;
  }
</style>
