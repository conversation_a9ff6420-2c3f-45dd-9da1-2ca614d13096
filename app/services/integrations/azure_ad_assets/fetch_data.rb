# frozen_string_literal: true

class Integrations::AzureAdAssets::FetchData
  include ReadReplicaDb

  attr_accessor :error

  AZURE_AD_ASSETS_HOST = 'https://graph.microsoft.com/v1.0'
  AZURE_AD_ASSETS_AUTHORIZATION_URI = 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=consent'
  AZURE_AD_ASSETS_SCOPE = 'offline_access Device.Read.All'
  AZURE_AD_ASSETS_CALLBACK = "#{Rails.application.credentials.domain_with_port}/integrations/azure_ad_assets/oauth2callback"
  AZURE_AD_ASSETS_TOKEN_URI = 'https://login.microsoftonline.com/common/oauth2/v2.0/token'

  def initialize(company_id)
    @company_id = company_id
    set_read_replica_db do
      @config ||= Company.find_by_cache(id: @company_id)&.azure_ad_assets_config
      @intg_id = Integration.find_by_name('azure_ad_assets').id
    end
    @api_logs = []
    @requests_count = 0
  end

  def client
    client = Signet::OAuth2::Client.new(
      authorization_uri: AZURE_AD_ASSETS_AUTHORIZATION_URI,
      client_id:  Rails.application.credentials.azure_ad_assets[:client_id],
      response_type: 'code',
      response_mode: 'query',
      scope: AZURE_AD_ASSETS_SCOPE,
      redirect_uri: AZURE_AD_ASSETS_CALLBACK,
      state: @company_id
    )
  end

  def token(code)
    body = {
      grant_type: 'authorization_code',
      code: code,
      client_id:  Rails.application.credentials.azure_ad_assets[:client_id],
      client_secret:  Rails.application.credentials.azure_ad_assets[:cliend_secret],
      scope: AZURE_AD_ASSETS_SCOPE,
      redirect_uri: AZURE_AD_ASSETS_CALLBACK,
     }
    headers = {'Content-Type': "application/x-www-form-urlencoded"}
    detail_params = { code: code }
    response = HTTParty.get(AZURE_AD_ASSETS_TOKEN_URI, headers: headers, body: body)
    log_event(response, "token", detail_params)
    response.parsed_response
  end

  def refresh_token
    body = {
      grant_type: 'refresh_token',
      client_id:  Rails.application.credentials.azure_ad_assets[:client_id],
      client_secret:  Rails.application.credentials.azure_ad_assets[:cliend_secret],
      tenant:  Rails.application.credentials.azure_ad_assets[:tenant],
      scope: AZURE_AD_ASSETS_SCOPE,
      refresh_token: @config.refresh_token,
      redirect_uri: AZURE_AD_ASSETS_CALLBACK,
    }
    headers = {'Content-Type': "application/x-www-form-urlencoded"}
    response = HTTParty.get(AZURE_AD_ASSETS_TOKEN_URI, headers: headers, body: body)
    log_event(response, "refresh_token")
    response.parsed_response
  end

  def get_devices(next_page_token = nil)
    url = "/devices?$top=100#{next_page_token}"
    make_api_call(url, "get_devices")
  end

  def get_registered_owner(device_id)
    url = "/devices/#{device_id}/registeredOwners"
    make_api_call(url, "get_registered_owner")
  end

  def get_registered_user(device_id)
    url = "/devices/#{device_id}/registeredUsers"
    make_api_call(url, "get_registered_user")
  end

  def check_token_expiry
    # We've done this to avoid access token expiration
    if @requests_count == 30
      response = refresh_token
      if response && response["access_token"] && response["refresh_token"]
        expiry_time = Time.now + (response["expires_in"] - 600)
        @config.update!(
          token: response['access_token'],
          expires_in: expiry_time,
          refresh_token: response['refresh_token'],
          skip_callbacks: true)

        @config.reload
      end
      @requests_count = 0
    end
    @requests_count += 1
  end

  def make_api_call(endpoint, api_type)
    check_token_expiry
    detail_params = { endpoint: endpoint, api_type: api_type }
    headers = {
      Authorization: "Bearer #{@config.token}",
      'Content-Type': 'application/json'
    }
    response = HTTParty.get("#{AZURE_AD_ASSETS_HOST}#{endpoint}", headers: headers)
    if response.present? && response.parsed_response['error']
      self.error = response.parsed_response['error']['message']
    end
    log_event(response, api_type, detail_params)
    response
  end

  def log_event(response, api_type, fur_detail={}, excep = nil)
    status = response.present? && response.code == 200 ? :success : :error
    api_response = {code: response.code, message: response.message, body: response.body}.to_s if response
    log_params = {
      api_type: api_type,
      class_name: self.class,
      integration_id: @intg_id,
      company_id:  @company_id,
      status: status,
      detail: details.merge(fur_detail || {}),
      activity: :action,
      response: api_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail:  excep.present? ? excep.backtrace : nil,
      error_message: excep.present? ? excep.message : nil }
    @api_logs << log_params
    if Rails.env.test?
      Logs::ApiEvent.create(log_params)
    else
      LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
    end
  end

  def ok?
    self.error.blank?
  end

  def details
    @config ? { token: @config.token, refresh_token: @config.refresh_token, expires_in: @config.expires_in } : {}
  end
end
