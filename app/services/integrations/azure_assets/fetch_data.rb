# frozen_string_literal: true

class Integrations::AzureAssets::FetchData
  include Utilities::Domains
  include ReadReplicaDb

  attr_accessor  :token, :client, :api_logs, :errors

  AZURE_HOST = 'https://management.azure.com'
  AZURE_AUTHORIZATION_URI = 'https://login.microsoftonline.com/common/oauth2/authorize?resource=https://management.core.windows.net/'

  def initialize(company_id = nil, first_time = true)
    @company_id = company_id
    set_read_replica_db do
      @config ||= Company.find_cache(id: @company_id).azure_assets_config
      @intg_id = Integration.find_by_name("azure_assets").id
    end
    @api_logs = []
    @first_time = first_time
    @details = {}
    @vm_sizes = []
    @vm_locations = []
    @errors = []
  end

  def client
    client = Signet::OAuth2::Client.new(
      authorization_uri: AZURE_AUTHORIZATION_URI,
      client_id: Rails.application.credentials.microsoft[:client_id],
      response_type: "code",
      response_mode: "query",
      redirect_uri: callback_url,
      state: @company_id) #Following OAuth convention. params[:state] is acting as company_id here.
  end

  def make_api_call(endpoint)
    headers = {
      Authorization: "Bearer #{@config.token}",
      'Content-Type': 'application/json'
    }
    response = HTTParty.get("#{AZURE_HOST}#{endpoint}", headers: headers)
    raise Exception.new(response: response) if response.present? && !response.success?
    response
  end

  def token(code)
    response = token_call(code)
    log_event(set_event_params('get_token', response, {code: code}))
    response
  rescue Exception => e
    log_event(set_event_params('get_token', nil, {code: code}, error_messages_detail(e)))
    raise e
  end

  def refresh_token
    response = refresh_token_call
    log_event(set_event_params('get_refresh_token', response))
    response
  rescue Exception => e
    log_event(set_event_params('get_refresh_token', nil, {}, error_messages_detail(e)))
    raise e
  end

  def get_subscriptions
    response = get_subscriptions_call
    log_event(set_event_params('get_subscriptions', response.body))
    response.parsed_response['value']
  rescue Exception => e
    log_event(set_event_params('get_subscriptions', nil, {}, error_messages_detail(e)))
    raise e
  end

  def get_virtual_machines(subscriptions_id)
    response = get_virtual_machines_call(subscriptions_id)
    log_event(set_event_params('get_virtual_machines', response.body))
    response.parsed_response.deep_symbolize_keys[:value]
  rescue Exception => e
    log_event(set_event_params('get_virtual_machines', nil, {}, error_messages_detail(e)))
    @errors << e.message
    []
  end

  def log_event(response={})
    if Rails.env.test?
      Logs::ApiEvent.create(response)
    else
      LogCreationWorker.perform_async('Logs::ApiEvent', response.to_json)
    end
  end

  def get_network_info(machine)
    net_interfaces = []
    machine[:properties][:networkProfile][:networkInterfaces].each do |net_interface_info|
      network_interface_url = "#{net_interface_info[:id]}?api-version=2021-02-01"
      info = get_network_interface(network_interface_url)
      net_interfaces << info if info.present?
    end

    net_interfaces.each do |net_interface|
      net_interface[:properties][:ipConfigurations].each do |conf|
        conf[:publicIPAddress] = get_public_ip(conf[:properties][:publicIPAddress]) if conf[:properties][:publicIPAddress].present?
      end
    end
    net_interfaces
  rescue Exception => e
    log_event(set_event_params('get_network_info', nil, {}, error_messages_detail(e)))
    []
  end

  def get_network_interface(network_interface_url)
    response = make_api_call(network_interface_url)
    log_event(set_event_params('get_network_interface', response.body))
    response.parsed_response.deep_symbolize_keys
  rescue Exception => e
    log_event(set_event_params('get_network_interface', nil, {}, error_messages_detail(e)))
    nil
  end

  def find_vm_size(machine)
    vm_size_name = machine[:properties][:hardwareProfile][:vmSize]
    size = @vm_sizes.detect { |vms| vms[:name] == vm_size_name }
    return size if size.present?
    @vm_sizes += get_vm_sizes(machine)
    @vm_sizes.detect { |vms| vms[:name] == vm_size_name }
  end

  def find_vm_location(machine)
    vm_location_name = machine[:location]
    location = @vm_locations.detect { |loc| loc[:name] == vm_location_name }
    return location if location.present?
    @vm_locations += get_vm_locations(machine)
    @vm_locations.detect { |loc| loc[:name] == vm_location_name }
  end

  def get_disk_info(machine)
    disk_info_url = "#{machine[:properties][:storageProfile][:osDisk][:managedDisk][:id]}?api-version=2021-04-01"
    response = make_api_call(disk_info_url)
    log_event(set_event_params('get_disk_info', response.body))
    return response.parsed_response.deep_symbolize_keys[:properties]
  rescue Exception => e
    log_event(set_event_params('get_disk_info', nil, {}, error_messages_detail(e)))
    {}
  end

  def get_instance_view(machine)
    instance_view_url = "#{machine[:id]}/InstanceView?api-version=2021-04-01"
    response = make_api_call(instance_view_url)
    log_event(set_event_params('get_instance_view', response.body))
    return response.parsed_response.deep_symbolize_keys
  rescue Exception => e
    log_event(set_event_params('get_instance_view', nil, {}, error_messages_detail(e)))
    nil
  end

  private

  def token_call(code)
    body = {
      grant_type: 'authorization_code',
      code: code,
      client_id: Rails.application.credentials.microsoft[:client_id],
      client_secret: Rails.application.credentials.microsoft[:client_secret],
      redirect_uri: callback_url,
     }
    headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
    response = HTTParty.post("https://login.microsoftonline.com/common/oauth2/token", headers: headers, body: body)
    response.parsed_response
  end

  def refresh_token_call
    body = {
        grant_type: 'refresh_token',
        client_id: Rails.application.credentials.microsoft[:client_id],
        client_secret: Rails.application.credentials.microsoft[:client_secret],
        tenant: Rails.application.credentials.microsoft[:tenant],
        refresh_token: @config.refresh_token,
        redirect_uri: callback_url,
      }
    headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
    response = HTTParty.post("https://login.microsoftonline.com/common/oauth2/token", headers: headers, body: body)

    if response.code == 400
      raise_error(response)
    end

    response.parsed_response
  end

  def raise_error(response)
    res = response.parsed_response
    if !@first_time && res["error"] == "invalid_grant"
      raise Exception.new("invalid_grant")
    end
  end

  def get_subscriptions_call
    url = '/subscriptions?api-version=2019-06-01'
    make_api_call(url)
  end

  def get_virtual_machines_call(subscriptions_id)
    url = "/subscriptions/#{subscriptions_id}/providers/Microsoft.Compute/virtualMachines?api-version=2021-03-01"
    make_api_call(url)
  end

  def get_vm_sizes(machine)
    list_size_url = "#{machine[:id]}/vmSizes?api-version=2021-03-01"
    response = make_api_call(list_size_url)
    log_event(set_event_params('get_vm_sizes', response.body))
    response.parsed_response.deep_symbolize_keys[:value]
  rescue Exception => e
    log_event(set_event_params('get_vm_sizes', nil, {}, error_messages_detail(e)))
    []
  end

  def get_vm_locations(machine)
    subs_id = machine[:id].split('/').third
    locations_list_url = "/subscriptions/#{subs_id}/locations?api-version=2020-01-01"
    response = make_api_call(locations_list_url)
    log_event(set_event_params('get_vm_locations', response.body))
    response.parsed_response.deep_symbolize_keys[:value]
  rescue Exception => e
    log_event(set_event_params('get_vm_locations', nil, {}, error_messages_detail(e)))
    []
  end

  def get_public_ip(ip_config)
    public_ip_url = "#{ip_config[:id]}?api-version=2021-02-01"
    response = make_api_call(public_ip_url)
    log_event(set_event_params('get_public_ip', response.body))
    response.parsed_response.deep_symbolize_keys
  rescue Exception => e
    log_event(set_event_params('get_public_ip', nil, {}, error_messages_detail(e)))
    nil
  end

  def event_params(args={})
    params = {
      company_id: @company_id ,
      status: args[:error_messages] ? :error : :success,
      error_detail: args[:error_messages] ? args[:error_messages][:error_detail] : nil,
      class_name: self.class.name,
      integration_id: @intg_id,
      activity: :action,
      api_type: args[:api_type],
      error_message: args[:error_messages] ? args[:error_messages][:error_message] : nil,
      detail: args[:detail],
      response: args[:response],
      created_at: DateTime.now
    }
    @api_logs << params
    params
  end

  def set_event_params(api_type, response=nil, add_detail={}, error_messages=nil)
    params = {
      response: response,
      api_type: api_type,
      detail: @details.merge(add_detail || {})
    }
    params = params.merge({error_messages: error_messages}) if error_messages
    event_params(params)
  end

  def error_messages_detail(error)
    {
      error_message: error.message,
      error_detail: error.backtrace.join("\n")
    }
  end

  def callback_url
    "#{Rails.application.credentials.domain_with_port}/integrations/azure_assets/oauth2callback"
  end
end
