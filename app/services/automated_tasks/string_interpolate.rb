module AutomatedTasks
  class StringInterpolate
    include Utilities::Domains
    include EmailResponseHelper

    attr_accessor :object, :prepare_data_for

    MAX_LENGTH = 50000

    def initialize(object, is_email_service = false, recipient = nil, prepare_data_for: nil, default_survey: nil, recipient_cont_ids: [])
      @object = object
      @recipient = recipient
      @prepare_data_for = prepare_data_for
      @recipient_cont_ids = recipient_cont_ids
      @default_survey = default_survey
      @is_email_service = is_email_service
    end

    def call(text)
      parts = []
      text&.split("{").each do |part|
        break if prepare_data_for == 'email' && parts.join.length >= MAX_LENGTH

        if part =~ /^([\w\s]+)\}(.*)/
          name = $1
          idx = part.index("}") + 1
          remaining = part[idx..part.length]
          key = name.underscore.gsub(" ", "_").to_sym
          value = mappings[key]
          if value.present? || is_interpolation_key?(key)
            part = "#{value}#{remaining}"
          else
            part = "{#{name}}#{remaining}"
          end
        end
        parts << part
      end
      parts.join
    end

    def mappings
      @mappings ||= case object.class.name
                    when "HelpTicketComment" then help_ticket_comment_mappings
                    when "HelpTicket" then help_ticket_mappings
                    when "TimeSpent" then time_spent_mappings
                    when "ProjectTask" then project_task_mappings
                    when "CustomFormAttachment" then help_ticket_attachment_mappings
                    when "CustomFormValue" then custom_form_value_mappings
                    else
                      { }
                    end
    end

    def keys
      @keys ||= case object.class.name
                when "HelpTicketComment" then help_ticket_comment_keys
                when "HelpTicket" then help_ticket_keys
                when "TimeSpent" then time_spent_keys
                when "ProjectTask" then project_task_keys
                when "CustomAttachment" then help_ticket_attachment_keys
                when "CustomFormValue" then custom_form_value_keys
                when "CustomFormAttachment" then help_ticket_keys(object.help_ticket)
                else
                  [ ]
                end
    end

    def symbolized_snakecased_keys
      @symbolized_snakecased_keys ||= keys.map { |v| v.gsub(/\s/, '_').to_sym }
    end

    def is_interpolation_key?(key)
      symbolized_snakecased_keys.include?(key)
    end

    def help_ticket_keys(obj = object)
      help_ticket_mappings(obj).keys.map { |key| key.to_s.humanize(capitalize: false) }
    end

    def help_ticket_comment_keys
      help_ticket_comment_mappings.keys.map { |key| key.to_s.humanize(capitalize: false) }
    end

    def help_ticket_attachment_keys
      help_ticket_attachment_mappings.keys.map { |key| key.to_s.humanize(capitalize: false) }
    end

    def help_ticket_assignment_keys
      help_ticket_assignment_mappings.keys.map { |key| key.to_s.humanize(capitalize: false) }
    end

    def time_spent_keys
      time_spent_mappings.keys.map { |key| key.to_s.humanize(capitalize: false) }
    end

    def project_task_keys
      project_task_mappings.keys.map { |key| key.to_s.humanize(capitalize: false) }
    end

    def custom_form_value_keys
      custom_form_value_mappings.keys.map { |key| key.to_s.humanize(capitalize: false) }
    end

    def subject(obj)
      obj.custom_form_values.joins(:custom_form_field).find_by("custom_form_fields.name = ?", 'subject')&.value_str || "No subject provided"
    end

    def description(obj)
      obj.custom_form_values.joins(:custom_form_field).find_by("custom_form_fields.name = ?", 'description')&.value_str || "No description provided"
    end

    def creator(obj)
      @creator ||= begin
        value_int = obj.custom_form_values.joins(:custom_form_field).find_by("custom_form_fields.name = ?", 'created_by')&.value_int
        obj.company.company_users.find_by_cache(contributor_id: value_int)
      end
    end

    def help_ticket_mappings(obj = object)
      timezone = obj.workspace.business_hour.timezone || obj.company.timezone || "US/Central"
      @help_ticket_mapping ||= {}
      fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_id: obj.company.id, company_module: "helpdesk" })
      fields = fields.uniq { |f| [f.field_attribute_type, f.name] }

      help_ticket_facade = HelpTicketFacade.new(obj)
      fields.each do |f|
        field_value = help_ticket_facade.send(f.field_attribute_type, f.name).presence
        @help_ticket_mapping["ticket_#{f.name&.parameterize&.underscore}".to_sym] = field_value || "\"No #{f.name&.gsub('_', ' ')} provided\""
      end

      @help_ticket_mapping[:ticket_button] = ticket_button(obj)
      @help_ticket_mapping[:ticket_number] = obj.ticket_number
      @help_ticket_mapping[:ticket_created_at] = obj.created_at&.in_time_zone(timezone)&.strftime("%b %d, %Y at %I:%M%P")
      @help_ticket_mapping[:url] = url(obj)
      @help_ticket_mapping[:survey_url] = survey_url(obj) if default_survey_settings_exists?(obj)
      @help_ticket_mapping[:ticket_comments] = ticket_comments(obj).presence || "\"No comments provided\""
      @help_ticket_mapping[:ticket_created_by_first_name] = ticket_created_by_first_name(obj)
      @help_ticket_mapping[:company_name] = ticket_company_name(obj)
      @help_ticket_mapping
    end

    def ticket_created_by_first_name(obj)
      obj.creator_contributors&.map { |c| c.first_name || c.name }.join(',')
    end

    def ticket_company_name(obj)
      obj.company.name
    end

    def help_ticket_comment_mappings
      @help_ticket_mapping ||= {
        comment_body: comment_body,
        commenter: object.contributor&.name || object.email || "",
      }.merge(help_ticket_mappings(object.help_ticket))
    end

    def time_spent_mappings
      @time_spent_mapping ||= {
        time_spent: object.time_spent,
        creator: object.contributor.name || "",
        started: object.started_at || "",
      }.merge(help_ticket_mappings(object.help_ticket))
    end

    def custom_form_value_mappings
      @custom_form_value_mappings ||= create_custom_form_value_mappings
    end

    def create_custom_form_value_mappings
      mapping = {
        value: object.value_str || object.value_int,
        field_name: object.custom_form_field&.name,
      }

      addtl_mapping = case object.custom_form_field&.field_attribute_type
                      when "people_list" then { staff: object.contributor&.name }
                      when "asset_list" then { asset: object.managed_asset&.name }
                      when "contract_list" then { contract: object.contract&.name }
                      when "vendor_list" then { vendor: object.vendor&.name }
                      when "telecom_list" then { telecom: object.telecom_service&.name }
                      when "location_list" then { location: object.location&.name }
                      else
                        { }
                      end

      addtl_mapping.merge(help_ticket_mappings(object.module)).merge(mapping)
    end

    def project_task_mappings
      @project_task_mapping ||= {
        task_description: object.description || "",
        task_assignee: task_assignees,
        task_creator: object.contributor&.name || "",
      }.merge(help_ticket_mappings(object.help_ticket))
    end

    def help_ticket_attachment_mappings
      @help_ticket_attachment_mappings ||= {
        attachment_filename: object.file_name
      }.merge(help_ticket_mappings(object.help_ticket))
    end

    def task_assignees
      if object.task_assignees.blank?
        "not assigned"
      elsif object.task_assignees.length == 0
        object.task_assignees.first.contributor.name
      else
        object.task_assignees.first.contributor.name + " et al."
      end
    end

    def ticket_assignments(obj)
      ids = obj.custom_form_values.joins(:custom_form_field).where("custom_form_fields.name = ?", 'assigned_to').pluck(:value_int)
      contributors = []
      contributors = Contributor.where(id: ids).includes(company_user: :user) if ids.present?
      if contributors.blank?
        "not assigned"
      elsif contributors.count == 1
        contributors.first.name
      else
        "#{contributors.first.name} et al."
      end
    end

    def url(obj = object)
      url = ''
      if @is_email_service
        url = url_for_uninvited_staff(obj)
      else
        url = "#{Rails.application.routes.url_helpers.help_tickets_url(subdomain: obj.company.subdomain)}/#{obj.id}"
      end
      ActionController::Base.helpers.link_to url, url, :class => "btn-template"
    end

    def survey_url(obj = object)
      return "" if !obj.id

      url = ""
      # Checking if survey feature is enabled or disabled
      service_option = ServiceOption.find_by(service_name: "help_tickets/surveys", status: true)
      if service_option.present?
        url = Rails.application.routes.url_helpers.closing_survey_url(obj.id, subdomain: obj.company.subdomain)
      elsif @default_survey.present?
        url = Rails.application.routes.url_helpers.custom_survey_url(
          @default_survey.id,
          subdomain: obj.company.subdomain,
          ticket_id: obj.id
        )
      end
      ActionController::Base.helpers.link_to "Survey form", url, :class => "btn-template"
    end

    def ticket_button(obj = object)
      url = @is_email_service ? url_for_uninvited_staff(obj) : ticket_url(obj)
      ActionController::Base.helpers.link_to "Go to ticket", url, :class => "btn-template"
    end

    def group_members_contributor_ids(contributors, collected = [])
      contributors.each do |contributor|
        if !collected.include?(contributor.id)
          collected << contributor.id
          if contributor.group
            group_contributors = contributor.group.group_members.map(&:contributor)
            group_members_contributor_ids(group_contributors, collected)
          end
        end
      end
      collected
    end

    def get_ticket_comments(obj = object, rec = @recipient)
      recent_comments = []
      all_comments = obj.help_ticket_comments.order(created_at: :desc)
      user_id = User.find_by_cache(email: rec)
      contributor_id = CompanyUser.find_by(user_id: user_id)&.contributor_id
      all_comments.each do |comment|
        if recent_comments.length < 5
          if comment.private_contributor_ids.length > 0
            all_contributor_ids = group_members_contributor_ids(Contributor.where(id: comment.private_contributor_ids))
            if (prepare_data_for == "email" && all_private_recipients?(all_contributor_ids)) || 
                (contributor_id.present? && all_contributor_ids.include?(contributor_id))
              recent_comments << comment
            end
          else
            recent_comments << comment
          end
        else
          break
        end
      end
      recent_comments
    end

    def all_private_recipients?(contributor_ids)
      @recipient_cont_ids.length > 0 && (@recipient_cont_ids - contributor_ids).empty?
    end

    def ticket_comments(obj = object)
      message_body = ""
      timezone = obj.workspace.business_hour.timezone || obj.company.timezone || "US/Central"
      ticket_comments = get_ticket_comments(obj)
      ticket_comments.each_with_index do |item, index|
        body = """
          <table cellpadding='0' cellspacing='0' width='100%' style='padding: 10px;'>
            <tr>
              <td width='60' style='width: 60px; vertical-align: top;'>
                <table width='50' height='50' style='width: 50px; height: 50px; background-color: orange; color: white; text-align: center; vertical-align: middle; border-radius: 50%;'>
                  <tr>
                    <td style='height: 50px; width: 50px; background-color: orange; color: white; border-radius: 50%; text-align: center; display: table-cell; vertical-align: middle;'>
                      #{item.contributor && item.contributor.name.split.map(&:first).join.upcase}
                    </td>
                  </tr>
                </table>
              </td>
              <td width='10'></td>
              <td style='max-width: 430px; vertical-align: middle;'>
                <div>
                  #{item[:comment_body]}
                </div>
                <table width='100%'>
                  <tr>
                    <td style='font-size: 12px; color: #6C757D;'>
                      #{item.contributor&.name}, #{item[:created_at].in_time_zone(timezone).strftime("%d %b %I:%M %p").to_s}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        """
        if index != ticket_comments.size - 1
          body += "<hr style='color: #ced4da'>"
        end
        message_body += body
      end
      message_body
    end

    def ticket_url(obj)
      user = User.find_by_cache(email: @recipient)
      if user && CompanyUser.access_granted.find_by_cache(user_id: user.id, company_id: obj.company&.id)
        "#{build_company_url(obj.company)}help_tickets/#{obj.id}"
      else
        url_for_uninvited_staff(obj)
      end
    end

    def scheduled_comment_url(obj)
      "#{build_company_url(obj.company)}help_tickets/#{obj.id}"
    end

    def url_for_uninvited_staff(obj)
      "#{build_company_url(obj.company)}preview_help_tickets/#{obj.guid}"
    end

    def comment_body
      if prepare_data_for == 'email' && object.comment_body.length > MAX_LENGTH
        if object.source == 'email'
          doc = Nokogiri::HTML(object.comment_body)
          object.comment_body = remove_replies(doc, 20).to_html
          if object.comment_body.length > MAX_LENGTH
            object.comment_body = Nokogiri::HTML(object.comment_body[0..MAX_LENGTH]).to_html
          end
        else
          object.comment_body = object.comment_body[0..MAX_LENGTH]
        end
      end

      if object.resolution_flag.present?
        body = object.comment_body
        body += "<div style='font-size: 0.875rem; margin-top: 0.2rem'> Ticket has been marked as <b> Closed </b> </div>"
        body
      else
        object.comment_body || ""
      end
    end

    def default_survey_settings_exists?(obj)
      obj.workspace.company_mailers.includes(:default_mailer).find_by(default_mailers: { event: "survey_sent" }) if obj.workspace.present?
    end
  end
end
