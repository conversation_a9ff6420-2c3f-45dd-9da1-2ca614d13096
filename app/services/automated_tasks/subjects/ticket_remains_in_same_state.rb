module AutomatedTasks
  module Subjects
    class TicketRemainsInSameState
      include AutomatedTasks::Concerns::Eventing
      extend ::NewRelic::Agent::MethodTracer

      def match?(cfv)
        self.class.trace_execution_scoped(['Custom/same_state_tasks/match/first']) do
          # AT trigger on cfv updation, task trigger, one trigger was of time, shouldn't proceed further if trigger time is not passed
          return false if is_task_empty?(cfv)

          return false unless pending_tasks(cfv).present?
        end

        minute_difference = ((Time.current - cfv.updated_at).abs) / 60
        hours_difference = minute_difference / 60
        is_time_exceeded = hours_difference >= value['time_interval'].to_i

        return false unless is_time_exceeded

        ticket_retained_same_status = AutomatedTasks::Subjects::TicketFormField.new(detail).match?(cfv)

        self.class.trace_execution_scoped(['Custom/same_state_tasks/match/second']) do
          if ticket_retained_same_status
            update_scheduled_at(cfv, 'executed')
            expire_related_scheduled_tasks(cfv)
            true
          else
            update_scheduled_at(cfv, 'expired')
            false
          end
        end
      end

      def update_scheduled_at(cfv, status)
        scheduled_at = pending_tasks(cfv).first
        if scheduled_at.present?
          scheduled_at.status = status
          scheduled_at.save
        end
      end

      def is_task_empty?(cfv)
        pending_tasks(cfv).find { |task| task.trigger_at < DateTime.now.utc }.nil?
      end

      def expire_related_scheduled_tasks(cfv)
        pending_tasks(cfv).update_all(status: 'expired')
      end
    end
  end
end
