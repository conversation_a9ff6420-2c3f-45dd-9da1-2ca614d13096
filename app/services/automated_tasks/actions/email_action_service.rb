module AutomatedTasks
  module Actions
    class EmailActionService
      attr_accessor :root, :workspace, :action, :recipients, :old_survey

      def initialize(root, action, recipients)
        self.root = root
        self.action = action
        self.recipients = recipients
        self.workspace = action.automated_task.workspace
      end

      def call
        execution_date_id = if root.is_a?(AutomatedTasks::ExecutionDate)
                              AutomatedTasks::ExecutionDate.find_by(
                                date: root.date,
                                company_id: root.company_id,
                                automated_task: action.automated_task_id
                              )&.id
                            else
                              root.id
                            end
        data = JSON.parse(action.value)
        data['body'] = replace_survey_url(data['body'])
        data['body'] = add_survey_templates(data['body'])

        retries = 0
        begin
          email_data = email_data(data, true, recipients)
          AutomatedTasks::TaskActionMailerWorker.perform_async(
            action.id, execution_date_id, root.class.name, recipients, email_data
          )
        rescue EOFError => e
          if retries <= 3
            sleep(5)
            retries += 1
            retry
          end
        end
      end

      def should_add_attachments?(body)
        @should_add_attachments ||= body.include?('{ticket_attachments}') && !should_send_attachment_link?
      end

      def should_send_attachment_link?
        self.workspace
            .helpdesk_settings
            .joins(:default_helpdesk_setting)
            .find_by("default_helpdesk_settings.setting_type = 'allow_the_attachment_links_in_email'")
            .enabled?
      end

      def email_data(data, is_email_service = false, recipients = [])
        user_ids = User.where(email: recipients).pluck(:id)
        contributor_ids = CompanyUser.where(company_id: workspace.company.id, user_id: user_ids).pluck(:contributor_id).uniq
        interpolated_string = AutomatedTasks::StringInterpolate.new(root, is_email_service, nil, prepare_data_for: 'email', default_survey: old_survey, recipient_cont_ids: contributor_ids)
        email_body = interpolated_string.call(data['body']).gsub('<div><!--block-->', '').gsub('</div>', '')
        subject = truncate_subject(interpolated_string.call(data['subject']))
        add_attachments = should_add_attachments?(data['body'])

        { 
          email_body: email_body,
          subject: subject,
          should_add_attachments: add_attachments,
          custom_form_attachments: add_attachments ? custom_form_attachments : [],
          attachment_size: @attachment_size,
          from_email: from_email,
          sanitize_email_body: sanitize_email_body(email_body),
          helpdesk_custom_emails: helpdesk_custom_emails
        }.as_json
      end

      def custom_form_attachments
        @custom_form_attachments ||= begin
          attachments = []
          @attachment_size = 0.0
          help_ticket.custom_form_values.includes(:custom_form_field)
                     .where(custom_form_fields: { name: 'attachments' })
                     .each do |cfv|
            attachment = cfv.attachment.attachment.blob
            attachments << {"file_name": attachment.filename.to_s, "file_url": attachment.url}
            @attachment_size += attachment.byte_size
          end
          attachments
        end
      end
    
      def help_ticket
        @help_ticket ||= if root.is_a?(HelpTicket)
                            root
                         elsif root.respond_to?(:help_ticket)
                            root.help_ticket
                         else
                            nil
                         end
      end

      def sanitize_email_body(html_content)
        @sanitize_email_body ||= begin
          document = Nokogiri::HTML::DocumentFragment.parse(html_content)
        
          document.css('*[style]').each do |node|
            sanitized_styles = node['style'].split(';').map do |style|
              style = style.strip
              # Updated regex to check for four identical values followed by a distinct fifth value
              if style =~ /padding:\s*((\d+(\.\d+)?(pt|px|rem|in)\s*){4})\s*(\d+(\.\d+)?(pt|px|rem|in))/
                'padding: 0;'  # Replace with default valid shorthand value
              elsif style =~ /margin:\s*((\d+(\.\d+)?(pt|px|rem|in)\s*){4})\s*(\d+(\.\d+)?(pt|px|rem|in))/
                'margin: 0;'
                # Fix spacing issues within units like '2 pt' by converting to '2pt'
              elsif style =~ /(\d+)\s+(pt|px|in|rem)\b/
                style.gsub(/(\d+)\s+(pt|px|in|rem)\b/, '\1\2')
              else
                style
              end
            end.join('; ')
        
            node['style'] = sanitized_styles 
          end
        
          document.to_html
        end
      end
    
      def default_from_email
        helpdesk_form&.email
      end
    
      def helpdesk_form
        root&.custom_form&.helpdesk_custom_form
      end
    
      def helpdesk_custom_email
        helpdesk_form&.helpdesk_custom_email
      end
    
      def valid_custom_email?
        helpdesk_custom_email&.verified? && helpdesk_custom_email.email.present? && helpdesk_custom_email.email !~ /^(nil|null)\@/
      end
    
      def valid_default_email?
        default_from_email.present? && default_from_email !~ /^(nil|null)\@/
      end
    
      def from_email
        from = "helpdesk@#{workspace.company.subdomain}.#{Rails.application.credentials.root_domain}"
        if valid_custom_email?
          from = helpdesk_custom_email.name ? "#{helpdesk_custom_email.name} <#{helpdesk_custom_email.email}>" : helpdesk_custom_email.email
        elsif valid_default_email?
          from = helpdesk_custom_email&.name ? "#{helpdesk_custom_email.name} <#{default_from_email}>" : default_from_email
        end
        from
      end

      def helpdesk_custom_emails
        company_id = root.is_a?(AutomatedTasks::ExecutionDate) ? root.company_id : help_ticket.company_id
        @custom_emails ||= HelpdeskCustomEmail.where(company_id: company_id)&.pluck(:email).compact
      end

      def truncate_subject(subject)
        subject.length > 250 ? "#{subject[0..249]}..." : subject
      end

      def replace_survey_url(body)
        if body.include?('{survey_url}') && !collect_closing_survey_enabled
          body.gsub('{survey_url}', 'Sorry, survey is not available for this ticket.')
        else
          body
        end
      end

      def add_survey_templates(email_body)
        # Checking if survey feature is enabled or disabled
        service_option = ServiceOption.find_by(service_name: "help_tickets/surveys", status: true)
        return email_body if service_option.present?

        ticket = if root.is_a?(HelpTicket)
                   root
                 elsif root.respond_to?('help_ticket') && root&.help_ticket
                   root.help_ticket
                 elsif root.is_a?(CustomFormAttachment)
                   root.custom_form_value.help_ticket
                 end

        return email_body unless ticket.present?

        custom_survey_ids = email_body.scan(%r{/custom_surveys/(\d+)})&.flatten&.map(&:to_i)
        custom_survey_ids&.each do |id|
          email_body = email_body.gsub("/custom_surveys/#{id}", "/custom_surveys/#{id}?ticket_id=#{ticket.id}")
        end

        worspace_surveys = ticket.workspace.custom_surveys

        if ticket.present? && email_body.include?('{survey_url}') && collect_closing_survey_enabled
          default_survey = worspace_surveys.find_by(is_default: true)
          if default_survey.present?
            self.old_survey = default_survey
            default_responses = []
            recipients_cont_ids(ticket).each do |id|
              default_responses << create_template_response(id, ticket, default_survey)
            end
            CustomSurvey::Response.insert_all(default_responses) if default_responses.present?
          end
        end

        if custom_survey_ids.present? && ticket.present?
          custom_responses = []
          worspace_surveys.where(id: custom_survey_ids).each do |survey|
            recipients_cont_ids(ticket).each do |cont_id|
              custom_responses << create_template_response(cont_id, ticket, survey)
            end
          end
          CustomSurvey::Response.insert_all(custom_responses) if custom_responses.present?
        end
        email_body
      end

      def create_template_response(cont_id, ticket, survey)
        {
          name: survey.title,
          status: "unopened",
          comment: [],
          score: nil,
          contributor_id: cont_id,
          custom_survey_id: survey.id,
          company_id: ticket.company_id,
          workspace_id: ticket.workspace_id,
          help_ticket_id: ticket.id
        }
      end

      def recipients_cont_ids(ticket)
        @recipients_cont_ids ||= CompanyUser.where(company_id: ticket.company_id).joins(:user).where(users: { email: recipients }).pluck(:contributor_id).uniq
      end

      def collect_closing_survey_enabled
        @collect_closing_survey_enabled ||= root.custom_form.helpdesk_custom_form.collect_closing_survey
      end
    end
  end
end
