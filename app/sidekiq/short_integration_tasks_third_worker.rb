class ShortIntegrationTasksThirdWorker
  include Sidekiq::Worker
  include RakeTaskExecutor
  sidekiq_options queue: 'integrations'

  def perform
    execute_rake_task("integrations:gsuite_data_sync")
    execute_rake_task("integrations:microsoft_async")
    execute_rake_task("integrations:sage_accounting_data_sync")
    execute_rake_task("integrations:kaseya_async_daily")
    execute_rake_task("integrations:send_errors_mailer")
    execute_rake_task("integrations:gsuite_ad_data_sync")
    execute_rake_task("integrations:sync_aws_cloud_data")
    execute_rake_task("integrations:mosyle_async")
  end
end
