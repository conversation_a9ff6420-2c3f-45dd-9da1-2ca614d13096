class ModuleAlertSendEmailsWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'low_intensity_schedule'

  def perform
    level = Rails.logger.level
    Rails.logger = ActiveSupport::Logger.new('log/module_alert_send_emails.log')
    Rails.logger.level = level
    ActiveRecord::Base.logger = Rails.logger
    started_at = Time.now.strftime("%Y-%m-%d %H:%M:%S")
    cron_lock = nil
    begin
      cron_lock = CronLock.create!(key: "module_alert:send_emails-#{started_at}")
    rescue ActiveRecord::RecordNotUnique => e
      Rails.logger.warn("Duplicate execution of module_alert:send_emails-#{Time.now.strftime("%Y-%m-%d %H:%M:%S")}")
      return
    end

    Rails.logger.info("Started module_alert:send_emails-#{started_at}")
    Company.not_sample.includes(module_alert_notifications: { module_alert: :monitorable }).find_each do |company|
      notifications = company.module_alert_notifications
                      .awaiting_delivery
                      .includes(module_alert: :monitorable)
                      .where('alert_date >= ? AND alert_date <= ?', Date.today.beginning_of_month, Date.today.end_of_month)
      if notifications.present?
        company.admin_company_users.each do |company_user|
          ModuleAlertSummaryMailer.send_summary(company_user, notifications).deliver_now
        end

        company.module_alert_notifications.awaiting_delivery.each do |notification|
          notification.update(email_sent_at: Time.zone.now)
        end
      end
    end

    cron_lock&.touch
    Rails.logger.info("Completed module_alert:send_emails-#{started_at}")
  end
end
