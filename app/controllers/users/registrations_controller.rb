class Users::RegistrationsController < Devise::RegistrationsController
  include PgTools
  include ActiveRecord::SecureToken
  include AssignAdminsToSampleCompany
  require 'utilities/domains'
  prepend_before_action :require_no_authentication, only: [:new, :create, :cancel, :update]
  prepend_before_action :authenticate_scope!, only: [:edit, :destroy]
  layout 'homepage'

  # GET /resource/sign_up
  def new
    Devise.sign_out_all_scopes ? sign_out : sign_out(resource_name)
    redirect_to new_user_about_you_path(host: secure_host, email: params[:email])
  end

  # POST /resource
  def create
    email = user_params[:email].strip.downcase
    resource = resource_class.find_by_email(email)

    # Then we'll check if user is present.
    if resource.present?
      flash[:warning] = "The email address was already confirmed, you can log in to your account."
      render json: { confirmed: false, message: "The email address was already confirmed, you can log in to your account." }, status: :conflict
    elsif User.generic_email?(email)
      flash[:warning] = "Please enter your work email with your company's domain"
      render json: { confirmed: false, message: "Please enter your work email with your company's domain" }, status: :bad_request
    else
      user = nil
      company = nil
      resource_updated = nil
      url = nil
      Company.transaction do
        build_resource(user_params)
        user = self.resource
        company = Company.new(company_params)
        company.verified = is_united_states_company?
        company = company.set_up_self(user)

        user.generate_authentication_token!
        user.confirmation_token = SecureRandom.hex(32)
        user.email = email

        yield user if block_given?
        if user.save(validate: false)
          user_form = company.custom_forms.find_by(form_name: "Teammates")
          Rails.logger.info("User form: #{user_form}")
          company_user = user.company_users.create(company: company,
                                                   granted_access_at: is_united_states_company? ? DateTime.current : nil,
                                                   custom_form_id: user_form.id)

          groups = company.groups.where(name: ['Admins', 'Help Desk Agents'], default: true).index_by(&:name)

          GroupMember.create!(skip_callbacks: true, skip_field_permissions: true, contributor_id: company_user.contributor_id, group_id: groups["Admins"].id)
          GroupMember.create!(skip_callbacks: true, skip_field_permissions: true, contributor_id: company_user.contributor_id, group_id: groups["Help Desk Agents"].id)

          create_expanded_privileges(company_user.contributor)
          add_access_to_sample_company(company) if should_add_sample_company?
          if @sample_company && is_sample_company_user(user)
            subdomain = @sample_company.subdomain
          else
            subdomain = company.subdomain
          end

          user_access = UserAccess.create!(super_admin_guid: session[:super_admin_guid],
                                           company_user: company_user,
                                           redirect_to: "/dashboard")

          url = user_access_url(id: user_access.auth_token,
                                domain: root_domain,
                                subdomain: subdomain)
        end
      rescue Exception => e
        Company.connection.execute "ROLLBACK"
        raise e
      end

      if user && company
        resource_updated = sign_up(resource_name, user)

        # Send the appropriate mailers
        SendConfirmationEmailJob.set(wait: 1.minute).perform_later(user.id, company.id)
        SendInfoEmailJob.set(wait: 5.minutes).perform_later(user)
        SendFreeOnboardingSupportEmailJob.set(wait_until: 1.day.from_now).perform_later(user)
        SendApprovalPendingEmailJob.set(wait: 2.minutes).perform_later(company.id) unless is_united_states_company?

        # Save any tracking data they came here with
        company.create_company_tracking(cookies)

        # Create sample company and discovered services
        company.run_setup_workers(request)

        # Check referral
        if params[:referred_by].present?
          referred_by = User.find_by_cache(id: params[:referred_by])
          new_referral = Referral.find_or_create_by(referrer_id: referred_by&.id, referrer_email: referred_by&.email, referred_email: user.email)
          new_referral.referred_id = user.id
          new_referral.save!
          Referrals::ReferredSignedUpMailerWorker.perform_async(referred_by&.id, user&.full_name || user.email)
        end

        if params[:tracking_id].present?
          mixpanel_service.alias(user&.guid, params[:tracking_id])
        end
        if Rails.env.production? && !ServiceOption.find_by(service_name: 'hubspot_integration').status
          HubspotIntegrationWorker.perform_async(params['user'].as_json, params['company'].as_json, request.remote_ip)
        end
        render json: { success: resource_updated, url: url, company: company, user: user.attributes.merge(full_name: user.full_name) }, status: :ok
      else
        render json: { confirmed: false, success: false, message: e }, status: :unprocessable_entity
      end
    end
  rescue Exception => e
    CognitoService.new(user).delete_cognito_user if user&.guid && user&.id
    Rails.logger.error(e)

    render json: { confirmed: false, success: false, message: e }, status: :unprocessable_entity
  end

  # GET /resource/edit
  def edit
    render :edit
  end

  # PUT /resource
  # We need to use a copy of the resource because we don't want to change
  # the current user in place.
  def update
    resource = resource_class.find_by_email(user_params[:email])
    if user_params[:email].present? && resource.password.blank?
      resource.assign_attributes(user_params)

      # we'll update local user if cognito user creates.
      resource_updated = sign_up_user(resource)
    else
      resource_updated = update_resource(resource, account_update_params)
    end
    yield resource if block_given?
    clean_up_passwords resource
    if resource_updated
      sign_up(resource_name, resource)
      render json: { user: resource }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  # DELETE /resource
  def destroy
    resource.destroy
    Devise.sign_out_all_scopes ? sign_out : sign_out(resource_name)
    set_flash_message :notice, :destroyed if is_flashing_format?
    yield resource if block_given?
    respond_with_navigational(resource) { redirect_to after_sign_out_path_for(resource_name) }
  end

  # GET /resource/cancel
  # Forces the session data which is usually expired after sign
  # in to be expired now. This is useful if the user wants to
  # cancel oauth signing in/up in the middle of the process,
  # removing all OAuth session data.
  def cancel
    expire_data_after_sign_in!
    redirect_to new_registration_path(resource_name)
  end

  def sync_with_hubspot
    if Rails.env.production? && !ServiceOption.find_by(service_name: 'hubspot_integration').status
      HubspotIntegrationWorker.perform_async(params['user'].as_json, nil, request.remote_ip)
    end
  end

  protected

  def require_no_authentication
    redirect_to new_user_registration_url(host: secure_host) and return unless is_secure_subdomain?
    super
  end

  def update_needs_confirmation?(resource, previous)
    resource.respond_to?(:pending_reconfirmation?) &&
      resource.pending_reconfirmation? &&
      previous != resource.unconfirmed_email
  end

  # By default we want to require a password checks on update.
  # You can overwrite this method in your own RegistrationsController.
  def update_resource(resource, params)
    resource.update_with_password(params)
  end

  # Build a devise resource passing in the session. Useful to move
  # temporary session data to the newly created user.
  def build_resource(hash=nil)
    self.resource = resource_class.new_with_session(hash || {}, session)
  end

  # Signs in a user on sign up. You can overwrite this method in your own
  # RegistrationsController.
  def sign_up(resource_name, resource)
    resource_updated = sign_up_user(resource)
    sign_in(resource_name, resource)
    resource_updated
  end

  # The path used after sign up. You need to overwrite this method
  # in your own RegistrationsController.
  def after_sign_up_path_for(resource)
    after_sign_in_path_for(resource)
  end

  # The default url to be used after updating a resource. You need to overwrite
  # this method in your own RegistrationsController.
  def after_update_path_for(resource)
    signed_in_root_path(resource)
  end

  # Authenticates the current scope and gets the current resource from the session.
  def authenticate_scope!
    send(:"authenticate_#{resource_name}!", force: true)
    self.resource = send(:"current_#{resource_name}")
  end

  def sign_up_params
    devise_parameter_sanitizer.sanitize(:sign_up)
  end

  def account_update_params
    devise_parameter_sanitizer.sanitize(:account_update)
  end

  def translation_scope
    'devise.registrations'
  end

  private

  def is_united_states_company?
    return true unless Rails.application.credentials.trigger_companies_manual_approval.eql?('true')

    if @is_united_states_company.nil?
      country = get_country(request.remote_ip)
      @is_united_states_company = country == "United States"
    end
    @is_united_states_company
  end

  def get_country(ip)
    response = Net::HTTP.get(URI.parse("https://www.iplocate.io/api/lookup/#{ip}"))
    JSON.parse(response)['country']
  end

  def add_access_to_sample_company company
    @sample_company ||= Company.find_by_cache(is_sample_company: true)
    admins_cu_ids = company.admin_company_users.ids
    if @sample_company && admins_cu_ids
      assign_admins(admins_cu_ids)
    end
  end

  def create_admin_user(resource)
    resource.save!
    cookies[:client_subdomain] = resouce.company.subdomain
  end

  def create_expanded_privileges(contributor)
    ExpandedPrivileges::Populate.new(contributor).call
  end

  def user_params
    params.require(:user).permit(
      :first_name,
      :last_name,
      :full_name,
      :email,
      :timezone,
      :password,
      :terms_of_services
    )
  end

  def company_params
      params.require(:company).permit(
        :name,
        :subdomain,
        :email,
        :logo,
        :logo_url,
        :logo_filename,
        :logo_image_data,
        :timezone,
        :url,
        :default_logo_url,
        :original_logo_url
      )
  end

  def sign_up_user resource
    if resp = CognitoService.new(resource).sign_up_user
      resource_updated = resource.save
      user_sub = resp.user.attributes.select{|item| item[:name] == "sub"}[0].value
      resource.update_columns(guid: user_sub, encrypted_password: nil)
      resource_updated
    end
  end

  def should_add_sample_company?
    !Rails.env.test? &&
    Rails.application.credentials.sample_company[:parent_id].present? &&
    Company.find_by_cache(id: Rails.application.credentials.sample_company[:parent_id]).present? &&
    is_united_states_company?
  end
end
