class Integrations::AzureAssets::Config < ApplicationRecord
  include CompanyCache

  self.table_name = 'azure_assets_configs'
  attr_accessor :skip_callbacks, :company_user_id

  belongs_to :company

  has_one :company_integration, as: :integrable, dependent: :destroy

  validates :token, presence: true
  validates_uniqueness_of :company_id, message: "azure assets is already integrated"

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: Proc.new{ |app| app.skip_callbacks }
  before_destroy :destroy_integration_data

  def create_comp_intg_and_execute_job
    azure_assets_int = Integration.find_by(name: 'azure_assets')
    comp_intg = CompanyIntegration.find_or_initialize_by(company_id: company_id,
                                                         integrable_type: 'Integrations::AzureAssets::Config',
                                                         integrable_id: id,
                                                         integration_id: azure_assets_int.id)

    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!

    Integrations::AzureAssets::SyncDataWorker.perform_async(id, true, false, company_user_id)
  end

  def destroy_integration_data
    self.company.discovered_assets.azure.where.not(status: :imported).destroy_all
  end
end
