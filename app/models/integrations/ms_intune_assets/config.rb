class Integrations::MsIntuneAssets::Config < ApplicationRecord
  include Company<PERSON>ache

  self.table_name = "ms_intune_assets_configs"
  attr_accessor :skip_callbacks, :company_user_id

  belongs_to :company
  has_one :company_integration, as: :integrable, dependent: :destroy

  validates :token, presence: true
  validates_uniqueness_of :company_id, message: 'intune already integrated'

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: -> { skip_callbacks }
  before_destroy :destroy_integration_data

  def create_comp_intg_and_execute_job
    intune_integration = Integration.find_by(name: "ms_intune_assets")
    comp_intg = CompanyIntegration.find_or_initialize_by(
      company_id: company_id,
      integrable_type: "Integrations::MsIntuneAssets::Config",
      integrable_id: id,
      integration_id: intune_integration.id
    )

    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!
    Integrations::MsIntuneAssets::SyncDataWorker.perform_async(id, true, false, company_user_id)
  end

  def destroy_integration_data
    self.company.discovered_assets.ms_intune.where(status: ["incomplete", "ignored", "ready_for_import"]).destroy_all
  end
end
