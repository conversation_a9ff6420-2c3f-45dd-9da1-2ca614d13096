class Integrations::AwsAssets::Config < ApplicationRecord
  include CompanyCache
  include DiscoveryToolsLogs

  self.table_name = "aws_assets_configs"
  attr_accessor :skip_callbacks, :company_user_id

  belongs_to :company
  has_one :company_integration, as: :integrable, dependent: :destroy

  validates_presence_of :access_key, :secret_key, :regions
  validate :verify_keys

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: Proc.new { |app| app.skip_callbacks }
  after_update :create_credentials_update_log
  after_rollback :create_api_logs, :unless => lambda { errors.empty? }
  before_destroy :destroy_integration_data

  def verify_keys
    @aws_assets_service = Integrations::AwsAssets::FetchData.new(self)
    response = @aws_assets_service.authenticate?
    errors.add :base, response[1] unless response[0]
  end

  def create_comp_intg_and_execute_job
    intg = Integration.find_by(name: "aws_assets")
    comp_intg = CompanyIntegration.find_or_initialize_by(
      integrable: self,
      integration_id: intg.id,
      company_id: company.id
    )
    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!
    Integrations::AwsAssets::SyncDataWorker.perform_async(self.id, true, false, company_user_id, 0)
  end

  protected

  def create_api_logs
    if @aws_assets_service
      @aws_assets_service.api_logs.each do |log|
        LogCreationWorker.perform_async("Logs::ApiEvent", log.to_json)
      end
    end
  end

  def destroy_integration_data
    self.company.discovered_assets.aws.where.not(status: :imported).destroy_all
  end

  def create_credentials_update_log
    if self.saved_changes?
      change_log = []
      if self.saved_changes.key?(:access_key)
        change_log << element_data("Access Key", mask_value(self.saved_changes[:access_key]&.first), mask_value(self.saved_changes[:access_key]&.last))
      end
      if self.saved_changes.key?(:secret_key)
        change_log << element_data("Secret Key", mask_value(self.saved_changes[:secret_key]&.first), mask_value(self.saved_changes[:secret_key]&.last))
      end
      if self.saved_changes.key?(:regions)
        change_log << element_data("Regions", self.saved_changes[:regions]&.first, self.saved_changes[:regions]&.last)
      end
      create_asset_connector_log(:credentials_updated, :aws, change_log, :successful, company_user_id, company_id) unless change_log.empty?
    end
  end
end
