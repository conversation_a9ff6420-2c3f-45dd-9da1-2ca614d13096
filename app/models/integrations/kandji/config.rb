class Integrations::Kandji::Config < ApplicationRecord
  include CompanyCache
  include DiscoveryToolsLogs

  self.table_name = 'kandji_configs'
  attr_accessor :skip_callbacks, :company_user_id

  belongs_to :company
  has_one :company_integration, as: :integrable, dependent: :destroy

  validates_presence_of :api_url, :api_token
  validates_uniqueness_of :company_id, message: 'kandji already integrated.'
  validate :verify_keys

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: Proc.new { |app| app.skip_callbacks }
  after_update :create_credentials_update_log
  # after_create :send_slack_alert, if: Proc.new { Rails.env.production? }
  before_destroy :destroy_integration_data

  def verify_keys
    unless Integrations::Kandji::FetchData.new(self).authenticate?
      errors.add :base, "API URL or API Token is invalid."
    end
  end

  def create_comp_intg_and_execute_job
    intg = Integration.find_by(name: 'kandji')
    comp_intg = CompanyIntegration.find_or_initialize_by(
      integrable: self,
      integration_id: intg.id,
      company_id: company.id
    )
    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!
    Integrations::Kandji::SyncDataWorker.perform_async(id, true, false, company_user_id)
  end

  # TODO: need to remove this
  def send_slack_alert
    Slack.configure do |config|
      config.token = Rails.application.credentials.slack[:api_token]
    end

    client = ::Slack::Web::Client.new
    client.auth_test
    message_body = 'A new Kandji config has been added.'
    client.chat_postMessage(channel: "#support", text: message_body)
  end

  def destroy_integration_data
    self.company.discovered_assets.kandji.where.not(status: :imported).destroy_all
  end

  def create_credentials_update_log
    if self.saved_changes?
      change_log = []
      if self.saved_changes.key?(:api_url)
        change_log << element_data("API URL", self.saved_changes[:api_url]&.first, self.saved_changes[:api_url]&.last)
      end
      if self.saved_changes.key?(:api_token)
        change_log << element_data("API Token", mask_value(self.saved_changes[:api_token]&.first), mask_value(self.saved_changes[:api_token]&.last))
      end
      create_asset_connector_log(:credentials_updated, :aws_assets, change_log, :successful, company_user_id, company_id) unless change_log.empty?
    end
  end
end
