class Integrations::JamfPro::Config < ApplicationRecord
  include CompanyCache
  include Discovery<PERSON>oolsLogs

  self.table_name = "jamf_pro_configs"
  attr_accessor :skip_callbacks, :company_user_id

  belongs_to :company
  has_one :company_integration, as: :integrable, dependent: :destroy

  validates :token, :username, :password, :instance_name, presence: true
  validates_uniqueness_of :company_id, message: 'Jamf Pro already integrated'

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: -> { skip_callbacks }
  after_update :create_credentials_update_log
  before_destroy :destroy_integration_data

  def create_comp_intg_and_execute_job
    jamf_pro_integration = Integration.find_by(name: "jamf_pro")
    comp_intg = CompanyIntegration.find_or_initialize_by(
      company_id: company_id,
      integrable_type: "Integrations::JamfPro::Config",
      integrable_id: id,
      integration_id: jamf_pro_integration.id
    )

    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!
    Integrations::JamfPro::SyncDataWorker.perform_async(id, true, false, company_user_id)
  end

  def destroy_integration_data
    self.company.discovered_assets.jamf_pro.where.not(status: :imported).destroy_all
  end

  def create_credentials_update_log
    if self.saved_changes?
      change_log = []
      if self.saved_changes.key?(:username)
        change_log << element_data("Username", self.saved_changes[:username]&.first, self.saved_changes[:username]&.last)
      end
      if self.saved_changes.key?(:password)
        change_log << element_data("Password", mask_value(self.saved_changes[:password]&.first), mask_value(self.saved_changes[:password]&.last))
      end
      if self.saved_changes.key?(:instance_name)
        change_log << element_data("Instance Name", self.saved_changes[:instance_name]&.first, self.saved_changes[:instance_name]&.last)
      end
      create_asset_connector_log(:credentials_updated, :aws_assets, change_log, :successful, company_user_id, company_id) unless change_log.empty?
    end
  end
end
