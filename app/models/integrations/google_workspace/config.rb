class Integrations::GoogleWorkspace::Config < ApplicationRecord
  self.table_name = "google_workspace_configs"
  attr_accessor :skip_callbacks

  belongs_to :company
  has_one :company_integration, as: :integrable, dependent: :destroy

  validates :token, presence: true
  validates_uniqueness_of :company_id, message: 'google workspace already integrated'
  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: -> { skip_callbacks }
  before_destroy :destroy_integration_data

  def create_comp_intg_and_execute_job
    intg = Integration.find_by(name: 'google_workspace')
    comp_intg = CompanyIntegration.find_or_initialize_by(
      company_id: company_id,
      integrable_type: "Integrations::GoogleWorkspace::Config",
      integrable_id: id,
      integration_id: intg.id
    )

    comp_intg.assign_attributes(status: true, sync_status: :pending)
    comp_intg.save!
    Integrations::GoogleWorkspace::SyncDataWorker.perform_async(id, true, false)
  end

  def destroy_integration_data
    self.company.discovered_assets.google_workspace.where(status: ["incomplete", "ignored", "ready_for_import"]).destroy_all
  end
end
